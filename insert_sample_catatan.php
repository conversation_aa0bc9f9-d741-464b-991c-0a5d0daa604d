<?php
/**
 * Script untuk menambahkan data catatan contoh langsung via SQL
 */

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>Menambahkan Data Catatan Contoh</h2>";
    
    $db = new Database();
    
    // Cek siswa yang ada
    $siswa = $db->fetchAll("SELECT id_siswa, nama_lengkap FROM siswa LIMIT 3");
    
    if (empty($siswa)) {
        echo "<p style='color: red;'>❌ Tidak ada data siswa</p>";
        exit;
    }
    
    echo "<p>Siswa yang ditemukan:</p>";
    foreach ($siswa as $s) {
        echo "<li>{$s['nama_lengkap']} (ID: {$s['id_siswa']})</li>";
    }
    
    // Hapus data catatan lama untuk testing
    echo "<p>Menghapus data catatan lama...</p>";
    $db->query("DELETE FROM catatan_siswa WHERE jenis_catatan IN ('bk_prestasi', 'bk_pelanggaran', 'bk_konseling')");
    
    // Data catatan contoh
    $catatanData = [
        // Prestasi untuk siswa pertama
        [
            'siswa_id' => $siswa[0]['id_siswa'],
            'jenis_catatan' => 'bk_prestasi',
            'judul_catatan' => 'Juara 1 Lomba Matematika',
            'isi_catatan' => 'Siswa berhasil meraih juara 1 dalam lomba matematika tingkat kabupaten. Menunjukkan kemampuan analisis yang sangat baik.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-5 days')),
            'tingkat_prioritas' => 'tinggi',
            'status_catatan' => 'aktif',
            'created_by' => 1
        ],
        [
            'siswa_id' => $siswa[0]['id_siswa'],
            'jenis_catatan' => 'bk_prestasi',
            'judul_catatan' => 'Prestasi Olahraga Basket',
            'isi_catatan' => 'Terpilih menjadi kapten tim basket sekolah dan berhasil membawa tim ke final kompetisi antar sekolah.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-10 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'created_by' => 1
        ],
        
        // Pelanggaran untuk siswa kedua
        [
            'siswa_id' => $siswa[1]['id_siswa'],
            'jenis_catatan' => 'bk_pelanggaran',
            'judul_catatan' => 'Terlambat Masuk Kelas',
            'isi_catatan' => 'Siswa terlambat masuk kelas sebanyak 3 kali dalam seminggu. Perlu pembinaan tentang kedisiplinan waktu.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-3 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Diberikan teguran lisan dan diminta membuat surat pernyataan tidak akan mengulangi.',
            'created_by' => 1
        ],
        [
            'siswa_id' => $siswa[1]['id_siswa'],
            'jenis_catatan' => 'bk_pelanggaran',
            'judul_catatan' => 'Tidak Mengerjakan Tugas',
            'isi_catatan' => 'Siswa tidak mengerjakan tugas matematika dan bahasa Indonesia. Sudah diberikan peringatan oleh guru mata pelajaran.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-7 days')),
            'tingkat_prioritas' => 'rendah',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Diminta mengerjakan tugas pengganti dan konseling dengan guru BK.',
            'created_by' => 1
        ],
        
        // Konseling untuk siswa ketiga
        [
            'siswa_id' => $siswa[2]['id_siswa'],
            'jenis_catatan' => 'bk_konseling',
            'judul_catatan' => 'Konseling Masalah Keluarga',
            'isi_catatan' => 'Siswa mengalami masalah di rumah yang mempengaruhi konsentrasi belajar. Perlu pendampingan khusus.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-2 days')),
            'tingkat_prioritas' => 'tinggi',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Dijadwalkan sesi konseling rutin setiap minggu dan koordinasi dengan orang tua.',
            'created_by' => 1
        ],
        
        // Tambahan untuk siswa pertama
        [
            'siswa_id' => $siswa[0]['id_siswa'],
            'jenis_catatan' => 'bk_konseling',
            'judul_catatan' => 'Konseling Karir',
            'isi_catatan' => 'Siswa meminta bimbingan untuk memilih jurusan kuliah yang sesuai dengan minat dan bakatnya.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-1 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'created_by' => 1
        ]
    ];
    
    // Insert data
    $berhasil = 0;
    $gagal = 0;
    
    foreach ($catatanData as $data) {
        try {
            $sql = "INSERT INTO catatan_siswa (
                siswa_id, jenis_catatan, judul_catatan, isi_catatan,
                tanggal_catatan, tingkat_prioritas, status_catatan,
                tindak_lanjut, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['siswa_id'],
                $data['jenis_catatan'],
                $data['judul_catatan'],
                $data['isi_catatan'],
                $data['tanggal_catatan'],
                $data['tingkat_prioritas'],
                $data['status_catatan'],
                $data['tindak_lanjut'] ?? null,
                $data['created_by']
            ];
            
            $result = $db->query($sql, $params);
            
            if ($result) {
                echo "<p style='color: green;'>✅ {$data['judul_catatan']} - {$siswa[array_search($data['siswa_id'], array_column($siswa, 'id_siswa'))]['nama_lengkap']}</p>";
                $berhasil++;
            } else {
                echo "<p style='color: red;'>❌ Gagal: {$data['judul_catatan']}</p>";
                $gagal++;
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: {$data['judul_catatan']} - {$e->getMessage()}</p>";
            $gagal++;
        }
    }
    
    echo "<h3>Hasil:</h3>";
    echo "<p>Berhasil: <strong style='color: green;'>{$berhasil}</strong></p>";
    echo "<p>Gagal: <strong style='color: red;'>{$gagal}</strong></p>";
    
    // Tampilkan statistik
    echo "<h3>Statistik per Siswa:</h3>";
    
    require_once __DIR__ . '/app/models/CatatanSiswa.php';
    $catatanModel = new CatatanSiswa();
    
    foreach ($siswa as $s) {
        $stats = $catatanModel->getDetailedStatistics($s['id_siswa']);
        
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>{$s['nama_lengkap']}</h4>";
        echo "<ul>";
        echo "<li>Total Catatan: <strong>{$stats['total_catatan']}</strong></li>";
        echo "<li>Prestasi: <strong style='color: green;'>{$stats['total_prestasi']}</strong></li>";
        echo "<li>Pelanggaran: <strong style='color: red;'>{$stats['total_pelanggaran']}</strong></li>";
        echo "<li>Konseling: <strong style='color: blue;'>{$stats['total_konseling']}</strong></li>";
        echo "<li>Total Bimbingan: <strong>{$stats['total_bimbingan']}</strong></li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h4>Langkah selanjutnya:</h4>";
    echo "<ol>";
    echo "<li>Akses halaman detail siswa</li>";
    echo "<li>Klik tab 'Catatan Siswa'</li>";
    echo "<li>Lihat statistik prestasi dan pelanggaran yang sudah muncul</li>";
    echo "<li>Cek accordion untuk melihat detail catatan per kategori</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
