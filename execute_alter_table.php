<?php
/**
 * Execute ALTER TABLE for ENUM
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=siswa_app', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>⚠️ Execute ALTER TABLE for ENUM</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
    echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ Warning</h4>";
    echo "<p>This will modify the database structure. Make sure you have a backup!</p>";
    echo "</div>";
    
    $alterSql = "ALTER TABLE berkas MODIFY COLUMN jenis_berkas ENUM(
        'kartu_keluarga',
        'akta_lahir',
        'rapor_kelas_x',
        'rapor_kelas_xi', 
        'rapor_kelas_xii',
        'ijazah_sd',
        'ijazah_smp',
        'ijazah_sma',
        'foto_siswa',
        'surat_keterangan_sehat',
        'surat_kelakuan_baik',
        'surat_peringatan_1',
        'surat_peringatan_2',
        'surat_peringatan_3',
        'surat_panggilan_ortu',
        'piagam_prestasi',
        'sertifikat_lomba',
        'penghargaan_akademik',
        'lainnya'
    ) NOT NULL";
    
    echo "<h3>Executing ALTER TABLE...</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<code>" . htmlspecialchars($alterSql) . "</code>";
    echo "</div>";
    
    // Execute the ALTER TABLE
    $stmt = $pdo->prepare($alterSql);
    $result = $stmt->execute();
    
    echo "<p style='color: green;'>✅ ALTER TABLE executed successfully!</p>";
    
    echo "<hr>";
    echo "<h3>Now updating specific rapor types:</h3>";
    
    // Now update with specific rapor types
    $updates = [
        16 => 'rapor_kelas_x',
        17 => 'rapor_kelas_xi', 
        18 => 'rapor_kelas_xii'
    ];
    
    foreach ($updates as $id => $jenis) {
        $stmt = $pdo->prepare("UPDATE berkas SET jenis_berkas = ? WHERE id = ?");
        $result = $stmt->execute([$jenis, $id]);
        echo "<p>✅ Updated ID $id to '$jenis' - Affected rows: " . $stmt->rowCount() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>Final Verification:</h3>";
    
    // Check final results
    $stmt = $pdo->prepare("SELECT id, jenis_berkas, nama_file_sistem FROM berkas WHERE siswa_id = 57 ORDER BY id");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($results as $row) {
        echo "<p><strong>ID " . $row['id'] . ":</strong> ";
        echo "<code>" . htmlspecialchars($row['jenis_berkas']) . "</code> - ";
        echo htmlspecialchars($row['nama_file_sistem']);
        echo "</p>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Database Structure Updated!</h4>";
    echo "<p>ENUM berhasil diupdate untuk mendukung kategori berkas yang lebih spesifik.</p>";
    echo "<p>Sekarang berkas dapat dikelompokkan dengan lebih detail.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Possible causes:</p>";
    echo "<ul>";
    echo "<li>Database connection failed</li>";
    echo "<li>Table is locked</li>";
    echo "<li>Insufficient privileges</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='test_berkas_grouped.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Grouped Display</a> ";
echo "<a href='public/siswa/detail/57' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👁️ Detail Siswa</a>";
echo "</p>";
?>
