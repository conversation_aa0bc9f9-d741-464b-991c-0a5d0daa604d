<?php
/**
 * Setup Sample Students for Pagination Testing
 * Script ini akan menambahkan banyak data siswa untuk testing pagination
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/Siswa.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/helpers/AcademicYear.php';

echo "<h1>🧪 Setup Sample Students for Pagination Testing</h1>";

try {
    $db = new Database();
    $siswaModel = new Siswa();
    $kelasModel = new Kelas();
    
    echo "<h2>📋 Current System Status</h2>";
    
    // Check current academic year
    $currentYear = AcademicYear::getCurrentAcademicYear();
    echo "<p style='color: green;'>✅ Current Academic Year: <strong>{$currentYear}</strong></p>";
    
    // Get available classes for current year
    $availableClasses = $db->fetchAll("
        SELECT id, nama_kelas, tingkat 
        FROM kelas 
        WHERE tahun_pelajaran = ? AND is_active = 1 AND tingkat > 0
        ORDER BY tingkat, nama_kelas
    ", [$currentYear]);
    
    echo "<p style='color: blue;'>ℹ️ Available Classes: " . count($availableClasses) . " classes</p>";
    foreach ($availableClasses as $kelas) {
        echo "<p style='margin-left: 20px;'>🏫 {$kelas['nama_kelas']} (Tingkat {$kelas['tingkat']})</p>";
    }
    
    if (empty($availableClasses)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ No Active Classes Found</h4>";
        echo "<p>Please create some classes first before adding students.</p>";
        echo "<p><a href='/siswa-app/public/kelas' target='_blank'>Go to Class Management</a></p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>👥 Adding Sample Students</h2>";
    
    // Sample student names untuk sekolah khusus laki-laki (Indonesian male names)
    $namaDepan = [
        'Ahmad', 'Budi', 'Eko', 'Galih', 'Ivan', 'Oki', 'Udin', 'Wawan', 'Yudi', 'Andi',
        'Candra', 'Doni', 'Erick', 'Hadi', 'Indra', 'Kevin', 'Niko', 'Rizki', 'Fajar', 'Bayu',
        'Dani', 'Arif', 'Bambang', 'Cahyo', 'Dimas', 'Eko', 'Fauzi', 'Gilang', 'Hendra', 'Irfan',
        'Joko', 'Krisna', 'Lukman', 'Maulana', 'Nugroho', 'Oscar', 'Pandu', 'Qomar', 'Reza', 'Satria'
    ];
    
    // Nama belakang untuk sekolah khusus laki-laki - hapus nama yang umumnya untuk perempuan
    $namaBelakang = [
        'Pratama', 'Gunawan', 'Setiawan', 'Prasetyo', 'Santoso', 'Kusuma', 'Wijaya', 'Handoko',
        'Nugroho', 'Kurniawan', 'Susanto', 'Firmansyah', 'Budiman', 'Hermawan', 'Ramadhan',
        'Saputra', 'Putra', 'Utama', 'Jaya', 'Budi', 'Adi', 'Rizki', 'Fajar', 'Bayu', 'Dani'
    ];

    // Sekolah khusus laki-laki - hanya 'L'
    $jenisKelamin = ['L'];
    $agama = ['Islam', 'Kristen', 'Katolik', 'Hindu', 'Buddha'];
    $statusSiswa = ['aktif'];
    
    // Generate students
    $totalStudentsToAdd = 75; // This will test pagination well
    $addedStudents = 0;
    $skippedStudents = 0;
    
    echo "<p>Target: Add {$totalStudentsToAdd} students...</p>";
    
    for ($i = 1; $i <= $totalStudentsToAdd; $i++) {
        // Generate random data
        $namaLengkap = $namaDepan[array_rand($namaDepan)] . ' ' . $namaBelakang[array_rand($namaBelakang)];
        $nis = '2024' . str_pad($i + 400, 3, '0', STR_PAD_LEFT); // Start from 2024401
        $jenisKelaminRandom = $jenisKelamin[array_rand($jenisKelamin)];
        $agamaRandom = $agama[array_rand($agama)];
        $kelasRandom = $availableClasses[array_rand($availableClasses)];
        
        // Check if NIS already exists
        $existingStudent = $db->fetch("SELECT id_siswa FROM siswa WHERE nis = ?", [$nis]);
        if ($existingStudent) {
            $skippedStudents++;
            echo "<p style='color: orange;'>⚠️ Skipped {$namaLengkap} (NIS {$nis} already exists)</p>";
            continue;
        }
        
        try {
            // Insert student
            $result = $db->query("
                INSERT INTO siswa (
                    nis, nama_lengkap, jenis_kelamin, agama, status_siswa, kelas_id,
                    tempat_lahir, tanggal_lahir, alamat, no_telepon, email,
                    nama_ayah, nama_ibu, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ", [
                $nis,
                $namaLengkap,
                $jenisKelaminRandom,
                $agamaRandom,
                'aktif',
                $kelasRandom['id'],
                'Jakarta', // Default tempat lahir
                '2008-01-01', // Default tanggal lahir
                'Jl. Contoh No. ' . $i . ', Jakarta', // Default alamat
                '08123456' . str_pad($i, 3, '0', STR_PAD_LEFT), // Default telepon
                strtolower(str_replace(' ', '.', $namaLengkap)) . '@email.com', // Default email
                'Ayah ' . $namaLengkap, // Default nama ayah
                'Ibu ' . $namaLengkap, // Default nama ibu
            ]);
            
            $addedStudents++;
            echo "<p style='color: green;'>✅ Added: {$namaLengkap} (NIS: {$nis}) - Class: {$kelasRandom['nama_kelas']}</p>";
            
            // Add some delay to avoid overwhelming the output
            if ($i % 10 == 0) {
                echo "<p style='color: blue;'>📊 Progress: {$i}/{$totalStudentsToAdd} students processed...</p>";
            }
            
        } catch (Exception $e) {
            $skippedStudents++;
            echo "<p style='color: red;'>❌ Failed to add {$namaLengkap}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📊 Final Statistics</h2>";
    
    // Get updated statistics
    $totalStudents = $db->fetch("
        SELECT COUNT(*) as total 
        FROM siswa s 
        JOIN kelas k ON s.kelas_id = k.id 
        WHERE k.tahun_pelajaran = ?
    ", [$currentYear]);
    
    $studentsPerClass = $db->fetchAll("
        SELECT k.nama_kelas, COUNT(s.id_siswa) as jumlah_siswa
        FROM kelas k
        LEFT JOIN siswa s ON k.id = s.kelas_id
        WHERE k.tahun_pelajaran = ? AND k.is_active = 1 AND k.tingkat > 0
        GROUP BY k.id, k.nama_kelas
        ORDER BY k.tingkat, k.nama_kelas
    ", [$currentYear]);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Operation Complete</h4>";
    echo "<ul>";
    echo "<li><strong>Students Added:</strong> {$addedStudents}</li>";
    echo "<li><strong>Students Skipped:</strong> {$skippedStudents}</li>";
    echo "<li><strong>Total Students in {$currentYear}:</strong> " . ($totalStudents['total'] ?? 0) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Students per Class:</h3>";
    foreach ($studentsPerClass as $classData) {
        echo "<p style='margin-left: 20px;'>🏫 {$classData['nama_kelas']}: {$classData['jumlah_siswa']} students</p>";
    }
    
    echo "<h2>🧪 Pagination Testing</h2>";
    
    $totalForPagination = $totalStudents['total'] ?? 0;
    $pagesAt10PerPage = ceil($totalForPagination / 10);
    $pagesAt25PerPage = ceil($totalForPagination / 25);
    $pagesAt50PerPage = ceil($totalForPagination / 50);
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📄 Pagination Preview</h4>";
    echo "<ul>";
    echo "<li><strong>Total Students:</strong> {$totalForPagination}</li>";
    echo "<li><strong>Pages at 10 per page:</strong> {$pagesAt10PerPage} pages</li>";
    echo "<li><strong>Pages at 25 per page:</strong> {$pagesAt25PerPage} pages</li>";
    echo "<li><strong>Pages at 50 per page:</strong> {$pagesAt50PerPage} pages</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<div class='d-grid gap-2' style='max-width: 400px;'>";
    echo "<a href='/siswa-app/public/siswa' target='_blank' style='display: inline-block; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;'>";
    echo "🔗 Test Pagination - Default (10 per page)";
    echo "</a>";
    
    echo "<a href='/siswa-app/public/siswa?per_page=25' target='_blank' style='display: inline-block; padding: 10px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; text-align: center; margin-top: 5px;'>";
    echo "🔗 Test Pagination - 25 per page";
    echo "</a>";
    
    echo "<a href='/siswa-app/public/siswa?per_page=50' target='_blank' style='display: inline-block; padding: 10px 15px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px; text-align: center; margin-top: 5px;'>";
    echo "🔗 Test Pagination - 50 per page";
    echo "</a>";
    
    echo "<a href='/siswa-app/public/siswa?page=2' target='_blank' style='display: inline-block; padding: 10px 15px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px; text-align: center; margin-top: 5px;'>";
    echo "🔗 Test Pagination - Page 2";
    echo "</a>";
    echo "</div>";
    
    echo "<h2>✅ Setup Complete</h2>";
    echo "<p style='color: green; font-weight: bold;'>";
    echo "Sample students have been added successfully! You can now test the pagination functionality.";
    echo "</p>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎯 What You Can Test Now:</h4>";
    echo "<ul>";
    echo "<li><strong>Pagination Navigation:</strong> Click through different pages</li>";
    echo "<li><strong>Records Per Page:</strong> Change the number of records displayed</li>";
    echo "<li><strong>Responsive Design:</strong> Test on different screen sizes</li>";
    echo "<li><strong>URL Parameters:</strong> Direct page access via URL</li>";
    echo "<li><strong>Academic Year Filter:</strong> Switch between academic years</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
