<?php
/**
 * Test Rapor KPP and KPA Display
 */

require_once 'app/models/Database.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/User.php';
require_once 'app/models/Siswa.php';
require_once 'app/models/Berkas.php';

// Initialize session manager
$sessionManager = new SimpleSessionManager();
$userModel = new User();
$siswaModel = new Siswa();
$berkasModel = new Berkas();

echo "<h2>📊 Test Rapor KPP dan KPA</h2>";

// Login sebagai admin
$result = $userModel->authenticate('admin', 'admin123');

if ($result['success']) {
    $sessionManager->loginUser($result['user']);
    echo "<p style='color: green;'>✅ Login berhasil sebagai: " . htmlspecialchars($result['user']['nama_lengkap']) . "</p>";
    
    // Get sample siswa
    $siswaList = $siswaModel->getAll();
    if (!empty($siswaList)) {
        $siswa = $siswaList[0];
        $siswaId = $siswa['id_siswa'] ?? $siswa['id'];
        
        echo "<p>📚 Testing dengan siswa: <strong>" . htmlspecialchars($siswa['nama_lengkap']) . "</strong> (ID: $siswaId)</p>";
        
        // Get berkas
        $berkas = $berkasModel->getBySiswaId($siswaId);
        
        if (!empty($berkas)) {
            echo "<h3>📋 Updated File Categories</h3>";
            $fileCategories = $berkasModel->getFileCategories();
            
            // Show Rapor category specifically
            if (isset($fileCategories['Rapor'])) {
                echo "<div style='border: 2px solid #28a745; border-radius: 10px; padding: 15px; margin: 15px 0; background: rgba(40, 167, 69, 0.05);'>";
                echo "<h4 style='color: #28a745; margin: 0 0 10px 0;'>📊 Kategori Rapor (Updated)</h4>";
                
                foreach ($fileCategories['Rapor'] as $typeKey => $typeName) {
                    $isNew = in_array($typeKey, ['rapor_kpp', 'rapor_kpa']);
                    $badge = $isNew ? " <span style='background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.7em;'>BARU</span>" : "";
                    echo "<span style='background: #f8f9fa; padding: 5px 10px; margin: 3px; border-radius: 5px; display: inline-block; border: 1px solid #dee2e6;'>$typeName$badge</span> ";
                }
                echo "</div>";
            }
            
            echo "<h3>📁 Berkas Rapor yang Tersedia</h3>";
            
            // Filter only rapor berkas
            $raporBerkas = array_filter($berkas, function($file) {
                return strpos($file['jenis_berkas'], 'rapor') === 0;
            });
            
            if (!empty($raporBerkas)) {
                echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; margin: 20px 0;'>";
                
                foreach ($raporBerkas as $file) {
                    $isNew = in_array($file['jenis_berkas'], ['rapor_kpp', 'rapor_kpa']);
                    $borderColor = $isNew ? '#dc3545' : '#28a745';
                    $bgColor = $isNew ? 'rgba(220, 53, 69, 0.1)' : 'rgba(40, 167, 69, 0.05)';
                    
                    echo "<div style='border: 2px solid $borderColor; border-radius: 8px; padding: 15px; background: $bgColor;'>";
                    
                    // File icon
                    echo "<div style='text-align: center; margin-bottom: 10px;'>";
                    echo "<span style='font-size: 2em;'>📊</span>";
                    if ($isNew) {
                        echo "<br><span style='background: #dc3545; color: white; padding: 2px 8px; border-radius: 10px; font-size: 0.7em; font-weight: bold;'>BARU</span>";
                    }
                    echo "</div>";
                    
                    // File info
                    $displayNames = [
                        'rapor_kelas_x' => 'Rapor Kelas X',
                        'rapor_kelas_xi' => 'Rapor Kelas XI',
                        'rapor_kelas_xii' => 'Rapor Kelas XII',
                        'rapor_kpp' => 'Rapor KPP',
                        'rapor_kpa' => 'Rapor KPA'
                    ];
                    
                    $displayName = $displayNames[$file['jenis_berkas']] ?? $file['jenis_berkas'];
                    
                    echo "<h5 style='margin: 0 0 8px 0; text-align: center; color: $borderColor;'>$displayName</h5>";
                    
                    if (!empty($file['keterangan'])) {
                        echo "<p style='font-size: 0.85em; color: #6c757d; text-align: center; margin: 5px 0;'>";
                        echo "ℹ️ " . htmlspecialchars($file['keterangan']);
                        echo "</p>";
                    }
                    
                    // Action buttons
                    echo "<div style='display: flex; gap: 5px; margin-top: 10px;'>";
                    echo "<a href='/siswa-app/public/" . htmlspecialchars($file['file_path']) . "' target='_blank' style='flex: 1; background: #007bff; color: white; padding: 6px 10px; text-decoration: none; border-radius: 4px; font-size: 0.8em; text-align: center;'>👁️ Lihat</a>";
                    echo "<a href='/siswa-app/public/berkas/download/" . $file['id'] . "' style='flex: 1; background: #28a745; color: white; padding: 6px 10px; text-decoration: none; border-radius: 4px; font-size: 0.8em; text-align: center;'>⬇️ Download</a>";
                    echo "</div>";
                    
                    echo "</div>";
                }
                
                echo "</div>";
            } else {
                echo "<p style='color: orange;'>⚠️ Belum ada berkas rapor yang diupload</p>";
            }
            
            echo "<h3>📤 Test Upload Modal</h3>";
            echo "<p>Sekarang modal upload akan menampilkan opsi Rapor KPP dan KPA:</p>";
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b3d9ff;'>";
            echo "<h5>Opsi Rapor di Modal Upload:</h5>";
            echo "<ul>";
            echo "<li>📊 Rapor Kelas X</li>";
            echo "<li>📊 Rapor Kelas XI</li>";
            echo "<li>📊 Rapor Kelas XII</li>";
            echo "<li>📊 <strong>Rapor KPP</strong> (Baru)</li>";
            echo "<li>📊 <strong>Rapor KPA</strong> (Baru)</li>";
            echo "</ul>";
            echo "</div>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada berkas untuk testing</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Tidak ada data siswa untuk testing</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Login gagal: " . htmlspecialchars($result['message']) . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Summary Penambahan Rapor KPP dan KPA</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724; margin-top: 0;'>✅ Rapor KPP dan KPA Berhasil Ditambahkan!</h4>";

echo "<p><strong>Perubahan yang Dilakukan:</strong></p>";
echo "<ol>";
echo "<li>✅ <strong>Model Berkas:</strong> Tambah rapor_kpp dan rapor_kpa ke kategori</li>";
echo "<li>✅ <strong>Database ENUM:</strong> Update ENUM untuk mendukung nilai baru</li>";
echo "<li>✅ <strong>Display Names:</strong> Tambah nama tampilan yang user-friendly</li>";
echo "<li>✅ <strong>Upload Modal:</strong> Opsi baru tersedia di dropdown</li>";
echo "<li>✅ <strong>Grouping:</strong> Otomatis masuk ke kategori Rapor</li>";
echo "</ol>";

echo "<p><strong>Kategori Rapor Lengkap:</strong></p>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;'>";

$raporTypes = [
    'rapor_kelas_x' => 'Rapor Kelas X',
    'rapor_kelas_xi' => 'Rapor Kelas XI', 
    'rapor_kelas_xii' => 'Rapor Kelas XII',
    'rapor_kpp' => 'Rapor KPP',
    'rapor_kpa' => 'Rapor KPA'
];

foreach ($raporTypes as $key => $name) {
    $isNew = in_array($key, ['rapor_kpp', 'rapor_kpa']);
    $bgColor = $isNew ? '#fff3cd' : '#d1ecf1';
    $textColor = $isNew ? '#856404' : '#0c5460';
    $badge = $isNew ? " 🆕" : "";
    
    echo "<div style='background: $bgColor; padding: 10px; border-radius: 5px; text-align: center; color: $textColor; font-weight: bold;'>";
    echo "📊 $name$badge";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "<h3>📋 Penjelasan KPP dan KPA:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b3d9ff;'>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";

echo "<div>";
echo "<h5 style='color: #0056b3; margin-top: 0;'>📊 KPP (Kompetensi Pengetahuan dan Praktik)</h5>";
echo "<ul style='margin: 0;'>";
echo "<li>Rapor yang menilai kompetensi pengetahuan siswa</li>";
echo "<li>Berisi nilai-nilai mata pelajaran akademik</li>";
echo "<li>Fokus pada aspek kognitif dan praktik</li>";
echo "<li>Evaluasi pemahaman konsep dan teori</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h5 style='color: #0056b3; margin-top: 0;'>📊 KPA (Kompetensi Pengetahuan dan Aplikasi)</h5>";
echo "<ul style='margin: 0;'>";
echo "<li>Rapor yang menilai kemampuan aplikasi pengetahuan</li>";
echo "<li>Berisi penilaian penerapan ilmu dalam praktik</li>";
echo "<li>Fokus pada aspek aplikatif dan implementasi</li>";
echo "<li>Evaluasi kemampuan problem solving</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='public/siswa/detail/$siswaId' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👁️ Lihat Detail Siswa</a> ";
echo "<a href='public/upload/berkas/$siswaId' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📤 Test Upload</a> ";
echo "<a href='public/' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Dashboard</a>";
echo "</p>";
?>
