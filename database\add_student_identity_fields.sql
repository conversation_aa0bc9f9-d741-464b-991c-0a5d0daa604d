-- <PERSON><PERSON>t untuk menambahkan field identitas siswa baru
-- File: database/add_student_identity_fields.sql

-- Menambahkan field identitas baru ke tabel siswa
ALTER TABLE siswa 
ADD COLUMN nik VARCHAR(16) UNIQUE AFTER nisn,
ADD COLUMN no_kk VARCHAR(16) AFTER nik,
ADD COLUMN asal_sekolah VARCHAR(100) AFTER pekerjaan_ibu,
ADD COLUMN asal_paroki VARCHAR(100) AFTER asal_sekolah,
ADD COLUMN golongan_darah ENUM('A', 'B', 'AB', 'O', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') AFTER asal_paroki;

-- Menambahkan index untuk field yang sering dicari
ALTER TABLE siswa 
ADD INDEX idx_nik (nik),
ADD INDEX idx_no_kk (no_kk),
ADD INDEX idx_asal_sekolah (asal_sekolah),
ADD INDEX idx_asal_paroki (asal_paroki);

-- Menambahkan komentar untuk dokumentasi
ALTER TABLE siswa 
MODIFY COLUMN nik VARCHAR(16) UNIQUE COMMENT 'Nomor Induk Kependudukan',
MODIFY COLUMN no_kk VARCHAR(16) COMMENT 'Nomor Kartu Keluarga',
MODIFY COLUMN asal_sekolah VARCHAR(100) COMMENT 'Sekolah asal sebelum masuk',
MODIFY COLUMN asal_paroki VARCHAR(100) COMMENT 'Paroki asal siswa',
MODIFY COLUMN golongan_darah ENUM('A', 'B', 'AB', 'O', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') COMMENT 'Golongan darah siswa';
