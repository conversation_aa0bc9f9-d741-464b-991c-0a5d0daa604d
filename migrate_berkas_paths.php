<?php
/**
 * Migrate Berkas File Paths
 * Update database records to use correct file paths in public/uploads
 */

require_once 'app/models/Database.php';
require_once 'app/models/Berkas.php';

echo "<h2>🔄 Migrate Berkas File Paths</h2>";

try {
    $db = new Database();
    $berkasModel = new Berkas();
    
    // Get all berkas records
    $allBerkas = $db->fetchAll("SELECT * FROM berkas ORDER BY id");
    
    echo "<p>📁 Found " . count($allBerkas) . " berkas records to check</p>";
    
    $updated = 0;
    $errors = 0;
    
    foreach ($allBerkas as $berkas) {
        $currentPath = $berkas['file_path'];
        $berkasId = $berkas['id'];
        $jenisberkas = $berkas['jenis_berkas'];
        $namaFileSistem = $berkas['nama_file_sistem'];
        
        // Generate correct path using model method
        $correctPath = $berkasModel->getFullFilePath($jenisberkas, $namaFileSistem);
        
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<strong>Berkas ID:</strong> $berkasId<br>";
        echo "<strong>Jenis:</strong> $jenisberkas<br>";
        echo "<strong>File:</strong> $namaFileSistem<br>";
        echo "<strong>Current Path:</strong> <code>$currentPath</code><br>";
        echo "<strong>Correct Path:</strong> <code>$correctPath</code><br>";
        
        if ($currentPath !== $correctPath) {
            // Check if physical file exists in new location
            $physicalPath = __DIR__ . '/public/' . $correctPath;
            
            if (file_exists($physicalPath)) {
                // Update database record
                $sql = "UPDATE berkas SET file_path = ? WHERE id = ?";
                $db->query($sql, [$correctPath, $berkasId]);
                
                echo "<span style='color: green;'>✅ Updated database record</span><br>";
                $updated++;
            } else {
                echo "<span style='color: red;'>❌ Physical file not found: $physicalPath</span><br>";
                $errors++;
            }
        } else {
            echo "<span style='color: blue;'>ℹ️ Path already correct</span><br>";
        }
        
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<h3>📊 Migration Summary</h3>";
    echo "<p><strong>Total Records:</strong> " . count($allBerkas) . "</p>";
    echo "<p><strong>Updated:</strong> <span style='color: green;'>$updated</span></p>";
    echo "<p><strong>Errors:</strong> <span style='color: red;'>$errors</span></p>";
    echo "<p><strong>Already Correct:</strong> <span style='color: blue;'>" . (count($allBerkas) - $updated - $errors) . "</span></p>";
    
    if ($updated > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Migration Completed Successfully!</h4>";
        echo "<p>Database records have been updated to use the correct file paths.</p>";
        echo "<p>Files are now accessible via web browser.</p>";
        echo "</div>";
    }
    
    // Test file access
    echo "<h3>🔍 Test File Access</h3>";
    $testBerkas = $db->fetchAll("SELECT * FROM berkas LIMIT 3");
    
    foreach ($testBerkas as $berkas) {
        $testUrl = "http://localhost/siswa-app/public/" . $berkas['file_path'];
        echo "<p><strong>Test URL:</strong> <a href='$testUrl' target='_blank'>$testUrl</a></p>";
        
        // Test with curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            echo "<span style='color: green;'>✅ File accessible (HTTP 200)</span><br>";
        } else {
            echo "<span style='color: red;'>❌ File not accessible (HTTP $httpCode)</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>📋 Next Steps</h3>";
echo "<ol>";
echo "<li>✅ Files moved to <code>public/uploads/berkas/</code></li>";
echo "<li>✅ Database paths updated</li>";
echo "<li>✅ Berkas model updated to use new path</li>";
echo "<li>🔄 Test file access from detail siswa page</li>";
echo "</ol>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<a href='public/siswa' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👥 Daftar Siswa</a> ";
echo "<a href='public/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Dashboard</a>";
echo "</p>";
?>
