<?php
/**
 * Debug Script untuk Data Siswa
 * Script ini akan membantu debug mengapa data siswa tidak tampil
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/Siswa.php';
require_once __DIR__ . '/app/helpers/AcademicYear.php';
require_once __DIR__ . '/app/helpers/Security.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 Debug Data Siswa</h1>";

try {
    $db = new Database();
    $siswaModel = new Siswa();
    
    echo "<h2>📋 System Information</h2>";
    
    // Check current academic year
    $currentYear = AcademicYear::getCurrentAcademicYear();
    $selectedYear = AcademicYear::getSelectedAcademicYear();
    
    echo "<p><strong>Current Academic Year:</strong> {$currentYear}</p>";
    echo "<p><strong>Selected Academic Year:</strong> {$selectedYear}</p>";
    
    // Check session data
    echo "<h3>Session Data:</h3>";
    if (isset($_SESSION['user'])) {
        echo "<p><strong>User ID:</strong> " . ($_SESSION['user']['id'] ?? 'Not set') . "</p>";
        echo "<p><strong>Username:</strong> " . ($_SESSION['user']['username'] ?? 'Not set') . "</p>";
        echo "<p><strong>Role:</strong> " . ($_SESSION['user']['role'] ?? 'Not set') . "</p>";
        echo "<p><strong>User Role (legacy):</strong> " . ($_SESSION['user_role'] ?? 'Not set') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ No user session found</p>";
    }
    
    echo "<h2>🗄️ Database Analysis</h2>";
    
    // Check total students in database
    $totalStudents = $db->fetch("SELECT COUNT(*) as total FROM siswa");
    echo "<p><strong>Total Students in Database:</strong> " . ($totalStudents['total'] ?? 0) . "</p>";
    
    // Check students by status
    $activeStudents = $db->fetch("SELECT COUNT(*) as total FROM siswa WHERE status_siswa = 'aktif'");
    echo "<p><strong>Active Students:</strong> " . ($activeStudents['total'] ?? 0) . "</p>";
    
    // Check students with kelas
    $studentsWithKelas = $db->fetch("
        SELECT COUNT(*) as total 
        FROM siswa s 
        JOIN kelas k ON s.kelas_id = k.id 
        WHERE s.status_siswa = 'aktif'
    ");
    echo "<p><strong>Active Students with Kelas:</strong> " . ($studentsWithKelas['total'] ?? 0) . "</p>";
    
    // Check students by academic year
    $studentsByYear = $db->fetchAll("
        SELECT k.tahun_pelajaran, COUNT(s.id_siswa) as total
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        WHERE s.status_siswa = 'aktif'
        GROUP BY k.tahun_pelajaran
        ORDER BY k.tahun_pelajaran DESC
    ");
    
    echo "<h3>Students by Academic Year:</h3>";
    foreach ($studentsByYear as $yearData) {
        $year = $yearData['tahun_pelajaran'] ?? 'NULL';
        $total = $yearData['total'] ?? 0;
        $isCurrent = ($year === $currentYear) ? ' (Current)' : '';
        $isSelected = ($year === $selectedYear) ? ' (Selected)' : '';
        echo "<p>📅 {$year}{$isCurrent}{$isSelected}: {$total} students</p>";
    }
    
    // Check kelas data
    echo "<h3>Kelas Information:</h3>";
    $kelasData = $db->fetchAll("
        SELECT tahun_pelajaran, COUNT(*) as total_kelas, 
               SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_kelas
        FROM kelas 
        WHERE tingkat > 0
        GROUP BY tahun_pelajaran
        ORDER BY tahun_pelajaran DESC
    ");
    
    foreach ($kelasData as $kelas) {
        $year = $kelas['tahun_pelajaran'] ?? 'NULL';
        $total = $kelas['total_kelas'] ?? 0;
        $active = $kelas['active_kelas'] ?? 0;
        echo "<p>🏫 {$year}: {$total} total classes ({$active} active)</p>";
    }
    
    echo "<h2>🧪 Testing Queries</h2>";
    
    // Test the exact query used in getAllPaginated
    echo "<h3>Testing getAllPaginated Query:</h3>";
    try {
        $testQuery = "
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif' AND k.tahun_pelajaran = ?
            ORDER BY k.tingkat, s.nama_lengkap
            LIMIT 10 OFFSET 0
        ";
        
        echo "<p><strong>Query:</strong></p>";
        echo "<pre>" . htmlspecialchars($testQuery) . "</pre>";
        echo "<p><strong>Parameters:</strong> ['{$selectedYear}']</p>";
        
        $testResults = $db->fetchAll($testQuery, [$selectedYear]);
        echo "<p><strong>Results:</strong> " . count($testResults) . " records found</p>";
        
        if (!empty($testResults)) {
            echo "<h4>Sample Results:</h4>";
            foreach (array_slice($testResults, 0, 3) as $student) {
                echo "<p>👤 {$student['nama_lengkap']} (NIS: {$student['nis']}) - Class: {$student['nama_kelas']}</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query Error: " . $e->getMessage() . "</p>";
    }
    
    // Test getTotalCount query
    echo "<h3>Testing getTotalCount Query:</h3>";
    try {
        $countQuery = "
            SELECT COUNT(*) as total
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif' AND k.tahun_pelajaran = ?
        ";
        
        echo "<p><strong>Query:</strong></p>";
        echo "<pre>" . htmlspecialchars($countQuery) . "</pre>";
        echo "<p><strong>Parameters:</strong> ['{$selectedYear}']</p>";
        
        $countResult = $db->fetch($countQuery, [$selectedYear]);
        $totalCount = (int)($countResult['total'] ?? 0);
        echo "<p><strong>Total Count:</strong> {$totalCount}</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Count Query Error: " . $e->getMessage() . "</p>";
    }
    
    // Test without academic year filter
    echo "<h3>Testing Without Academic Year Filter:</h3>";
    try {
        $noFilterQuery = "
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif'
            ORDER BY k.tingkat, s.nama_lengkap
            LIMIT 10
        ";
        
        $noFilterResults = $db->fetchAll($noFilterQuery);
        echo "<p><strong>Results without year filter:</strong> " . count($noFilterResults) . " records found</p>";
        
        if (!empty($noFilterResults)) {
            echo "<h4>Sample Results:</h4>";
            foreach (array_slice($noFilterResults, 0, 3) as $student) {
                $kelas = $student['nama_kelas'] ?? 'No Class';
                $year = $student['tahun_pelajaran'] ?? 'No Year';
                echo "<p>👤 {$student['nama_lengkap']} (NIS: {$student['nis']}) - Class: {$kelas} - Year: {$year}</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ No Filter Query Error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🔧 Testing Model Methods</h2>";
    
    // Test model methods
    echo "<h3>Testing Siswa Model Methods:</h3>";
    
    try {
        $modelTotalCount = $siswaModel->getTotalCount($selectedYear);
        echo "<p><strong>Model getTotalCount():</strong> {$modelTotalCount}</p>";
        
        $modelPaginatedResults = $siswaModel->getAllPaginated($selectedYear, 10, 0);
        echo "<p><strong>Model getAllPaginated():</strong> " . count($modelPaginatedResults) . " records</p>";
        
        $modelAllResults = $siswaModel->getAll($selectedYear);
        echo "<p><strong>Model getAll():</strong> " . count($modelAllResults) . " records</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Model Method Error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🎯 Diagnosis</h2>";
    
    // Provide diagnosis
    if ($totalStudents['total'] == 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ No Students in Database</h4>";
        echo "<p>The database contains no student records. Please add some students first.</p>";
        echo "</div>";
    } elseif ($activeStudents['total'] == 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ No Active Students</h4>";
        echo "<p>All students have status other than 'aktif'. Check student status values.</p>";
        echo "</div>";
    } elseif ($studentsWithKelas['total'] == 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ No Students Assigned to Classes</h4>";
        echo "<p>Students exist but are not assigned to any classes. Please assign students to classes.</p>";
        echo "</div>";
    } else {
        $studentsInSelectedYear = 0;
        foreach ($studentsByYear as $yearData) {
            if ($yearData['tahun_pelajaran'] === $selectedYear) {
                $studentsInSelectedYear = $yearData['total'];
                break;
            }
        }
        
        if ($studentsInSelectedYear == 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ No Students in Selected Academic Year</h4>";
            echo "<p>Students exist but none are in the selected academic year ({$selectedYear}).</p>";
            echo "<p>Available years with students:</p>";
            echo "<ul>";
            foreach ($studentsByYear as $yearData) {
                if ($yearData['tahun_pelajaran'] && $yearData['total'] > 0) {
                    echo "<li>{$yearData['tahun_pelajaran']}: {$yearData['total']} students</li>";
                }
            }
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>✅ Data Looks Good</h4>";
            echo "<p>Found {$studentsInSelectedYear} students in selected year ({$selectedYear}).</p>";
            echo "<p>The issue might be in the controller or view logic.</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>🔗 Quick Actions</h2>";
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue;'>🔗 Go to Student List Page</a></p>";
    echo "<p><a href='/siswa-app/setup_sample_students.php' target='_blank' style='color: blue;'>🔗 Add Sample Students</a></p>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue;'>🔗 Go to Class Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
