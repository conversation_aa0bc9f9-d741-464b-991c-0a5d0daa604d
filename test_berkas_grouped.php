<?php
/**
 * Test Berkas Grouped Display
 */

require_once 'app/models/Database.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/User.php';
require_once 'app/models/Siswa.php';
require_once 'app/models/Berkas.php';

// Initialize session manager
$sessionManager = new SimpleSessionManager();
$userModel = new User();
$siswaModel = new Siswa();
$berkasModel = new Berkas();

echo "<h2>🗂️ Test Berkas Grouped Display</h2>";

// Login sebagai admin
$result = $userModel->authenticate('admin', 'admin123');

if ($result['success']) {
    $sessionManager->loginUser($result['user']);
    echo "<p style='color: green;'>✅ Login berhasil sebagai: " . htmlspecialchars($result['user']['nama_lengkap']) . "</p>";
    
    // Get sample siswa
    $siswaList = $siswaModel->getAll();
    if (!empty($siswaList)) {
        $siswa = $siswaList[0];
        $siswaId = $siswa['id_siswa'] ?? $siswa['id'];
        
        echo "<p>📚 Testing dengan siswa: <strong>" . htmlspecialchars($siswa['nama_lengkap']) . "</strong> (ID: $siswaId)</p>";
        
        // Get berkas
        $berkas = $berkasModel->getBySiswaId($siswaId);
        
        if (!empty($berkas)) {
            echo "<h3>📋 File Categories Available</h3>";
            $fileCategories = $berkasModel->getFileCategories();
            
            foreach ($fileCategories as $categoryName => $types) {
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
                echo "<h5 style='color: #007bff; margin: 0 0 10px 0;'>$categoryName</h5>";
                foreach ($types as $typeKey => $typeName) {
                    echo "<span style='background: #f8f9fa; padding: 3px 8px; margin: 2px; border-radius: 3px; font-size: 0.85em;'>$typeName</span> ";
                }
                echo "</div>";
            }
            
            echo "<h3>📁 Berkas Grouped by Category</h3>";
            
            // Group berkas by category
            $berkasGrouped = [];
            
            // Define category mapping with icons and colors
            $categoryMapping = [
                'Dokumen Identitas' => ['icon' => '🆔', 'color' => '#007bff'],
                'Rapor' => ['icon' => '📊', 'color' => '#28a745'],
                'Ijazah' => ['icon' => '🏆', 'color' => '#ffc107'],
                'Foto' => ['icon' => '📷', 'color' => '#17a2b8'],
                'Surat' => ['icon' => '📄', 'color' => '#dc3545'],
                'Prestasi' => ['icon' => '🏅', 'color' => '#fd7e14'],
                'Lainnya' => ['icon' => '📁', 'color' => '#6c757d']
            ];
            
            // Group files by category
            foreach ($berkas as $file) {
                $jenisberkas = $file['jenis_berkas'];
                $categoryFound = false;
                
                foreach ($fileCategories as $categoryName => $types) {
                    if (array_key_exists($jenisberkas, $types)) {
                        if (!isset($berkasGrouped[$categoryName])) {
                            $berkasGrouped[$categoryName] = [];
                        }
                        $berkasGrouped[$categoryName][] = $file;
                        $categoryFound = true;
                        break;
                    }
                }
                
                // If not found in any category, put in "Lainnya"
                if (!$categoryFound) {
                    if (!isset($berkasGrouped['Lainnya'])) {
                        $berkasGrouped['Lainnya'] = [];
                    }
                    $berkasGrouped['Lainnya'][] = $file;
                }
            }
            
            // Function to get document display name
            function getDocumentDisplayName($jenisberkas) {
                $displayNames = [
                    // Dokumen Identitas
                    'kartu_keluarga' => 'Kartu Keluarga',
                    'akta_lahir' => 'Akta Kelahiran',
                    
                    // Rapor
                    'rapor_kelas_x' => 'Rapor Kelas X',
                    'rapor_kelas_xi' => 'Rapor Kelas XI',
                    'rapor_kelas_xii' => 'Rapor Kelas XII',
                    
                    // Ijazah
                    'ijazah_sd' => 'Ijazah SD/MI',
                    'ijazah_smp' => 'Ijazah SMP/MTs',
                    'ijazah_sma' => 'Ijazah SMA/SMK/MA',
                    
                    // Foto
                    'foto_siswa' => 'Foto Siswa',
                    
                    // Surat
                    'surat_keterangan_sehat' => 'Surat Keterangan Sehat',
                    'surat_kelakuan_baik' => 'Surat Kelakuan Baik',
                    'surat_peringatan_1' => 'Surat Peringatan 1',
                    'surat_peringatan_2' => 'Surat Peringatan 2',
                    'surat_peringatan_3' => 'Surat Peringatan 3',
                    'surat_panggilan_ortu' => 'Surat Panggilan Orang Tua',
                    
                    // Prestasi
                    'piagam_prestasi' => 'Piagam Prestasi',
                    'sertifikat_lomba' => 'Sertifikat Lomba',
                    'penghargaan_akademik' => 'Penghargaan Akademik',
                    
                    // Lainnya
                    'lainnya' => 'Dokumen Lainnya'
                ];
                return $displayNames[$jenisberkas] ?? ucwords(str_replace('_', ' ', $jenisberkas));
            }
            
            // Display grouped berkas
            foreach ($berkasGrouped as $categoryName => $categoryFiles) {
                $categoryInfo = $categoryMapping[$categoryName] ?? ['icon' => '📁', 'color' => '#6c757d'];
                
                echo "<div style='border: 2px solid " . $categoryInfo['color'] . "; border-radius: 10px; padding: 15px; margin: 15px 0; background: rgba(" . hexdec(substr($categoryInfo['color'], 1, 2)) . "," . hexdec(substr($categoryInfo['color'], 3, 2)) . "," . hexdec(substr($categoryInfo['color'], 5, 2)) . ", 0.05);'>";
                
                // Category Header
                echo "<div style='display: flex; align-items: center; margin-bottom: 15px;'>";
                echo "<span style='font-size: 1.5em; margin-right: 10px;'>" . $categoryInfo['icon'] . "</span>";
                echo "<div>";
                echo "<h4 style='margin: 0; color: " . $categoryInfo['color'] . ";'>$categoryName</h4>";
                echo "<small style='color: #6c757d;'>" . count($categoryFiles) . " berkas</small>";
                echo "</div>";
                echo "</div>";
                
                // Category Files
                echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px;'>";
                
                foreach ($categoryFiles as $file) {
                    echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 12px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
                    
                    // File icon based on extension
                    $fileName = $file['nama_file_asli'] ?? $file['nama_berkas'] ?? '';
                    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                    $fileIcon = '📄';
                    
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                        $fileIcon = '🖼️';
                    } elseif ($extension === 'pdf') {
                        $fileIcon = '📕';
                    } elseif (in_array($extension, ['doc', 'docx'])) {
                        $fileIcon = '📘';
                    }
                    
                    echo "<div style='display: flex; align-items: start;'>";
                    echo "<span style='font-size: 1.2em; margin-right: 10px;'>$fileIcon</span>";
                    echo "<div style='flex: 1;'>";
                    echo "<h6 style='margin: 0 0 5px 0; font-weight: bold;'>" . getDocumentDisplayName($file['jenis_berkas']) . "</h6>";
                    
                    if (!empty($file['keterangan'])) {
                        echo "<small style='color: #17a2b8; display: block; margin-bottom: 8px;'>";
                        echo "ℹ️ " . htmlspecialchars($file['keterangan']);
                        echo "</small>";
                    }
                    
                    // Action buttons
                    echo "<div style='display: flex; gap: 5px; margin-top: 8px;'>";
                    echo "<a href='/siswa-app/public/" . htmlspecialchars($file['file_path']) . "' target='_blank' style='background: #007bff; color: white; padding: 4px 8px; text-decoration: none; border-radius: 4px; font-size: 0.8em;'>👁️ Lihat</a>";
                    echo "<a href='/siswa-app/public/berkas/download/" . $file['id'] . "' style='background: #28a745; color: white; padding: 4px 8px; text-decoration: none; border-radius: 4px; font-size: 0.8em;'>⬇️ Download</a>";
                    echo "</div>";
                    
                    echo "</div>";
                    echo "</div>";
                    echo "</div>";
                }
                
                echo "</div>";
                echo "</div>";
            }
            
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada berkas untuk testing</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Tidak ada data siswa untuk testing</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Login gagal: " . htmlspecialchars($result['message']) . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Summary Pengelompokan Berkas</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724; margin-top: 0;'>✅ Berkas Berhasil Dikelompokkan!</h4>";

echo "<p><strong>Kategori Berkas:</strong></p>";
echo "<ol>";
echo "<li><strong>📋 Dokumen Identitas:</strong> Kartu Keluarga, Akta Kelahiran</li>";
echo "<li><strong>📊 Rapor:</strong> Rapor Kelas X, XI, XII</li>";
echo "<li><strong>🏆 Ijazah:</strong> Ijazah SD/MI, SMP/MTs, SMA/SMK/MA</li>";
echo "<li><strong>📷 Foto:</strong> Foto Siswa</li>";
echo "<li><strong>📄 Surat:</strong> Surat Keterangan, Surat Peringatan 1-3, Surat Panggilan Orang Tua</li>";
echo "<li><strong>🏅 Prestasi:</strong> Piagam Prestasi, Sertifikat Lomba, Penghargaan Akademik</li>";
echo "<li><strong>📁 Lainnya:</strong> Dokumen lain yang tidak masuk kategori di atas</li>";
echo "</ol>";

echo "<p><strong>Fitur Baru:</strong></p>";
echo "<ul>";
echo "<li>✅ Berkas dikelompokkan berdasarkan kategori</li>";
echo "<li>✅ Nama berkas menggunakan nama deskriptif (bukan nama file)</li>";
echo "<li>✅ Tidak menampilkan tanggal upload (lebih clean)</li>";
echo "<li>✅ Icon kategori yang berbeda untuk setiap jenis</li>";
echo "<li>✅ Warna yang konsisten untuk setiap kategori</li>";
echo "<li>✅ Layout grid yang responsive</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='public/siswa/detail/$siswaId' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Lihat Detail Siswa</a> ";
echo "<a href='public/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Dashboard</a>";
echo "</p>";
?>
