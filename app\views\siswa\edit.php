<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-person-gear text-primary"></i>
                        Edit Data Siswa
                    </h1>
                    <p class="text-muted mb-0">
                        Edit data untuk: <strong><?= htmlspecialchars($siswa['nama_lengkap'] ?? 'N/A') ?></strong>
                    </p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali ke Detail
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            <?= htmlspecialchars($_SESSION['success']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            <?= htmlspecialchars($_SESSION['error']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-pencil-square"></i>
                        Form Edit Siswa
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="editSiswaForm">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="id_siswa" value="<?= $siswa['id_siswa'] ?? $siswa['id'] ?>">
                        
                        <div class="row">
                            <!-- Data Pribadi -->
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">Data Pribadi</h6>
                                
                                <div class="mb-3">
                                    <label for="nis" class="form-label">NIS <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nis" name="nis" 
                                           value="<?= htmlspecialchars($siswa['nis'] ?? '') ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nisn" class="form-label">NISN</label>
                                    <input type="text" class="form-control" id="nisn" name="nisn"
                                           value="<?= htmlspecialchars($siswa['nisn'] ?? '') ?>" maxlength="10">
                                </div>

                                <div class="mb-3">
                                    <label for="nik" class="form-label">NIK</label>
                                    <input type="text" class="form-control" id="nik" name="nik"
                                           value="<?= htmlspecialchars($siswa['nik'] ?? '') ?>" maxlength="16">
                                </div>

                                <div class="mb-3">
                                    <label for="no_kk" class="form-label">No. KK</label>
                                    <input type="text" class="form-control" id="no_kk" name="no_kk"
                                           value="<?= htmlspecialchars($siswa['no_kk'] ?? '') ?>" maxlength="16">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nama_lengkap" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" 
                                           value="<?= htmlspecialchars($siswa['nama_lengkap'] ?? '') ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="jenis_kelamin" class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                    <select class="form-select" id="jenis_kelamin" name="jenis_kelamin" required>
                                        <option value="">Pilih Jenis Kelamin</option>
                                        <option value="Laki-laki" <?= ($siswa['jenis_kelamin'] ?? '') === 'Laki-laki' ? 'selected' : '' ?>>Laki-laki</option>
                                        <option value="Perempuan" <?= ($siswa['jenis_kelamin'] ?? '') === 'Perempuan' ? 'selected' : '' ?>>Perempuan</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="tempat_lahir" class="form-label">Tempat Lahir</label>
                                    <input type="text" class="form-control" id="tempat_lahir" name="tempat_lahir" 
                                           value="<?= htmlspecialchars($siswa['tempat_lahir'] ?? '') ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="tanggal_lahir" class="form-label">Tanggal Lahir</label>
                                    <input type="date" class="form-control" id="tanggal_lahir" name="tanggal_lahir"
                                           value="<?= htmlspecialchars($siswa['tanggal_lahir'] ?? '') ?>">
                                </div>

                                <div class="mb-3">
                                    <label for="golongan_darah" class="form-label">Golongan Darah</label>
                                    <select class="form-select" id="golongan_darah" name="golongan_darah">
                                        <option value="">Pilih Golongan Darah</option>
                                        <option value="A" <?= ($siswa['golongan_darah'] ?? '') === 'A' ? 'selected' : '' ?>>A</option>
                                        <option value="B" <?= ($siswa['golongan_darah'] ?? '') === 'B' ? 'selected' : '' ?>>B</option>
                                        <option value="AB" <?= ($siswa['golongan_darah'] ?? '') === 'AB' ? 'selected' : '' ?>>AB</option>
                                        <option value="O" <?= ($siswa['golongan_darah'] ?? '') === 'O' ? 'selected' : '' ?>>O</option>
                                        <option value="A+" <?= ($siswa['golongan_darah'] ?? '') === 'A+' ? 'selected' : '' ?>>A+</option>
                                        <option value="A-" <?= ($siswa['golongan_darah'] ?? '') === 'A-' ? 'selected' : '' ?>>A-</option>
                                        <option value="B+" <?= ($siswa['golongan_darah'] ?? '') === 'B+' ? 'selected' : '' ?>>B+</option>
                                        <option value="B-" <?= ($siswa['golongan_darah'] ?? '') === 'B-' ? 'selected' : '' ?>>B-</option>
                                        <option value="AB+" <?= ($siswa['golongan_darah'] ?? '') === 'AB+' ? 'selected' : '' ?>>AB+</option>
                                        <option value="AB-" <?= ($siswa['golongan_darah'] ?? '') === 'AB-' ? 'selected' : '' ?>>AB-</option>
                                        <option value="O+" <?= ($siswa['golongan_darah'] ?? '') === 'O+' ? 'selected' : '' ?>>O+</option>
                                        <option value="O-" <?= ($siswa['golongan_darah'] ?? '') === 'O-' ? 'selected' : '' ?>>O-</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Data Akademik & Kontak -->
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">Data Akademik & Kontak</h6>
                                
                                <div class="mb-3">
                                    <label for="kelas_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                                    <select class="form-select" id="kelas_id" name="kelas_id" required>
                                        <option value="">Pilih Kelas</option>
                                        <?php if (isset($kelas_list) && !empty($kelas_list)): ?>
                                            <?php foreach ($kelas_list as $kelas): ?>
                                                <?php
                                                $kelasId = $kelas['id_kelas'] ?? $kelas['id'] ?? '';
                                                $kelasNama = $kelas['nama_kelas'] ?? $kelas['nama'] ?? 'Kelas';
                                                $siswaKelasId = $siswa['kelas_id'] ?? '';
                                                ?>
                                                <option value="<?= htmlspecialchars($kelasId) ?>"
                                                        <?= $siswaKelasId == $kelasId ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($kelasNama) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <option value="">Tidak ada kelas tersedia</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="tahun_masuk" class="form-label">Tahun Masuk</label>
                                    <input type="number" class="form-control" id="tahun_masuk" name="tahun_masuk" 
                                           value="<?= htmlspecialchars($siswa['tahun_masuk'] ?? '') ?>" 
                                           min="2000" max="<?= date('Y') ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status_siswa" class="form-label">Status Siswa</label>
                                    <select class="form-select" id="status_siswa" name="status_siswa">
                                        <option value="Aktif" <?= ($siswa['status_siswa'] ?? '') === 'Aktif' ? 'selected' : '' ?>>Aktif</option>
                                        <option value="Tidak Aktif" <?= ($siswa['status_siswa'] ?? '') === 'Tidak Aktif' ? 'selected' : '' ?>>Tidak Aktif</option>
                                        <option value="Lulus" <?= ($siswa['status_siswa'] ?? '') === 'Lulus' ? 'selected' : '' ?>>Lulus</option>
                                        <option value="Pindah" <?= ($siswa['status_siswa'] ?? '') === 'Pindah' ? 'selected' : '' ?>>Pindah</option>
                                        <option value="Drop Out" <?= ($siswa['status_siswa'] ?? '') === 'Drop Out' ? 'selected' : '' ?>>Drop Out</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($siswa['email'] ?? '') ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="no_telepon" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="no_telepon" name="no_telepon" 
                                           value="<?= htmlspecialchars($siswa['no_telepon'] ?? '') ?>">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Data Orang Tua -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-3 mt-3">Data Orang Tua</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nama_ayah" class="form-label">Nama Ayah</label>
                                    <input type="text" class="form-control" id="nama_ayah" name="nama_ayah"
                                           value="<?= htmlspecialchars($siswa['nama_ayah'] ?? '') ?>">
                                </div>

                                <div class="mb-3">
                                    <label for="pekerjaan_ayah" class="form-label">Pekerjaan Ayah</label>
                                    <input type="text" class="form-control" id="pekerjaan_ayah" name="pekerjaan_ayah"
                                           value="<?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? '') ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nama_ibu" class="form-label">Nama Ibu</label>
                                    <input type="text" class="form-control" id="nama_ibu" name="nama_ibu"
                                           value="<?= htmlspecialchars($siswa['nama_ibu'] ?? '') ?>">
                                </div>

                                <div class="mb-3">
                                    <label for="pekerjaan_ibu" class="form-label">Pekerjaan Ibu</label>
                                    <input type="text" class="form-control" id="pekerjaan_ibu" name="pekerjaan_ibu"
                                           value="<?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? '') ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Data Sekolah & Paroki -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-3 mt-3">Data Sekolah & Paroki</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="asal_sekolah" class="form-label">Asal Sekolah</label>
                                    <input type="text" class="form-control" id="asal_sekolah" name="asal_sekolah"
                                           value="<?= htmlspecialchars($siswa['asal_sekolah'] ?? '') ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="asal_paroki" class="form-label">Asal Paroki</label>
                                    <input type="text" class="form-control" id="asal_paroki" name="asal_paroki"
                                           value="<?= htmlspecialchars($siswa['asal_paroki'] ?? '') ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Alamat -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-3 mt-3">Alamat</h6>

                                <div class="mb-3">
                                    <label for="alamat" class="form-label">Alamat Lengkap</label>
                                    <textarea class="form-control" id="alamat" name="alamat" rows="3"><?= htmlspecialchars($siswa['alamat'] ?? '') ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle"></i>
                                        Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle"></i>
                                        Simpan Perubahan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar Info -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-info-circle"></i>
                        Informasi
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Field dengan tanda (*) wajib diisi</small>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>NIS harus unik untuk setiap siswa</small>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Pastikan data yang dimasukkan benar</small>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <small>Perubahan akan tersimpan otomatis</small>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="bi bi-lightning"></i>
                        Aksi Cepat
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye"></i>
                            Lihat Detail
                        </a>
                        <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-outline-success btn-sm">
                            <i class="bi bi-upload"></i>
                            Upload Berkas
                        </a>
                        <a href="/siswa-app/public/siswa" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-list"></i>
                            Daftar Siswa
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('editSiswaForm').addEventListener('submit', function(e) {
    const nis = document.getElementById('nis').value.trim();
    const namaLengkap = document.getElementById('nama_lengkap').value.trim();
    const jenisKelamin = document.getElementById('jenis_kelamin').value;
    const kelasId = document.getElementById('kelas_id').value;
    
    if (!nis) {
        e.preventDefault();
        alert('NIS harus diisi');
        document.getElementById('nis').focus();
        return;
    }
    
    if (!namaLengkap) {
        e.preventDefault();
        alert('Nama lengkap harus diisi');
        document.getElementById('nama_lengkap').focus();
        return;
    }
    
    if (!jenisKelamin) {
        e.preventDefault();
        alert('Jenis kelamin harus dipilih');
        document.getElementById('jenis_kelamin').focus();
        return;
    }
    
    if (!kelasId) {
        e.preventDefault();
        alert('Kelas harus dipilih');
        document.getElementById('kelas_id').focus();
        return;
    }
});

// Format NIK dan No KK (hanya angka)
document.getElementById('nik').addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/[^0-9]/g, '');
});

document.getElementById('no_kk').addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/[^0-9]/g, '');
});

document.getElementById('nisn').addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/[^0-9]/g, '');
});
</script>
