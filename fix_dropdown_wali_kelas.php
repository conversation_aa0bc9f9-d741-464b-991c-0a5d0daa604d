<?php
/**
 * Script Perbaikan Dropdown Wali Kelas
 * 
 * Memastikan dropdown wali kelas di form tambah kelas berfungsi dengan benar
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/User.php';

echo "<h1>🔧 Perbaikan Dropdown Wali Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    $userModel = new User();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Tujuan Perbaikan</h3>";
    echo "<ul>";
    echo "<li>Memastikan ada user dengan role wali kelas</li>";
    echo "<li>Memastikan dropdown form tambah kelas menampilkan pilihan</li>";
    echo "<li>Menambah kolom kelas yang dikelola di manajemen user</li>";
    echo "<li>Test semua fungsi terkait wali kelas</li>";
    echo "</ul>";
    echo "</div>";
    
    // Step 1: Cek user wali kelas yang ada
    echo "<h2>👥 Step 1: Cek User Wali Kelas</h2>";
    
    $stmt = $pdo->query("
        SELECT id, username, nama_lengkap, role, is_active
        FROM users 
        WHERE role LIKE 'wali_kelas%'
        ORDER BY role, nama_lengkap
    ");
    $existingWaliKelas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>User wali kelas yang ada:</strong> " . count($existingWaliKelas) . "</p>";
    
    if (!empty($existingWaliKelas)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($existingWaliKelas as $user) {
            $statusColor = $user['is_active'] ? 'green' : 'red';
            $statusText = $user['is_active'] ? 'Aktif' : 'Tidak Aktif';
            
            echo "<tr>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><code>{$user['role']}</code></td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Tidak ada user wali kelas! Akan dibuat otomatis.</p>";
    }
    
    // Step 2: Buat user wali kelas jika belum ada
    echo "<h2>🔄 Step 2: Buat User Wali Kelas</h2>";
    
    if (count($existingWaliKelas) < 5) {
        echo "<p>🔄 Membuat user wali kelas untuk kelas yang umum...</p>";
        
        $kelasUmum = [
            ['kelas' => 'KPP-A', 'role' => 'wali_kelas_kpp_a', 'nama' => 'Pak Budi Santoso'],
            ['kelas' => 'X-1', 'role' => 'wali_kelas_x_1', 'nama' => 'Pak Ahmad Wijaya'],
            ['kelas' => 'X-2', 'role' => 'wali_kelas_x_2', 'nama' => 'Pak Rizki Pratama'],
            ['kelas' => 'XI-1', 'role' => 'wali_kelas_xi_1', 'nama' => 'Pak Dani Kurniawan'],
            ['kelas' => 'XI-2', 'role' => 'wali_kelas_xi_2', 'nama' => 'Pak Eko Saputra']
        ];
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Kelas</th><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($kelasUmum as $data) {
            $username = str_replace('wali_kelas_', 'wali_', $data['role']);
            $email = $username . "@sekolah.com";
            $password = password_hash('wali123', PASSWORD_DEFAULT);
            
            // Cek apakah user sudah ada
            $stmt = $pdo->prepare("SELECT id FROM users WHERE role = ? OR username = ?");
            $stmt->execute([$data['role'], $username]);
            $existingUser = $stmt->fetch();
            
            if (!$existingUser) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO users (username, email, password, role, nama_lengkap, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, 1, NOW())
                    ");
                    $stmt->execute([$username, $email, $password, $data['role'], $data['nama']]);
                    
                    echo "<tr style='background: #d4edda;'>";
                    echo "<td>{$data['kelas']}</td>";
                    echo "<td>{$username}</td>";
                    echo "<td>{$data['nama']}</td>";
                    echo "<td><code>{$data['role']}</code></td>";
                    echo "<td style='color: green;'>✅ Berhasil dibuat</td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr style='background: #f8d7da;'>";
                    echo "<td>{$data['kelas']}</td>";
                    echo "<td>{$username}</td>";
                    echo "<td>{$data['nama']}</td>";
                    echo "<td><code>{$data['role']}</code></td>";
                    echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr style='background: #fff3cd;'>";
                echo "<td>{$data['kelas']}</td>";
                echo "<td>{$username}</td>";
                echo "<td>{$data['nama']}</td>";
                echo "<td><code>{$data['role']}</code></td>";
                echo "<td style='color: orange;'>⚠️ Sudah ada</td>";
                echo "</tr>";
            }
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ User wali kelas sudah cukup</p>";
    }
    
    // Step 3: Test method getUsersByRole()
    echo "<h2>🧪 Step 3: Test Method getUsersByRole()</h2>";
    
    $waliKelasList = $userModel->getUsersByRole('wali_kelas');
    
    echo "<p><strong>Hasil getUsersByRole('wali_kelas'):</strong> " . count($waliKelasList) . " user</p>";
    
    if (!empty($waliKelasList)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasList as $user) {
            echo "<tr>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><code>{$user['role']}</code></td>";
            echo "<td style='color: green;'>✅ Ditemukan</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        echo "<p style='color: green;'>✅ Method getUsersByRole() berfungsi dengan baik</p>";
    } else {
        echo "<p style='color: red;'>❌ Method getUsersByRole() tidak mengembalikan data</p>";
    }
    
    // Step 4: Test dropdown simulation
    echo "<h2>📝 Step 4: Simulasi Dropdown</h2>";
    
    if (!empty($waliKelasList)) {
        echo "<h4>Dropdown yang akan muncul di form tambah kelas:</h4>";
        echo "<select class='form-select' style='width: 100%; padding: 8px; margin: 10px 0; font-size: 14px;'>";
        echo "<option value=''>Pilih Wali Kelas</option>";
        
        foreach ($waliKelasList as $wali) {
            $roleInfo = '';
            if (strpos($wali['role'], 'wali_kelas_') === 0) {
                $roleInfo = ' - ' . strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $wali['role']));
            }
            
            echo "<option value='{$wali['id']}'>";
            echo htmlspecialchars($wali['nama_lengkap']) . " (" . htmlspecialchars($wali['username']) . ")" . $roleInfo;
            echo "</option>";
        }
        echo "</select>";
        
        echo "<p style='color: green;'>✅ Dropdown akan menampilkan " . count($waliKelasList) . " pilihan wali kelas</p>";
    } else {
        echo "<p style='color: red;'>❌ Dropdown akan kosong</p>";
    }
    
    // Step 5: Verifikasi perbaikan
    echo "<h2>✅ Step 5: Verifikasi Perbaikan</h2>";
    
    $issues = [];
    
    // Cek user wali kelas
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role LIKE 'wali_kelas%' AND is_active = 1");
    $totalWaliKelas = $stmt->fetchColumn();
    
    if ($totalWaliKelas == 0) {
        $issues[] = "Tidak ada user wali kelas aktif";
    }
    
    // Cek method getUsersByRole
    $testResult = $userModel->getUsersByRole('wali_kelas');
    if (empty($testResult)) {
        $issues[] = "Method getUsersByRole() tidak mengembalikan data";
    }
    
    // Cek controller KelasController
    $controllerFile = __DIR__ . '/app/controllers/KelasController.php';
    if (file_exists($controllerFile)) {
        $controllerContent = file_get_contents($controllerFile);
        if (strpos($controllerContent, 'getUsersByRole') === false) {
            $issues[] = "Controller tidak memanggil getUsersByRole()";
        }
    } else {
        $issues[] = "KelasController tidak ditemukan";
    }
    
    if (empty($issues)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 PERBAIKAN BERHASIL!</h3>";
        echo "<ul>";
        echo "<li>✅ User wali kelas: {$totalWaliKelas} user aktif</li>";
        echo "<li>✅ Method getUsersByRole(): " . count($testResult) . " user ditemukan</li>";
        echo "<li>✅ Controller: Memanggil getUsersByRole() dengan benar</li>";
        echo "<li>✅ Dropdown: Akan menampilkan pilihan wali kelas</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ MASIH ADA MASALAH</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li style='color: red;'>{$issue}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Step 6: Link test manual
    echo "<h2>🔗 Step 6: Test Manual</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Link untuk Test Manual:</h4>";
    echo "<ul>";
    echo "<li><a href='/siswa-app/public/kelas/create' target='_blank'>Form Tambah Kelas</a> - Test dropdown wali kelas</li>";
    echo "<li><a href='/siswa-app/public/admin/users' target='_blank'>Manajemen User</a> - Lihat kolom kelas yang dikelola</li>";
    echo "<li><a href='/siswa-app/public/kelas' target='_blank'>Manajemen Kelas</a> - Lihat assignment wali kelas</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 Langkah Test Manual:</h4>";
    echo "<ol>";
    echo "<li><strong>Login sebagai admin</strong></li>";
    echo "<li><strong>Akses form tambah kelas</strong> dan cek dropdown wali kelas</li>";
    echo "<li><strong>Buat kelas baru</strong> dengan memilih wali kelas</li>";
    echo "<li><strong>Cek manajemen user</strong> untuk melihat kolom kelas yang dikelola</li>";
    echo "<li><strong>Verifikasi</strong> bahwa semua data konsisten</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Perbaikan selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
