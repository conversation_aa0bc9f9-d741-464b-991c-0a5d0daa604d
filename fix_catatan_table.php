<?php
/**
 * Script untuk memperbaiki tabel catatan_siswa
 */

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>Memperbaiki Tabel Catatan Siswa</h2>";
    
    $db = new Database();
    
    // Cek struktur tabel
    echo "<p>1. Memeriksa struktur tabel catatan_siswa...</p>";
    $structure = $db->fetchAll("DESCRIBE catatan_siswa");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($structure as $field) {
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Null']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td>{$field['Default']}</td>";
        echo "<td>{$field['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Cek auto increment value
    echo "<p>2. Memeriksa auto increment value...</p>";
    $autoIncrement = $db->fetch("SHOW TABLE STATUS LIKE 'catatan_siswa'");
    echo "<p>Auto Increment Value: <strong>{$autoIncrement['Auto_increment']}</strong></p>";
    
    // Reset auto increment jika perlu
    if ($autoIncrement['Auto_increment'] == 0 || $autoIncrement['Auto_increment'] == 1) {
        echo "<p>3. Mereset auto increment...</p>";
        
        // Cari ID tertinggi yang ada
        $maxId = $db->fetch("SELECT MAX(id) as max_id FROM catatan_siswa");
        $nextId = ($maxId['max_id'] ?? 0) + 1;
        
        echo "<p>ID tertinggi saat ini: {$maxId['max_id']}</p>";
        echo "<p>Setting auto increment ke: {$nextId}</p>";
        
        $db->query("ALTER TABLE catatan_siswa AUTO_INCREMENT = {$nextId}");
        
        echo "<p style='color: green;'>✅ Auto increment berhasil direset</p>";
    }
    
    // Cek lagi auto increment value
    $autoIncrement = $db->fetch("SHOW TABLE STATUS LIKE 'catatan_siswa'");
    echo "<p>Auto Increment Value setelah perbaikan: <strong>{$autoIncrement['Auto_increment']}</strong></p>";
    
    // Test insert
    echo "<p>4. Testing insert data...</p>";
    
    // Cek siswa
    $siswa = $db->fetch("SELECT id_siswa, nama_lengkap FROM siswa LIMIT 1");
    
    if ($siswa) {
        try {
            $sql = "INSERT INTO catatan_siswa (
                siswa_id, jenis_catatan, judul_catatan, isi_catatan,
                tanggal_catatan, tingkat_prioritas, status_catatan, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $siswa['id_siswa'],
                'bk_prestasi',
                'Test Prestasi',
                'Ini adalah test prestasi untuk memastikan insert berfungsi',
                date('Y-m-d'),
                'sedang',
                'aktif',
                1
            ];
            
            $result = $db->query($sql, $params);
            $insertId = $db->lastInsertId();
            
            if ($result && $insertId) {
                echo "<p style='color: green;'>✅ Test insert berhasil dengan ID: {$insertId}</p>";
                
                // Hapus data test
                $db->query("DELETE FROM catatan_siswa WHERE id = ?", [$insertId]);
                echo "<p>Test data dihapus</p>";
            } else {
                echo "<p style='color: red;'>❌ Test insert gagal</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error test insert: {$e->getMessage()}</p>";
        }
    }
    
    echo "<h3 style='color: green;'>✅ Perbaikan tabel selesai!</h3>";
    echo "<p>Sekarang Anda dapat menjalankan script insert_sample_catatan.php lagi.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
