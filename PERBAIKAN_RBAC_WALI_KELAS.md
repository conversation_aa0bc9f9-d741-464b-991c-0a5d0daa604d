# Perbaikan Sistem RBAC Wali Kelas

## 📋 Deskripsi Masalah
Sistem RBAC (Role-Based Access Control) untuk wali kelas tidak berfungsi dengan baik. Wali kelas masih dapat melihat data siswa dari semua kelas, bukan hanya kelas yang mereka kelola.

## 🔍 Analisis Masalah yang Ditemukan

### 1. Masalah Database
- **Kolom `wali_kelas_id` tidak ada** di tabel `kelas`
- **Relasi antara user dan kelas tidak terdefinisi** dengan benar
- **Data wali kelas tidak ter-assign** ke kelas tertentu

### 2. Masalah Model
- **Query `getAllForWaliKelas()`** menggunakan field yang tidak ada
- **Model Kelas tidak memiliki filter** untuk wali kelas
- **Method `getAll()` tidak mengecek role** pengguna

### 3. Masalah Form
- **Form kelas hanya menggunakan input text** untuk wali kelas
- **Tidak ada dropdown untuk memilih** user dengan role wali_kelas
- **Tidak ada validasi wali_kelas_id** saat create/edit kelas

## 🔧 Solusi yang Diimplementasikan

### 1. Perbaikan Database Schema

**File:** `fix_wali_kelas_database.php`

```sql
-- Tambah kolom wali_kelas_id
ALTER TABLE kelas ADD COLUMN wali_kelas_id INT NULL AFTER wali_kelas;

-- Tambah foreign key constraint
ALTER TABLE kelas ADD CONSTRAINT fk_kelas_wali_kelas 
FOREIGN KEY (wali_kelas_id) REFERENCES users(id) ON DELETE SET NULL;

-- Tambah index
ALTER TABLE kelas ADD INDEX idx_wali_kelas_id (wali_kelas_id);

-- Update enum role untuk menambah wali_kelas
ALTER TABLE users MODIFY COLUMN role 
ENUM('admin','pamong_mp','pamong_mt','pamong_mm','pamong_mu','wali_kelas','staff') 
DEFAULT 'staff';
```

### 2. Perbaikan Model Kelas

**File:** `app/models/Kelas.php`

```php
public function getAll($academicYear = null) {
    // Filter by role
    require_once __DIR__ . '/../helpers/Security.php';
    if (Security::isWaliKelas()) {
        return $this->getForWaliKelas($academicYear);
    }
    // ... query normal untuk admin/pamong
}

public function getForWaliKelas($academicYear = null) {
    $userId = $_SESSION['user_id'] ?? 0;
    
    return $this->db->fetchAll("
        SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
        FROM kelas
        WHERE is_active = 1 
        AND tahun_pelajaran = ?
        AND wali_kelas_id = ?
        ORDER BY tingkat, nama_kelas
    ", [$academicYear, $userId]);
}
```

### 3. Perbaikan Model User

**File:** `app/models/User.php`

```php
public function getUsersByRole($role) {
    return $this->db->fetchAll("
        SELECT id, username, email, role, nama_lengkap, is_active
        FROM users
        WHERE role = ? AND is_active = 1
        ORDER BY nama_lengkap
    ", [$role]);
}
```

### 4. Perbaikan Form Kelas

**File:** `app/views/kelas/form.php`

```php
<select class="form-select" id="wali_kelas_id" name="wali_kelas_id" required>
    <option value="">Pilih Wali Kelas</option>
    <?php if (isset($wali_kelas_list) && is_array($wali_kelas_list)): ?>
        <?php foreach ($wali_kelas_list as $wali): ?>
            <option value="<?= $wali['id'] ?>" 
                    <?= (isset($kelas['wali_kelas_id']) && $kelas['wali_kelas_id'] == $wali['id']) ? 'selected' : '' ?>>
                <?= htmlspecialchars($wali['nama_lengkap']) ?> (<?= htmlspecialchars($wali['username']) ?>)
            </option>
        <?php endforeach; ?>
    <?php endif; ?>
</select>
```

### 5. Perbaikan Controller Kelas

**File:** `app/controllers/KelasController.php`

```php
public function create() {
    // ... existing code ...
    
    // Get wali kelas list
    require_once __DIR__ . '/../models/User.php';
    $userModel = new User();
    $waliKelasList = $userModel->getUsersByRole('wali_kelas');
    
    $data = [
        'title' => 'Tambah Kelas',
        'wali_kelas_list' => $waliKelasList,
        // ... other data
    ];
}

// Handle POST data
$wali_kelas_id = (int)($_POST['wali_kelas_id'] ?? 0);

if ($nama_kelas && $tingkat && $tahun_pelajaran && $wali_kelas_id) {
    $data = [
        'nama_kelas' => $nama_kelas,
        'tingkat' => $tingkat,
        'kurikulum' => $kurikulum,
        'tahun_pelajaran' => $tahun_pelajaran,
        'wali_kelas' => $wali_kelas,
        'wali_kelas_id' => $wali_kelas_id,
        'kapasitas' => $kapasitas,
        'created_by' => $_SESSION['user_id'] ?? 1
    ];
}
```

## 🧪 Testing dan Verifikasi

### File Testing
1. **`diagnosa_wali_kelas_rbac.php`** - Diagnosa masalah awal
2. **`fix_wali_kelas_database.php`** - Script perbaikan database
3. **`test_wali_kelas_rbac_fixed.php`** - Verifikasi perbaikan

### Langkah Testing
1. **Jalankan diagnosa** untuk mengidentifikasi masalah
2. **Jalankan script perbaikan** untuk memperbaiki database
3. **Jalankan test verifikasi** untuk memastikan perbaikan berhasil

## 🚀 Cara Menjalankan Perbaikan

### Langkah 1: Backup Database
```bash
mysqldump -u root -p siswa_app > backup_before_rbac_fix.sql
```

### Langkah 2: Jalankan Diagnosa
```
http://localhost/siswa-app/diagnosa_wali_kelas_rbac.php
```

### Langkah 3: Jalankan Perbaikan
```
http://localhost/siswa-app/fix_wali_kelas_database.php
```

### Langkah 4: Verifikasi Perbaikan
```
http://localhost/siswa-app/test_wali_kelas_rbac_fixed.php
```

### Langkah 5: Test Manual
1. Login sebagai wali kelas (contoh: `wali_kpa` / `wali123`)
2. Akses halaman Daftar Siswa
3. Verifikasi hanya siswa di kelas yang dikelola yang tampil
4. Akses halaman Data Kelas
5. Verifikasi hanya kelas yang dikelola yang tampil

## 📊 Hasil Setelah Perbaikan

### ✅ Yang Sudah Diperbaiki
- **Database schema** sudah benar dengan kolom `wali_kelas_id`
- **Relasi user-kelas** sudah terdefinisi dengan baik
- **Model Kelas dan Siswa** sudah filter berdasarkan role
- **Form kelas** sudah bisa assign wali kelas dengan dropdown
- **Controller** sudah handle `wali_kelas_id` dengan benar

### 🎯 Fitur yang Berfungsi
- **Wali kelas hanya melihat siswa di kelasnya**
- **Wali kelas hanya melihat kelas yang dikelola**
- **Form kelas bisa assign wali kelas**
- **Sistem RBAC berfungsi otomatis**

## 👥 User Wali Kelas untuk Testing

| Username | Password | Nama Lengkap | Kelas |
|----------|----------|--------------|-------|
| wali_kpa | wali123 | Pak Budi Santoso | KPA |
| wali_x1 | wali123 | Pak Ahmad Wijaya | X-IPA-1 |
| wali_x2 | wali123 | Pak Rizki Pratama | X-IPA-2 |
| wali_xi1 | wali123 | Pak Dani Kurniawan | XI-IPA-1 |
| wali_xii1 | wali123 | Pak Fajar Nugroho | XII-IPA-1 |

## 🔄 Maintenance

### Menambah Wali Kelas Baru
1. Buat user dengan role `wali_kelas`
2. Edit kelas dan assign wali kelas baru
3. Test login dan akses data

### Mengubah Assignment Kelas
1. Edit kelas yang ingin diubah
2. Pilih wali kelas baru dari dropdown
3. Save perubahan

### Monitoring
- Gunakan script test untuk verifikasi berkala
- Monitor log error untuk masalah akses
- Pastikan semua kelas memiliki wali kelas

## 🎉 Kesimpulan

**Sistem RBAC Wali Kelas sudah berhasil diperbaiki!**

- ✅ **Masalah database** sudah diselesaikan
- ✅ **Model dan Controller** sudah diperbaiki
- ✅ **Form assignment** sudah berfungsi
- ✅ **Testing** sudah dilakukan dan berhasil

Sekarang wali kelas hanya dapat mengakses data siswa dan kelas yang mereka kelola, sesuai dengan prinsip RBAC yang benar.
