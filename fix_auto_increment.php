<?php
require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>Fixing AUTO_INCREMENT for siswa table</h2>";
    
    $db = new Database();
    
    echo "<p>1. Checking current table structure...</p>";
    
    $columns = $db->fetchAll("SHOW COLUMNS FROM siswa WHERE Field = 'id_siswa'");
    
    if (empty($columns)) {
        throw new Exception("Column 'id_siswa' not found in 'siswa' table.");
    }
    
    $currentExtra = $columns[0]['Extra'];
    
    if (strpos($currentExtra, 'auto_increment') !== false) {
        echo "<p><strong>✓ AUTO_INCREMENT is already set for 'id_siswa'. No changes needed.</strong></p>";
        exit;
    }
    
    echo "<p>2. Applying AUTO_INCREMENT to 'id_siswa' column...</p>";
    
    $sql_drop = "ALTER TABLE siswa DROP PRIMARY KEY";
    $sql_modify = "ALTER TABLE siswa MODIFY COLUMN id_siswa INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY";
    
    try {
        $db->query($sql_drop);
        echo "<p>✓ Dropped existing primary key.</p>";
    } catch (Exception $e) {
        // Ignore if no primary key exists to be dropped
        echo "<p>Notice: Could not drop primary key, likely because it doesn't exist. " . $e->getMessage() . "</p>";
    }

    $db->query($sql_modify);
    
    echo "<p><strong>✓ Successfully applied AUTO_INCREMENT to 'id_siswa'.</strong></p>";
    
    echo "<p>3. Verifying updated table structure...</p>";
    
    $updatedColumns = $db->fetchAll("SHOW CREATE TABLE siswa");
    echo "<pre>" . htmlspecialchars($updatedColumns[0]['Create Table']) . "</pre>";
    
    echo "<h3>✅ Process complete!</h3>";
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
