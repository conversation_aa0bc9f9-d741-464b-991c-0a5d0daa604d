<?php
/**
 * Test Berkas Upload dengan Login
 */

require_once 'app/models/Database.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/User.php';
require_once 'app/models/Siswa.php';

// Initialize session manager
$sessionManager = new SimpleSessionManager();
$userModel = new User();
$siswaModel = new Siswa();

echo "<h2>🔐 Test Berkas Upload dengan Login</h2>";

// Try different passwords
$passwords = ['password', 'admin123', 'admin'];
$result = null;

foreach ($passwords as $password) {
    echo "<p>🔑 Trying password: <code>$password</code></p>";
    $result = $userModel->authenticate('admin', $password);
    if ($result['success']) {
        echo "<p style='color: green;'>✅ Login successful with password: <code>$password</code></p>";
        break;
    } else {
        echo "<p style='color: red;'>❌ Failed with password: <code>$password</code></p>";
    }
}

if ($result['success']) {
    $sessionManager->loginUser($result['user']);
    echo "<p style='color: green;'>✅ Login berhasil sebagai: " . htmlspecialchars($result['user']['nama_lengkap']) . "</p>";
    
    // Get sample siswa
    $siswaList = $siswaModel->getAll();
    if (!empty($siswaList)) {
        $siswa = $siswaList[0];
        $siswaId = $siswa['id_siswa'] ?? $siswa['id'];
        
        echo "<p>📚 Testing dengan siswa: <strong>" . htmlspecialchars($siswa['nama_lengkap']) . "</strong> (ID: $siswaId)</p>";
        
        // Test URLs
        $testUrls = [
            "http://localhost/siswa-app/public/berkas/upload/$siswaId",
            "http://localhost/siswa-app/public/upload/berkas/$siswaId"
        ];
        
        echo "<h3>Test dengan Session Valid</h3>";
        
        foreach ($testUrls as $url) {
            echo "<p><strong>Testing:</strong> $url</p>";
            
            // Get session cookie
            $sessionId = session_id();
            $sessionName = session_name();
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_COOKIE, "$sessionName=$sessionId");
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode == 302) {
                // Extract Location header
                if (preg_match('/Location: (.+)/', $response, $matches)) {
                    $location = trim($matches[1]);
                    if (strpos($location, '/upload/berkas/') !== false) {
                        echo "<p style='color: green;'>✅ Redirect ke URL yang benar: <code>$location</code></p>";
                    } else {
                        echo "<p style='color: blue;'>🔄 Redirect ke: <code>$location</code></p>";
                    }
                } else {
                    echo "<p style='color: blue;'>🔄 Redirect (302) detected</p>";
                }
            } elseif ($httpCode == 200) {
                echo "<p style='color: green;'>✅ OK (200) - Halaman berhasil dimuat</p>";
            } elseif ($httpCode == 404) {
                echo "<p style='color: red;'>❌ Not Found (404)</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ HTTP Code: $httpCode</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Tidak ada data siswa untuk testing</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Login gagal: " . htmlspecialchars($result['message']) . "</p>";
}

echo "<hr>";
echo "<p><a href='public/'>Kembali ke Aplikasi</a></p>";
echo "<p><a href='simple_login.php'>Simple Login</a></p>";
?>
