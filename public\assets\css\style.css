/* ===================================
   SISWA APP - CUSTOM STYLES
   Version: 2.0 with Security Enhancement
   =================================== */

/* ===== ROOT VARIABLES ===== */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --white-color: #ffffff;

    --border-radius: 0.35rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --transition: all 0.3s ease;
}

/* ===== GLOBAL STYLES ===== */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
}

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

/* ===== NAVIGATION ===== */
.navbar-brand {
    font-weight: 800;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    font-weight: 600;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: var(--white-color);
    border-bottom: 1px solid #e3e6f0;
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #224abe);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #224abe, #1e3a8a);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #17a673);
    border: none;
}

.btn-info {
    background: linear-gradient(45deg, var(--info-color), #2c9faf);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #dda20a);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
    border: none;
}

/* ===== FORMS ===== */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #d1d3e2;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background-color: #f8f9fc;
    border-color: #d1d3e2;
}

/* ===== ALERTS ===== */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* ===== TABLES ===== */
.table {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--primary-color);
    color: var(--white-color);
    font-weight: 700;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fc;
}

.table tbody td {
    padding: 1rem;
    border-color: #e3e6f0;
}

/* ===== ACADEMIC YEAR SELECTOR ===== */
.academic-year-selector {
    display: flex;
    align-items: center;
}

.academic-year-selector .dropdown-toggle {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.academic-year-selector .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.academic-year-selector .dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.academic-year-selector .dropdown-toggle::after {
    margin-left: 0.5rem;
}

.academic-year-selector .dropdown-menu {
    min-width: 300px;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
}

.academic-year-selector .dropdown-header {
    font-weight: 600;
    color: var(--dark-color);
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
}

.academic-year-selector .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: var(--transition);
}

.academic-year-selector .dropdown-item:hover {
    background-color: var(--light-color);
}

.academic-year-selector .dropdown-item.active {
    background-color: var(--primary-color);
    color: white;
}

.academic-year-selector .dropdown-item.active:hover {
    background-color: #224abe;
}

.academic-year-selector .badge {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
}

.academic-year-selector .dropdown-divider {
    margin: 0.5rem 0;
}

/* ===== FOOTER STYLES ===== */
.bg-gradient-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    position: relative;
    overflow: hidden;
}

.bg-gradient-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.footer-section {
    position: relative;
    z-index: 1;
}

.footer-section h5,
.footer-section h6 {
    color: #ffffff;
    margin-bottom: 1rem;
    position: relative;
}

.footer-section h5::after,
.footer-section h6::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    border-radius: 1px;
}

.footer-links li {
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.footer-links li:hover {
    transform: translateX(5px);
}

.footer-links a {
    transition: var(--transition);
    display: flex;
    align-items: center;
    padding: 0.25rem 0;
}

.footer-links a:hover {
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

.footer-links a i {
    width: 20px;
    transition: var(--transition);
}

.footer-links a:hover i {
    color: var(--info-color);
    transform: scale(1.1);
}

.footer-features li {
    transition: var(--transition);
    padding: 0.25rem 0;
}

.footer-features li:hover {
    transform: translateX(3px);
}

.footer-features .bi-check-circle {
    transition: var(--transition);
}

.footer-features li:hover .bi-check-circle {
    transform: scale(1.2);
    color: var(--success-color) !important;
}

.system-info .info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.system-info .info-item:last-child {
    border-bottom: none;
}

.system-info .info-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding-left: 0.5rem;
    margin-left: -0.5rem;
}

.text-light-emphasis {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Footer badges */
.footer-section .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    border-radius: 20px;
    font-weight: 500;
    transition: var(--transition);
}

.footer-section .badge:hover {
    transform: scale(1.05);
}

/* Footer bottom border */
.border-secondary {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Responsive footer */
@media (max-width: 768px) {
    .footer-section {
        text-align: center;
        margin-bottom: 2rem;
    }

    .footer-section h5::after,
    .footer-section h6::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links a,
    .footer-features li {
        justify-content: center;
    }

    .system-info {
        text-align: center;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .academic-year-selector {
        margin-bottom: 0.5rem;
    }

    .academic-year-selector .dropdown-menu {
        min-width: 280px;
    }
}

/* ===== PAGINATION STYLES ===== */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: var(--primary-color);
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.pagination .page-link:hover {
    color: var(--white-color);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white-color);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: var(--secondary-color);
    background-color: var(--light-color);
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
}

/* Per page selector */
.form-select-sm {
    font-size: 0.875rem;
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-select-sm:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Pagination info text */
.pagination-info {
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-weight: 500;
}

/* Responsive pagination */
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 1px;
    }

    .pagination .page-item:not(.active):not(.disabled) .page-link {
        display: none;
    }

    .pagination .page-item.active .page-link,
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link,
    .pagination .page-item:nth-child(2) .page-link,
    .pagination .page-item:nth-last-child(2) .page-link {
        display: block;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.footer-section {
    animation: fadeInUp 0.6s ease-out;
}

.footer-section:nth-child(1) { animation-delay: 0.1s; }
.footer-section:nth-child(2) { animation-delay: 0.2s; }
.footer-section:nth-child(3) { animation-delay: 0.3s; }
.footer-section:nth-child(4) { animation-delay: 0.4s; }