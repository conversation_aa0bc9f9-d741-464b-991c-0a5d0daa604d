<?php
// Error reporting - Enhanced for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Load dependencies manually (since we're not using PSR-4 namespaces)
// Check if vendor/autoload.php exists, if not, skip it
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

// Load core classes in correct order
require_once __DIR__ . '/../app/models/Database.php';
require_once __DIR__ . '/../app/helpers/Security.php';
require_once __DIR__ . '/../app/helpers/SimpleSessionManager.php';

// Initialize session manager first (this will configure and start session)
$sessionManager = new SimpleSessionManager();

// Load other models and controllers
require_once __DIR__ . '/../app/models/User.php';
require_once __DIR__ . '/../app/models/Siswa.php';
require_once __DIR__ . '/../app/models/Kelas.php';
require_once __DIR__ . '/../app/models/Berkas.php';
require_once __DIR__ . '/../app/controllers/AuthController.php';
require_once __DIR__ . '/../app/controllers/DashboardController.php';
require_once __DIR__ . '/../app/controllers/SiswaController.php';
require_once __DIR__ . '/../app/controllers/KelasController.php';
require_once __DIR__ . '/../app/controllers/UploadController.php';
require_once __DIR__ . '/../app/controllers/BerkasController.php';
require_once __DIR__ . '/../app/controllers/CatatanController.php';
require_once __DIR__ . '/../app/controllers/UserManagementController.php';
require_once __DIR__ . '/../app/controllers/AbsensiController.php';
require_once __DIR__ . '/../app/controllers/AcademicYearController.php';
require_once __DIR__ . '/../app/controllers/AcademicYearManagementController.php';

// Parse URL and handle subdirectory
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if running in subdirectory
$basePath = '/siswa-app';
if (strpos($uri, $basePath) === 0) {
    $uri = substr($uri, strlen($basePath));
}

// Remove /public if present
if (strpos($uri, '/public') === 0) {
    $uri = substr($uri, 7);
}

$uri = explode('/', trim($uri, '/'));

// Debug output (remove in production)
// echo "Debug URI: " . print_r($uri, true) . "<br>";

// Default redirect
if (empty($uri[0]) || $uri[0] === '') {
    if (Security::isAuthenticated()) {
        header('Location: ' . $basePath . '/public/dashboard');
    } else {
        header('Location: ' . $basePath . '/public/login');
    }
    exit;
}

$controller = null;
$action = $uri[1] ?? 'index';
$id = $uri[2] ?? null;

// Route handling
switch ($uri[0]) {
    // Authentication routes (no auth required)
    case 'login':
        $controller = new AuthController();
        $action = 'login';
        break;

    case 'auth':
        $controller = new AuthController();
        $action = $uri[1] ?? 'login';
        // Handle POST requests for login
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'login') {
            $action = 'processLogin';
        }
        break;

    case 'logout':
        $controller = new AuthController();
        $action = 'logout';
        break;

    case 'unauthorized':
        $controller = new AuthController();
        $action = 'unauthorized';
        break;

    // Protected routes (auth required)
    case 'dashboard':
        Security::requireAuth();
        $controller = new DashboardController();
        $action = 'index';
        break;

    case 'siswa':
        Security::requireAuth();
        $controller = new SiswaController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable
        // Handle siswa actions with parameters
        if (in_array($action, ['edit', 'detail', 'delete']) && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'upload-foto' && isset($uri[2])) {
            $action = 'uploadFoto';
            $id = $uri[2];
        } elseif ($action === 'delete-foto' && isset($uri[2])) {
            $action = 'deleteFoto';
            $id = $uri[2];
        }
        break;

    case 'kelas':
        Security::requireAuth();
        $controller = new KelasController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable
        // Handle kelas actions with parameters
        if (in_array($action, ['edit', 'detail', 'delete']) && isset($uri[2])) {
            $id = $uri[2];
        }
        break;

    case 'upload':
        Security::requireAuth();
        $controller = new UploadController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable
        // Handle upload actions with parameters
        if ($action === 'berkas' && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'delete' && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'download' && isset($uri[2])) {
            $id = $uri[2];
        }
        break;

    case 'berkas':
        Security::requireAuth();
        $controller = new BerkasController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable
        // Handle berkas actions with parameters
        if ($action === 'upload' && isset($uri[2])) {
            // Redirect to correct upload route
            header('Location: /siswa-app/public/upload/berkas/' . $uri[2]);
            exit;
        } elseif ($action === 'download' && isset($uri[2])) {
            // Use UploadController for download
            $uploadController = new UploadController();
            $uploadController->download($uri[2]);
            exit;
        } elseif ($action === 'delete' && isset($uri[2])) {
            // Use UploadController for delete
            $uploadController = new UploadController();
            $uploadController->delete($uri[2]);
            exit;
        } elseif ($action === 'index' && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'category' && isset($uri[2]) && isset($uri[3])) {
            $id = $uri[2]; // siswa_id
            $category = $uri[3]; // category name
        }
        break;

    case 'catatan':
        Security::requireAuth();
        $controller = new CatatanController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable
        // Handle catatan actions with parameters
        if (in_array($action, ['add', 'create', 'edit', 'delete', 'detail']) && isset($uri[2])) {
            $id = $uri[2];
        } elseif ($action === 'index' && isset($uri[2])) {
            $id = $uri[2]; // siswa_id
        }
        break;

    case 'profile':
        Security::requireAuth();
        $controller = new AuthController();
        $action = 'profile';
        break;

    case 'users':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new AuthController();
        $action = 'users';
        break;

    case 'register':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new AuthController();
        $action = 'register';
        break;

    case 'admin':
        Security::requireAuth();
        Security::requireRole('admin');
        $controller = new UserManagementController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable

        // Handle admin routes
        if ($action === 'users') {
            $subAction = $uri[2] ?? 'index';
            if ($subAction === 'create') {
                $action = 'create';
            } elseif ($subAction === 'store') {
                $action = 'store';
            } elseif ($subAction === 'edit' && isset($uri[3])) {
                $action = 'edit';
                $id = $uri[3];
            } elseif ($subAction === 'update' && isset($uri[3])) {
                $action = 'update';
                $id = $uri[3];
            } elseif ($subAction === 'reset-password' && isset($uri[3])) {
                $action = 'resetPassword';
                $id = $uri[3];
            } elseif ($subAction === 'delete' && isset($uri[3])) {
                $action = 'delete';
                $id = $uri[3];
            } else {
                $action = 'index';
            }
        }
        break;

    case 'absensi':
        Security::requireAuth();
        $controller = new AbsensiController();
        $action = $uri[1] ?? 'index';
        $id = null; // Initialize $id variable

        // Handle absensi routes
        if ($action === 'add' && isset($uri[2])) {
            $action = 'add';
            $id = $uri[2];
        } elseif ($action === 'create') {
            $action = 'create';
        } elseif ($action === 'edit' && isset($uri[2])) {
            $action = 'edit';
            $id = $uri[2];
        } elseif ($action === 'update' && isset($uri[2])) {
            $action = 'update';
            $id = $uri[2];
        } elseif ($action === 'delete' && isset($uri[2])) {
            $action = 'delete';
            $id = $uri[2];
        }
        break;

    case 'academic-year':
        Security::requireAuth();
        $controller = new AcademicYearController();
        $action = $uri[1] ?? 'current';

        // Handle academic year routes
        if ($action === 'change') {
            $action = 'change';
        } elseif ($action === 'reset') {
            $action = 'reset';
        } elseif ($action === 'current') {
            $action = 'current';
        }
        break;

    case 'academic-year-management':
        Security::requireAuth();
        Security::requireRole(['admin', 'staff']);
        $controller = new AcademicYearManagementController();
        $action = $uri[1] ?? 'index';

        // Handle academic year management routes
        if ($action === 'add') {
            $action = 'add';
        } elseif ($action === 'generate') {
            $action = 'generateYears';
        } elseif ($action === 'delete') {
            $action = 'delete';
        } else {
            $action = 'index';
        }
        break;

    // Static assets
    case 'assets':
        // Serve static files
        $filePath = __DIR__ . $_SERVER['REQUEST_URI'];
        if (file_exists($filePath)) {
            $mimeType = mime_content_type($filePath);
            header('Content-Type: ' . $mimeType);
            readfile($filePath);
        } else {
            http_response_code(404);
        }
        exit;

    default:
        http_response_code(404);
        echo "404 Not Found";
        exit;
}

// Execute controller action
if ($controller && method_exists($controller, $action)) {
    try {
        // Debug logging for troubleshooting
        error_log("Executing action: $action with ID: " . ($id ?? 'null') . " and category: " . ($category ?? 'null'));

        if (isset($category)) {
            // For berkas category action
            $controller->$action($id, $category);
        } elseif ($id !== null) {
            // Use !== null instead of truthy check to handle ID "0"
            $controller->$action($id);
        } else {
            $controller->$action();
        }
    } catch (Exception $e) {
        error_log("Controller error: " . $e->getMessage());
        error_log("Action: $action, ID: " . ($id ?? 'null') . ", Controller: " . get_class($controller));
        http_response_code(500);
        echo "Internal Server Error";
    }
} else {
    error_log("Controller or action not found. Controller: " . ($controller ? get_class($controller) : 'null') . ", Action: $action");
    http_response_code(404);
    echo "404 Not Found";
}
?>