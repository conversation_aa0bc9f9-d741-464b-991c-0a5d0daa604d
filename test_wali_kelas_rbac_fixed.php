<?php
/**
 * Script Test dan Verifikasi Sistem RBAC Wali Kelas yang Sudah Diperbaiki
 * 
 * Script ini akan menguji apakah sistem RBAC wali kelas sudah berfungsi dengan benar
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/Siswa.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/models/User.php';

echo "<h1>🧪 Test Sistem RBAC Wali Kelas yang Sudah Diperbaiki</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Test yang Akan Dilakukan</h3>";
    echo "<ul>";
    echo "<li>Verifikasi struktur database sudah benar</li>";
    echo "<li>Test query getAllForWaliKelas() untuk setiap wali kelas</li>";
    echo "<li>Test query getForWaliKelas() di model Kelas</li>";
    echo "<li>Simulasi login sebagai wali kelas dan cek akses data</li>";
    echo "<li>Verifikasi form kelas sudah bisa assign wali kelas</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 1: Verifikasi struktur database
    echo "<h2>🔧 Test 1: Verifikasi Struktur Database</h2>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM kelas LIKE 'wali_kelas_id'");
    $hasWaliKelasId = $stmt->rowCount() > 0;
    
    if ($hasWaliKelasId) {
        echo "<p style='color: green;'>✅ Kolom wali_kelas_id ada di tabel kelas</p>";
        
        // Cek foreign key constraint
        $stmt = $pdo->query("
            SELECT CONSTRAINT_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'kelas' 
            AND COLUMN_NAME = 'wali_kelas_id' 
            AND REFERENCED_TABLE_NAME = 'users'
        ");
        $hasForeignKey = $stmt->rowCount() > 0;
        
        if ($hasForeignKey) {
            echo "<p style='color: green;'>✅ Foreign key constraint ke tabel users ada</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint tidak ditemukan (opsional)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Kolom wali_kelas_id tidak ada di tabel kelas</p>";
        echo "<p>Silakan jalankan script fix_wali_kelas_database.php terlebih dahulu.</p>";
        exit;
    }
    
    // Test 2: Cek data wali kelas
    echo "<h2>👨‍🏫 Test 2: Data Wali Kelas</h2>";
    
    $stmt = $pdo->query("SELECT id, username, nama_lengkap FROM users WHERE role = 'wali_kelas' AND is_active = 1");
    $waliKelasUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($waliKelasUsers)) {
        echo "<p style='color: red;'>❌ Tidak ada user dengan role wali_kelas</p>";
        echo "<p>Silakan jalankan script fix_wali_kelas_database.php terlebih dahulu.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Ditemukan " . count($waliKelasUsers) . " user wali kelas</p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Kelas yang Dikelola</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($waliKelasUsers as $wali) {
        // Cek kelas yang dikelola
        $stmt = $pdo->prepare("SELECT nama_kelas FROM kelas WHERE wali_kelas_id = ? AND is_active = 1");
        $stmt->execute([$wali['id']]);
        $kelasYangDikelola = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<tr>";
        echo "<td>{$wali['id']}</td>";
        echo "<td>{$wali['username']}</td>";
        echo "<td>{$wali['nama_lengkap']}</td>";
        echo "<td>" . (empty($kelasYangDikelola) ? '<span style="color: red;">Belum ada</span>' : implode(', ', $kelasYangDikelola)) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Test 3: Test model Siswa getAllForWaliKelas()
    echo "<h2>📚 Test 3: Model Siswa - getAllForWaliKelas()</h2>";
    
    $siswaModel = new Siswa();
    
    foreach ($waliKelasUsers as $wali) {
        echo "<h4>Test untuk {$wali['nama_lengkap']} ({$wali['username']}):</h4>";
        
        // Simulasi session untuk wali kelas ini
        $_SESSION['user_id'] = $wali['id'];
        $_SESSION['user_role'] = 'wali_kelas';
        
        $siswaList = $siswaModel->getAllForWaliKelas();
        
        echo "<p><strong>Jumlah siswa yang dapat diakses:</strong> " . count($siswaList) . "</p>";
        
        if (!empty($siswaList)) {
            echo "<p style='color: green;'>✅ Query berhasil mengembalikan data siswa</p>";
            
            // Tampilkan 3 siswa pertama sebagai sample
            echo "<p><strong>Sample siswa:</strong></p>";
            echo "<ul>";
            $count = 0;
            foreach ($siswaList as $siswa) {
                if ($count >= 3) break;
                echo "<li>{$siswa['nama_lengkap']} - {$siswa['nama_kelas']}</li>";
                $count++;
            }
            if (count($siswaList) > 3) {
                echo "<li><em>... dan " . (count($siswaList) - 3) . " siswa lainnya</em></li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada siswa yang dapat diakses (mungkin belum ada siswa di kelas yang dikelola)</p>";
        }
        
        echo "<hr style='margin: 10px 0;'>";
    }
    
    // Test 4: Test model Kelas getForWaliKelas()
    echo "<h2>🏫 Test 4: Model Kelas - getForWaliKelas()</h2>";
    
    $kelasModel = new Kelas();
    
    foreach ($waliKelasUsers as $wali) {
        echo "<h4>Test untuk {$wali['nama_lengkap']} ({$wali['username']}):</h4>";
        
        // Simulasi session untuk wali kelas ini
        $_SESSION['user_id'] = $wali['id'];
        $_SESSION['user_role'] = 'wali_kelas';
        
        $kelasList = $kelasModel->getForWaliKelas();
        
        echo "<p><strong>Jumlah kelas yang dapat diakses:</strong> " . count($kelasList) . "</p>";
        
        if (!empty($kelasList)) {
            echo "<p style='color: green;'>✅ Query berhasil mengembalikan data kelas</p>";
            
            echo "<ul>";
            foreach ($kelasList as $kelas) {
                echo "<li>{$kelas['nama_kelas']} - {$kelas['tahun_pelajaran']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada kelas yang dapat diakses</p>";
        }
        
        echo "<hr style='margin: 10px 0;'>";
    }
    
    // Test 5: Test integrasi dengan getAll() yang sudah dimodifikasi
    echo "<h2>🔄 Test 5: Integrasi Model dengan RBAC</h2>";
    
    foreach ($waliKelasUsers as $wali) {
        echo "<h4>Test getAll() untuk {$wali['nama_lengkap']}:</h4>";
        
        // Simulasi session untuk wali kelas ini
        $_SESSION['user_id'] = $wali['id'];
        $_SESSION['user_role'] = 'wali_kelas';
        
        // Test model Kelas getAll() (seharusnya otomatis filter untuk wali kelas)
        $kelasListAll = $kelasModel->getAll();
        echo "<p><strong>Kelas dari getAll():</strong> " . count($kelasListAll) . " kelas</p>";
        
        // Test model Siswa getAll() (seharusnya otomatis filter untuk wali kelas)
        $siswaListAll = $siswaModel->getAll();
        echo "<p><strong>Siswa dari getAll():</strong> " . count($siswaListAll) . " siswa</p>";
        
        if (count($kelasListAll) > 0 && count($siswaListAll) > 0) {
            echo "<p style='color: green;'>✅ Integrasi RBAC berfungsi dengan baik</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Mungkin belum ada data atau perlu assignment kelas</p>";
        }
        
        echo "<hr style='margin: 10px 0;'>";
    }
    
    // Test 6: Verifikasi form kelas
    echo "<h2>📝 Test 6: Verifikasi Form Kelas</h2>";
    
    $userModel = new User();
    $waliKelasListForForm = $userModel->getUsersByRole('wali_kelas');
    
    if (!empty($waliKelasListForForm)) {
        echo "<p style='color: green;'>✅ Method getUsersByRole('wali_kelas') berfungsi</p>";
        echo "<p><strong>Data untuk dropdown form:</strong></p>";
        echo "<ul>";
        foreach ($waliKelasListForForm as $wali) {
            echo "<li>ID: {$wali['id']} - {$wali['nama_lengkap']} ({$wali['username']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Method getUsersByRole('wali_kelas') tidak mengembalikan data</p>";
    }
    
    // Test 7: Ringkasan dan rekomendasi
    echo "<h2>📋 Test 7: Ringkasan dan Rekomendasi</h2>";
    
    $totalKelasWithWali = 0;
    $totalKelasWithoutWali = 0;
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_kelas,
            COUNT(wali_kelas_id) as kelas_with_wali
        FROM kelas 
        WHERE is_active = 1
    ");
    $kelasStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $totalKelasWithWali = $kelasStats['kelas_with_wali'];
    $totalKelasWithoutWali = $kelasStats['total_kelas'] - $kelasStats['kelas_with_wali'];
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Statistik Sistem:</h4>";
    echo "<ul>";
    echo "<li><strong>Total User Wali Kelas:</strong> " . count($waliKelasUsers) . "</li>";
    echo "<li><strong>Total Kelas Aktif:</strong> {$kelasStats['total_kelas']}</li>";
    echo "<li><strong>Kelas dengan Wali Kelas:</strong> {$totalKelasWithWali}</li>";
    echo "<li><strong>Kelas tanpa Wali Kelas:</strong> {$totalKelasWithoutWali}</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($totalKelasWithoutWali > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Rekomendasi:</h4>";
        echo "<p>Masih ada {$totalKelasWithoutWali} kelas yang belum memiliki wali kelas.</p>";
        echo "<p>Silakan assign wali kelas melalui form edit kelas atau jalankan script fix_wali_kelas_database.php lagi.</p>";
        echo "</div>";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎉 Status Sistem RBAC Wali Kelas:</h4>";
    
    $allTestsPassed = true;
    
    if (!$hasWaliKelasId) {
        echo "<p style='color: red;'>❌ Struktur database belum benar</p>";
        $allTestsPassed = false;
    }
    
    if (empty($waliKelasUsers)) {
        echo "<p style='color: red;'>❌ Belum ada user wali kelas</p>";
        $allTestsPassed = false;
    }
    
    if (empty($waliKelasListForForm)) {
        echo "<p style='color: red;'>❌ Form kelas belum bisa assign wali kelas</p>";
        $allTestsPassed = false;
    }
    
    if ($allTestsPassed) {
        echo "<p style='color: green; font-size: 1.2em;'><strong>✅ SISTEM RBAC WALI KELAS SUDAH BERFUNGSI DENGAN BAIK!</strong></p>";
        echo "<p>Wali kelas sekarang hanya dapat mengakses siswa dan kelas yang mereka kelola.</p>";
    } else {
        echo "<p style='color: red; font-size: 1.2em;'><strong>❌ SISTEM RBAC WALI KELAS MASIH BERMASALAH</strong></p>";
        echo "<p>Silakan perbaiki masalah di atas terlebih dahulu.</p>";
    }
    echo "</div>";
    
    // Reset session
    unset($_SESSION['user_id'], $_SESSION['user_role']);
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
