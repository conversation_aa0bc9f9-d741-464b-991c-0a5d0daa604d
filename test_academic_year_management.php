<?php
/**
 * Test Script untuk Manajemen <PERSON>
 * Script ini akan menguji fitur penambahan dan pengelolaan tahun ajaran
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/helpers/AcademicYear.php';

echo "<h1>🧪 Test Manajemen <PERSON></h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Checking Current System</h2>";
    
    // Check current academic year
    $currentYear = AcademicYear::getCurrentAcademicYear();
    echo "<p style='color: green;'>✅ Current Academic Year: <strong>{$currentYear}</strong></p>";
    
    // Check available years
    $availableYears = AcademicYear::getAvailableAcademicYears();
    echo "<p style='color: green;'>✅ Available Years: " . count($availableYears) . " years</p>";
    foreach ($availableYears as $year) {
        $isCurrent = AcademicYear::isCurrentAcademicYear($year) ? ' (Current)' : '';
        echo "<p style='margin-left: 20px;'>📅 {$year}{$isCurrent}</p>";
    }
    
    echo "<h2>🔧 Testing Academic Year Helper Functions</h2>";
    
    // Test validation
    $testYears = [
        '2023/2024' => true,
        '2024/2025' => true,
        '2023-2024' => false,
        '23/24' => false,
        '2024/2023' => false, // Invalid order
        'invalid' => false
    ];
    
    foreach ($testYears as $testYear => $expected) {
        $result = AcademicYear::isValidAcademicYear($testYear);
        $status = ($result === $expected) ? '✅' : '❌';
        $expectedText = $expected ? 'Valid' : 'Invalid';
        echo "<p>{$status} Testing '{$testYear}': Expected {$expectedText}, Got " . ($result ? 'Valid' : 'Invalid') . "</p>";
    }
    
    echo "<h2>📊 Database Statistics</h2>";
    
    // Get statistics for each available year
    foreach ($availableYears as $year) {
        echo "<h3>📅 Tahun Ajaran: {$year}</h3>";
        
        // Count students
        $siswaCount = $db->fetch("
            SELECT COUNT(*) as count
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.tahun_pelajaran = ?
        ", [$year]);

        // Count active classes
        $kelasAktifCount = $db->fetch("
            SELECT COUNT(*) as count
            FROM kelas
            WHERE tahun_pelajaran = ?
            AND is_active = 1
            AND tingkat > 0
        ", [$year]);

        // Count total classes
        $kelasTotalCount = $db->fetch("
            SELECT COUNT(*) as count
            FROM kelas
            WHERE tahun_pelajaran = ?
            AND tingkat > 0
        ", [$year]);

        // Count placeholder entries
        $placeholderCount = $db->fetch("
            SELECT COUNT(*) as count
            FROM kelas
            WHERE tahun_pelajaran = ?
            AND nama_kelas LIKE 'PLACEHOLDER_%'
            AND tingkat = 0
        ", [$year]);
        
        echo "<div style='margin-left: 20px;'>";
        echo "<p>👥 Total Siswa: <strong>" . ($siswaCount['count'] ?? 0) . "</strong></p>";
        echo "<p>🏫 Kelas Aktif: <strong>" . ($kelasAktifCount['count'] ?? 0) . "</strong></p>";
        echo "<p>📚 Total Kelas: <strong>" . ($kelasTotalCount['count'] ?? 0) . "</strong></p>";
        echo "<p>🔧 Placeholder Entries: <strong>" . ($placeholderCount['count'] ?? 0) . "</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>🧪 Testing Year Generation</h2>";
    
    // Test previous and next year functions
    $testYear = '2023/2024';
    $previousYear = AcademicYear::getPreviousAcademicYear($testYear);
    $nextYear = AcademicYear::getNextAcademicYear($testYear);
    
    echo "<p>📅 Base Year: <strong>{$testYear}</strong></p>";
    echo "<p>⬅️ Previous Year: <strong>{$previousYear}</strong></p>";
    echo "<p>➡️ Next Year: <strong>{$nextYear}</strong></p>";
    
    // Test year options generation
    echo "<h3>🎛️ Dropdown Options Generation</h3>";
    $options = AcademicYear::generateAcademicYearOptions();
    foreach ($options as $option) {
        $badges = [];
        if ($option['current']) $badges[] = 'Current';
        if ($option['selected']) $badges[] = 'Selected';
        $badgeText = !empty($badges) ? ' (' . implode(', ', $badges) . ')' : '';
        echo "<p style='margin-left: 20px;'>📋 {$option['text']}{$badgeText}</p>";
    }
    
    echo "<h2>🔍 Testing Sample Year Addition</h2>";
    
    // Test adding a sample year (if not exists)
    $sampleYear = '2022/2023';
    if (!in_array($sampleYear, $availableYears)) {
        try {
            $db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                'PLACEHOLDER_' . str_replace('/', '_', $sampleYear),
                0,
                $sampleYear,
                'System Generated (Test)',
                0,
                false,
                1 // Assuming user ID 1 exists
            ]);
            echo "<p style='color: green;'>✅ Successfully added test year: {$sampleYear}</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Could not add test year: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Test year {$sampleYear} already exists</p>";
    }
    
    echo "<h2>🎯 Feature Testing Summary</h2>";
    
    // Check if management page would work
    $requiredFunctions = [
        'getCurrentAcademicYear' => method_exists('AcademicYear', 'getCurrentAcademicYear'),
        'getAvailableAcademicYears' => method_exists('AcademicYear', 'getAvailableAcademicYears'),
        'isValidAcademicYear' => method_exists('AcademicYear', 'isValidAcademicYear'),
        'isCurrentAcademicYear' => method_exists('AcademicYear', 'isCurrentAcademicYear'),
        'generateAcademicYearOptions' => method_exists('AcademicYear', 'generateAcademicYearOptions')
    ];
    
    echo "<h3>🔧 Required Functions Check</h3>";
    foreach ($requiredFunctions as $function => $exists) {
        $status = $exists ? '✅' : '❌';
        echo "<p>{$status} {$function}: " . ($exists ? 'Available' : 'Missing') . "</p>";
    }
    
    // Check database structure
    echo "<h3>🗄️ Database Structure Check</h3>";
    try {
        $kelasStructure = $db->fetchAll("DESCRIBE kelas");
        $hasRequiredFields = false;
        foreach ($kelasStructure as $field) {
            if ($field['Field'] === 'tahun_pelajaran') {
                $hasRequiredFields = true;
                break;
            }
        }
        echo "<p>" . ($hasRequiredFields ? '✅' : '❌') . " kelas.tahun_pelajaran field: " . ($hasRequiredFields ? 'Available' : 'Missing') . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database structure check failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/academic-year-management' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Open Academic Year Management Page
    </a></p>";
    
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Test Academic Year Selector (Student List)
    </a></p>";
    
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Test Academic Year Selector (Class List)
    </a></p>";
    
    echo "<h2>📝 Usage Instructions</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎯 How to Use Academic Year Management:</h4>";
    echo "<ol>";
    echo "<li><strong>Access:</strong> Go to Manajemen → Manajemen Tahun Ajaran (Admin/Staff only)</li>";
    echo "<li><strong>Add Single Year:</strong> Click 'Tambah Tahun Ajaran' and enter format YYYY/YYYY</li>";
    echo "<li><strong>Bulk Generate:</strong> Click 'Generate Bulk' to create multiple years at once (Admin only)</li>";
    echo "<li><strong>View Statistics:</strong> See student and class counts for each year</li>";
    echo "<li><strong>Delete Empty Years:</strong> Remove years with no data (Admin only)</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>💡 Tips:</h4>";
    echo "<ul>";
    echo "<li>Academic years follow format: 2023/2024 (July 2023 - June 2024)</li>";
    echo "<li>Current year is automatically determined by system date</li>";
    echo "<li>Years with data cannot be deleted for safety</li>";
    echo "<li>Use bulk generate to quickly add historical years</li>";
    echo "<li>Academic year selector in navbar allows switching between years</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>✅ Test Complete</h2>";
    echo "<p style='color: green; font-weight: bold;'>
        Academic Year Management system is ready for use! 
        All required functions are available and the database structure is correct.
    </p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
