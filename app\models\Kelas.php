<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/../helpers/Security.php';

class Kelas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Get all active classes
     */
    public function getAll($academicYear = null) {
        try {
            // Get selected academic year if not provided
            if ($academicYear === null) {
                require_once __DIR__ . '/../helpers/AcademicYear.php';
                $academicYear = AcademicYear::getSelectedAcademicYear();
            }

            // Filter by role
            require_once __DIR__ . '/../helpers/Security.php';
            if (Security::isWaliKelas()) {
                return $this->getForWaliKelas($academicYear);
            }

            return $this->db->fetchAll("
                SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
                FROM kelas
                WHERE is_active = 1 AND tahun_pelajaran = ?
                ORDER BY
                    CASE tingkat
                        WHEN 'KPP' THEN 1  -- <PERSON><PERSON> Persiapan Pertama
                        WHEN 'X' THEN 2    -- <PERSON>las 10
                        WHEN 'XI' THEN 3   -- Kelas 11
                        WHEN 'XII' THEN 4  -- Kelas 12
                        WHEN 'KPA' THEN 5  -- Kelas Persiapan Atas
                        ELSE 6
                    END,
                    nama_kelas
            ", [$academicYear]);
        } catch (Exception $e) {
            error_log("Error in Kelas::getAll(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get classes for wali kelas (only their classes)
     * Supports both old 'wali_kelas' role and new specific roles like 'wali_kelas_x_1'
     */
    public function getForWaliKelas($academicYear = null) {
        try {
            require_once __DIR__ . '/../helpers/Security.php';

            if (!Security::isWaliKelas()) {
                return [];
            }

            // Get selected academic year if not provided
            if ($academicYear === null) {
                require_once __DIR__ . '/../helpers/AcademicYear.php';
                $academicYear = AcademicYear::getSelectedAcademicYear();
            }

            $userId = $_SESSION['user_id'] ?? 0;
            $userRole = $_SESSION['user_role'] ?? '';

            // For specific wali kelas roles (wali_kelas_x_1, etc), filter by role-based class name
            if (strpos($userRole, 'wali_kelas_') === 0) {
                $className = Security::getWaliKelasClass(); // Returns 'X-1', 'KPP-A', etc

                return $this->db->fetchAll("
                    SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
                    FROM kelas
                    WHERE is_active = 1
                    AND tahun_pelajaran = ?
                    AND nama_kelas = ?
                    ORDER BY
                        CASE tingkat
                            WHEN 'KPP' THEN 1  -- Kelas Persiapan Pertama
                            WHEN 'X' THEN 2    -- Kelas 10
                            WHEN 'XI' THEN 3   -- Kelas 11
                            WHEN 'XII' THEN 4  -- Kelas 12
                            WHEN 'KPA' THEN 5  -- Kelas Persiapan Atas
                            ELSE 6
                        END,
                        nama_kelas
                ", [$academicYear, $className]);
            }

            // Fallback for old 'wali_kelas' role - use wali_kelas_id
            return $this->db->fetchAll("
                SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
                FROM kelas
                WHERE is_active = 1
                AND tahun_pelajaran = ?
                AND wali_kelas_id = ?
                ORDER BY
                    CASE tingkat
                        WHEN 'KPP' THEN 1  -- Kelas Persiapan Pertama
                        WHEN 'X' THEN 2    -- Kelas 10
                        WHEN 'XI' THEN 3   -- Kelas 11
                        WHEN 'XII' THEN 4  -- Kelas 12
                        WHEN 'KPA' THEN 5  -- Kelas Persiapan Atas
                        ELSE 6
                    END,
                    nama_kelas
            ", [$academicYear, $userId]);
        } catch (Exception $e) {
            error_log("Error in Kelas::getForWaliKelas(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all active classes without academic year filter (for admin purposes)
     */
    public function getAllWithoutFilter() {
        try {
            return $this->db->fetchAll("
                SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
                FROM kelas
                WHERE is_active = 1
                ORDER BY tahun_pelajaran DESC,
                    CASE tingkat
                        WHEN 'KPP' THEN 1  -- Kelas Persiapan Pertama
                        WHEN 'X' THEN 2    -- Kelas 10
                        WHEN 'XI' THEN 3   -- Kelas 11
                        WHEN 'XII' THEN 4  -- Kelas 12
                        WHEN 'KPA' THEN 5  -- Kelas Persiapan Atas
                        ELSE 6
                    END,
                    nama_kelas
            ");
        } catch (Exception $e) {
            error_log("Error in Kelas::getAllWithoutFilter(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get class by ID
     */
    public function getById($id) {
        try {
            return $this->db->fetch("
                SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
                FROM kelas
                WHERE id = ?
            ", [$id]);
        } catch (Exception $e) {
            error_log("Error in Kelas::getById(): " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get classes by academic year
     */
    public function getByAcademicYear($academicYear) {
        try {
            return $this->db->fetchAll("
                SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
                FROM kelas
                WHERE is_active = 1 AND tahun_pelajaran = ?
                ORDER BY
                    CASE tingkat
                        WHEN 'KPP' THEN 1
                        WHEN 'X' THEN 2
                        WHEN 'XI' THEN 3
                        WHEN 'XII' THEN 4
                        WHEN 'KPA' THEN 5
                        ELSE 6
                    END,
                    nama_kelas
            ", [$academicYear]);
        } catch (Exception $e) {
            error_log("Error in Kelas::getByAcademicYear(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get class count by academic year
     */
    public function getCountByAcademicYear($academicYear) {
        try {
            $result = $this->db->fetch("
                SELECT COUNT(*) as total
                FROM kelas
                WHERE is_active = 1 AND tahun_pelajaran = ?
            ", [$academicYear]);

            return $result ? (int)$result['total'] : 0;
        } catch (Exception $e) {
            error_log("Error in Kelas::getCountByAcademicYear(): " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Create new class
     */
    public function create($nama_kelas) {
        try {
            $this->db->query("INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran) VALUES (?, 10, '2024/2025')", [$nama_kelas]);
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Error in Kelas::create(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create new class with full data
     */
    public function createKelas($data) {
        try {
            // Check for duplicate class name in the same academic year
            $existingClass = $this->db->fetch("
                SELECT id FROM kelas
                WHERE nama_kelas = ? AND tahun_pelajaran = ? AND is_active = 1
            ", [$data['nama_kelas'], $data['tahun_pelajaran']]);

            if ($existingClass) {
                throw new Exception("Kelas dengan nama '{$data['nama_kelas']}' sudah ada untuk tahun pelajaran {$data['tahun_pelajaran']}");
            }

            $sql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

            $params = [
                $data['nama_kelas'],
                $data['tingkat'],
                $data['kurikulum'] ?? null,
                $data['tahun_pelajaran'],
                $data['wali_kelas'] ?? null,
                (int)($data['kapasitas'] ?? 30),
                $data['created_by'] ?? 1
            ];

            $this->db->query($sql, $params);

            $kelasId = $this->db->lastInsertId();

            // Log activity (optional - comment out if causing issues)
            try {
                if (method_exists('Security', 'logSecurityEvent')) {
                    Security::logSecurityEvent('kelas_created', [
                        'kelas_id' => $kelasId,
                        'nama_kelas' => $data['nama_kelas'],
                        'created_by' => $data['created_by'] ?? 1
                    ]);
                }
            } catch (Exception $logError) {
                // Log error but don't fail the operation
                error_log("Security logging failed: " . $logError->getMessage());
            }

            return $kelasId;
        } catch (Exception $e) {
            error_log("Error in Kelas::createKelas(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update class
     */
    public function updateKelas($id, $data) {
        try {
            // Check for duplicate class name in the same academic year (excluding current class)
            $existingClass = $this->db->fetch("
                SELECT id FROM kelas
                WHERE nama_kelas = ? AND tahun_pelajaran = ? AND is_active = 1 AND id != ?
            ", [$data['nama_kelas'], $data['tahun_pelajaran'], $id]);

            if ($existingClass) {
                throw new Exception("Kelas dengan nama '{$data['nama_kelas']}' sudah ada untuk tahun pelajaran {$data['tahun_pelajaran']}");
            }

            $sql = "UPDATE kelas SET
                    nama_kelas = ?,
                    tingkat = ?,
                    kurikulum = ?,
                    tahun_pelajaran = ?,
                    wali_kelas = ?,
                    kapasitas = ?,
                    updated_by = ?,
                    updated_at = NOW()
                    WHERE id = ?";

            $params = [
                $data['nama_kelas'],
                $data['tingkat'],
                $data['kurikulum'] ?? null,
                $data['tahun_pelajaran'],
                $data['wali_kelas'] ?? null,
                (int)($data['kapasitas'] ?? 30),
                $data['updated_by'] ?? 1,
                $id
            ];

            $this->db->query($sql, $params);

            // Log activity (optional - comment out if causing issues)
            try {
                if (method_exists('Security', 'logSecurityEvent')) {
                    Security::logSecurityEvent('kelas_updated', [
                        'kelas_id' => $id,
                        'nama_kelas' => $data['nama_kelas'],
                        'updated_by' => $data['updated_by'] ?? 1
                    ]);
                }
            } catch (Exception $logError) {
                // Log error but don't fail the operation
                error_log("Security logging failed: " . $logError->getMessage());
            }

            return true;
        } catch (Exception $e) {
            error_log("Error in Kelas::updateKelas(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete class (soft delete)
     */
    public function deleteKelas($id) {
        try {
            // Check if there are students in this class
            $studentCount = $this->db->fetch("SELECT COUNT(*) as count FROM siswa WHERE kelas_id = ?", [$id]);

            if ($studentCount && $studentCount['count'] > 0) {
                throw new Exception("Tidak dapat menghapus kelas yang masih memiliki siswa.");
            }

            // Soft delete
            $this->db->query("UPDATE kelas SET is_active = 0, updated_at = NOW() WHERE id = ?", [$id]);

            // Log activity (optional - comment out if causing issues)
            try {
                if (method_exists('Security', 'logSecurityEvent')) {
                    Security::logSecurityEvent('kelas_deleted', [
                        'kelas_id' => $id,
                        'deleted_by' => $_SESSION['user_id'] ?? 1
                    ]);
                }
            } catch (Exception $logError) {
                // Log error but don't fail the operation
                error_log("Security logging failed: " . $logError->getMessage());
            }

            return true;
        } catch (Exception $e) {
            error_log("Error in Kelas::deleteKelas(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get class statistics
     */
    public function getStatistics() {
        try {
            $stats = [];

            // Total classes
            $total = $this->db->fetch("SELECT COUNT(*) as count FROM kelas WHERE is_active = 1");
            $stats['total_kelas'] = $total['count'] ?? 0;

            // Classes by level
            $byLevel = $this->db->fetchAll("
                SELECT tingkat, COUNT(*) as count
                FROM kelas
                WHERE is_active = 1
                GROUP BY tingkat
                ORDER BY tingkat
            ");
            $stats['by_level'] = $byLevel;

            // Classes with students count
            $withStudents = $this->db->fetchAll("
                SELECT k.nama_kelas, k.tingkat, COUNT(s.id_siswa) as student_count, k.kapasitas
                FROM kelas k
                LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
                WHERE k.is_active = 1
                GROUP BY k.id, k.nama_kelas, k.tingkat, k.kapasitas
                ORDER BY k.tingkat, k.nama_kelas
            ");
            $stats['with_students'] = $withStudents;

            return $stats;
        } catch (Exception $e) {
            error_log("Error in Kelas::getStatistics(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get classes for dropdown/select options
     */
    public function getForSelect() {
        try {
            return $this->db->fetchAll("
                SELECT id as value, CONCAT(nama_kelas, ' (', tingkat, ')') as label
                FROM kelas
                WHERE is_active = 1
                ORDER BY
                    CASE tingkat
                        WHEN 'KPP' THEN 1
                        WHEN 'X' THEN 2
                        WHEN 'XI' THEN 3
                        WHEN 'XII' THEN 4
                        WHEN 'KPA' THEN 5
                        ELSE 6
                    END,
                    nama_kelas
            ");
        } catch (Exception $e) {
            error_log("Error in Kelas::getForSelect(): " . $e->getMessage());
            return [];
        }
    }
}
?>