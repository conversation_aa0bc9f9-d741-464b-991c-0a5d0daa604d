<?php
/**
 * Script untuk menambahkan field identitas siswa baru
 * Menambahkan: NIK, No KK, <PERSON>al <PERSON>, <PERSON><PERSON>rok<PERSON>, <PERSON><PERSON><PERSON>
 */

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>Menambahkan Field Identitas Siswa Baru</h2>";
    
    $db = new Database();
    
    echo "<p>1. Memeriksa struktur tabel siswa saat ini...</p>";
    
    // Cek apakah field sudah ada
    $columns = $db->fetchAll("SHOW COLUMNS FROM siswa");
    $existingColumns = array_column($columns, 'Field');
    
    $newFields = ['nik', 'no_kk', 'asal_sekolah', 'asal_paroki', 'golongan_darah'];
    $fieldsToAdd = [];
    
    foreach ($newFields as $field) {
        if (!in_array($field, $existingColumns)) {
            $fieldsToAdd[] = $field;
        } else {
            echo "<p>✓ Field '$field' sudah ada</p>";
        }
    }
    
    if (empty($fieldsToAdd)) {
        echo "<p><strong>Semua field sudah ada. Tidak perlu menambahkan field baru.</strong></p>";
        exit;
    }
    
    echo "<p>2. Menambahkan field baru: " . implode(', ', $fieldsToAdd) . "</p>";
    
    // Menambahkan field satu per satu untuk menghindari error
    $alterQueries = [];
    
    if (in_array('nik', $fieldsToAdd)) {
        $alterQueries[] = "ALTER TABLE siswa ADD COLUMN nik VARCHAR(16) UNIQUE AFTER nisn";
    }
    
    if (in_array('no_kk', $fieldsToAdd)) {
        $alterQueries[] = "ALTER TABLE siswa ADD COLUMN no_kk VARCHAR(16) AFTER " . 
                         (in_array('nik', $fieldsToAdd) ? 'nik' : 'nisn');
    }
    
    if (in_array('asal_sekolah', $fieldsToAdd)) {
        $alterQueries[] = "ALTER TABLE siswa ADD COLUMN asal_sekolah VARCHAR(100) AFTER pekerjaan_ibu";
    }
    
    if (in_array('asal_paroki', $fieldsToAdd)) {
        $alterQueries[] = "ALTER TABLE siswa ADD COLUMN asal_paroki VARCHAR(100) AFTER asal_sekolah";
    }
    
    if (in_array('golongan_darah', $fieldsToAdd)) {
        $alterQueries[] = "ALTER TABLE siswa ADD COLUMN golongan_darah ENUM('A', 'B', 'AB', 'O', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') AFTER asal_paroki";
    }
    
    // Eksekusi query
    foreach ($alterQueries as $query) {
        try {
            $db->query($query);
            echo "<p>✓ Berhasil: " . $query . "</p>";
        } catch (Exception $e) {
            echo "<p>✗ Error: " . $e->getMessage() . "</p>";
            echo "<p>Query: " . $query . "</p>";
        }
    }
    
    echo "<p>3. Menambahkan index untuk optimasi pencarian...</p>";
    
    // Menambahkan index
    $indexQueries = [];
    
    if (in_array('nik', $fieldsToAdd)) {
        $indexQueries[] = "ALTER TABLE siswa ADD INDEX idx_nik (nik)";
    }
    
    if (in_array('no_kk', $fieldsToAdd)) {
        $indexQueries[] = "ALTER TABLE siswa ADD INDEX idx_no_kk (no_kk)";
    }
    
    if (in_array('asal_sekolah', $fieldsToAdd)) {
        $indexQueries[] = "ALTER TABLE siswa ADD INDEX idx_asal_sekolah (asal_sekolah)";
    }
    
    if (in_array('asal_paroki', $fieldsToAdd)) {
        $indexQueries[] = "ALTER TABLE siswa ADD INDEX idx_asal_paroki (asal_paroki)";
    }
    
    foreach ($indexQueries as $query) {
        try {
            $db->query($query);
            echo "<p>✓ Index berhasil ditambahkan</p>";
        } catch (Exception $e) {
            echo "<p>✗ Error menambahkan index: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>4. Menambahkan komentar untuk dokumentasi...</p>";
    
    // Menambahkan komentar
    $commentQueries = [];
    
    if (in_array('nik', $fieldsToAdd)) {
        $commentQueries[] = "ALTER TABLE siswa MODIFY COLUMN nik VARCHAR(16) UNIQUE COMMENT 'Nomor Induk Kependudukan'";
    }
    
    if (in_array('no_kk', $fieldsToAdd)) {
        $commentQueries[] = "ALTER TABLE siswa MODIFY COLUMN no_kk VARCHAR(16) COMMENT 'Nomor Kartu Keluarga'";
    }
    
    if (in_array('asal_sekolah', $fieldsToAdd)) {
        $commentQueries[] = "ALTER TABLE siswa MODIFY COLUMN asal_sekolah VARCHAR(100) COMMENT 'Sekolah asal sebelum masuk'";
    }
    
    if (in_array('asal_paroki', $fieldsToAdd)) {
        $commentQueries[] = "ALTER TABLE siswa MODIFY COLUMN asal_paroki VARCHAR(100) COMMENT 'Paroki asal siswa'";
    }
    
    if (in_array('golongan_darah', $fieldsToAdd)) {
        $commentQueries[] = "ALTER TABLE siswa MODIFY COLUMN golongan_darah ENUM('A', 'B', 'AB', 'O', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') COMMENT 'Golongan darah siswa'";
    }
    
    foreach ($commentQueries as $query) {
        try {
            $db->query($query);
            echo "<p>✓ Komentar berhasil ditambahkan</p>";
        } catch (Exception $e) {
            echo "<p>✗ Error menambahkan komentar: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>5. Memeriksa struktur tabel siswa setelah update...</p>";
    
    $updatedColumns = $db->fetchAll("SHOW COLUMNS FROM siswa");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($updatedColumns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>✅ Proses selesai!</h3>";
    echo "<p><strong>Field identitas siswa baru berhasil ditambahkan:</strong></p>";
    echo "<ul>";
    echo "<li>NIK (Nomor Induk Kependudukan) - VARCHAR(16) UNIQUE</li>";
    echo "<li>No KK (Nomor Kartu Keluarga) - VARCHAR(16)</li>";
    echo "<li>Asal Sekolah - VARCHAR(100)</li>";
    echo "<li>Asal Paroki - VARCHAR(100)</li>";
    echo "<li>Golongan Darah - ENUM dengan pilihan lengkap</li>";
    echo "</ul>";
    
    echo "<p><strong>Langkah selanjutnya:</strong></p>";
    echo "<ol>";
    echo "<li>Update form input siswa untuk menambahkan field baru</li>";
    echo "<li>Update model Siswa untuk handle field baru</li>";
    echo "<li>Update view detail siswa untuk menampilkan field baru</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Pastikan database sudah terhubung dan tabel siswa sudah ada.</p>";
}
?>
