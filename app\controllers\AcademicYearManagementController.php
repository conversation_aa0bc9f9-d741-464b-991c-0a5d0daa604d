<?php
require_once __DIR__ . '/../helpers/AcademicYear.php';
require_once __DIR__ . '/../helpers/Security.php';
require_once __DIR__ . '/../models/Database.php';

class AcademicYearManagementController {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Display academic year management page
     */
    public function index() {
        Security::requireAuth();
        Security::requireRole(['admin', 'staff']);

        $availableYears = AcademicYear::getAvailableAcademicYears();
        $currentYear = AcademicYear::getCurrentAcademicYear();
        
        // Get statistics for each year
        $yearStats = [];
        foreach ($availableYears as $year) {
            $stats = $this->getYearStatistics($year);
            $yearStats[$year] = $stats;
        }

        $data = [
            'title' => 'Manajemen Ta<PERSON>',
            'available_years' => $availableYears,
            'current_year' => $currentYear,
            'year_stats' => $yearStats,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('academic_year/management', $data);
    }

    /**
     * Add new academic year
     */
    public function add() {
        Security::requireAuth();
        Security::requireRole(['admin', 'staff']);

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/academic-year-management');
            exit;
        }

        try {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                throw new Exception('Token keamanan tidak valid');
            }

            $academicYear = trim($_POST['academic_year'] ?? '');
            
            if (empty($academicYear)) {
                throw new Exception('Tahun ajaran tidak boleh kosong');
            }

            if (!AcademicYear::isValidAcademicYear($academicYear)) {
                throw new Exception('Format tahun ajaran tidak valid. Gunakan format YYYY/YYYY (contoh: 2023/2024)');
            }

            // Check if year already exists
            $existingYears = AcademicYear::getAvailableAcademicYears();
            if (in_array($academicYear, $existingYears)) {
                throw new Exception('Tahun ajaran ' . $academicYear . ' sudah ada');
            }

            // Create a dummy class entry to register the academic year
            // This is needed because the system gets available years from the kelas table
            $this->db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                'PLACEHOLDER_' . str_replace('/', '_', $academicYear),
                0, // Special tingkat for placeholder
                $academicYear,
                'System Generated',
                0,
                false, // Not active
                $_SESSION['user_id']
            ]);

            $_SESSION['success'] = 'Tahun ajaran ' . $academicYear . ' berhasil ditambahkan';

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/academic-year-management');
        exit;
    }

    /**
     * Generate multiple academic years
     */
    public function generateYears() {
        Security::requireAuth();
        Security::requireRole(['admin']);

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/academic-year-management');
            exit;
        }

        try {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                throw new Exception('Token keamanan tidak valid');
            }

            $startYear = (int)($_POST['start_year'] ?? 0);
            $endYear = (int)($_POST['end_year'] ?? 0);
            
            if ($startYear <= 0 || $endYear <= 0) {
                throw new Exception('Tahun mulai dan tahun akhir harus diisi dengan benar');
            }

            if ($startYear > $endYear) {
                throw new Exception('Tahun mulai tidak boleh lebih besar dari tahun akhir');
            }

            if (($endYear - $startYear) > 20) {
                throw new Exception('Maksimal 20 tahun ajaran dapat dibuat sekaligus');
            }

            $existingYears = AcademicYear::getAvailableAcademicYears();
            $addedYears = [];
            $skippedYears = [];

            for ($year = $startYear; $year <= $endYear; $year++) {
                $academicYear = $year . '/' . ($year + 1);
                
                if (in_array($academicYear, $existingYears)) {
                    $skippedYears[] = $academicYear;
                    continue;
                }

                // Create placeholder entry
                $this->db->query("
                    INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ", [
                    'PLACEHOLDER_' . str_replace('/', '_', $academicYear),
                    0,
                    $academicYear,
                    'System Generated',
                    0,
                    false,
                    $_SESSION['user_id']
                ]);

                $addedYears[] = $academicYear;
            }

            $message = '';
            if (!empty($addedYears)) {
                $message .= 'Berhasil menambahkan ' . count($addedYears) . ' tahun ajaran: ' . implode(', ', $addedYears);
            }
            if (!empty($skippedYears)) {
                if (!empty($message)) $message .= '. ';
                $message .= 'Dilewati ' . count($skippedYears) . ' tahun ajaran yang sudah ada: ' . implode(', ', $skippedYears);
            }

            $_SESSION['success'] = $message;

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/academic-year-management');
        exit;
    }

    /**
     * Delete academic year (with confirmation)
     */
    public function delete() {
        Security::requireAuth();
        Security::requireRole(['admin']);

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/academic-year-management');
            exit;
        }

        try {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                throw new Exception('Token keamanan tidak valid');
            }

            $academicYear = $_POST['academic_year'] ?? '';
            
            if (empty($academicYear)) {
                throw new Exception('Tahun ajaran tidak boleh kosong');
            }

            // Check if it's current year
            if (AcademicYear::isCurrentAcademicYear($academicYear)) {
                throw new Exception('Tidak dapat menghapus tahun ajaran yang sedang berjalan');
            }

            // Check if there are students or classes using this year
            $stats = $this->getYearStatistics($academicYear);
            if ($stats['total_siswa'] > 0 || $stats['total_kelas_aktif'] > 0) {
                throw new Exception('Tidak dapat menghapus tahun ajaran yang masih memiliki data siswa atau kelas aktif');
            }

            // Delete placeholder entries
            $this->db->query("
                DELETE FROM kelas 
                WHERE tahun_pelajaran = ? 
                AND nama_kelas LIKE 'PLACEHOLDER_%' 
                AND tingkat = 0 
                AND is_active = 0
            ", [$academicYear]);

            $_SESSION['success'] = 'Tahun ajaran ' . $academicYear . ' berhasil dihapus';

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/academic-year-management');
        exit;
    }

    /**
     * Get statistics for a specific academic year
     */
    private function getYearStatistics($academicYear) {
        try {
            // Count students
            $siswaCount = $this->db->fetch("
                SELECT COUNT(*) as count
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE k.tahun_pelajaran = ?
            ", [$academicYear]);

            // Count active classes
            $kelasAktifCount = $this->db->fetch("
                SELECT COUNT(*) as count
                FROM kelas
                WHERE tahun_pelajaran = ?
                AND is_active = 1
                AND tingkat > 0
            ", [$academicYear]);

            // Count total classes (including inactive)
            $kelasTotalCount = $this->db->fetch("
                SELECT COUNT(*) as count
                FROM kelas
                WHERE tahun_pelajaran = ?
                AND tingkat > 0
            ", [$academicYear]);

            return [
                'total_siswa' => (int)($siswaCount['count'] ?? 0),
                'total_kelas_aktif' => (int)($kelasAktifCount['count'] ?? 0),
                'total_kelas' => (int)($kelasTotalCount['count'] ?? 0),
                'is_current' => AcademicYear::isCurrentAcademicYear($academicYear),
                'has_data' => ((int)($siswaCount['count'] ?? 0) > 0 || (int)($kelasAktifCount['count'] ?? 0) > 0)
            ];

        } catch (Exception $e) {
            error_log("Error getting year statistics: " . $e->getMessage());
            return [
                'total_siswa' => 0,
                'total_kelas_aktif' => 0,
                'total_kelas' => 0,
                'is_current' => false,
                'has_data' => false
            ];
        }
    }

    /**
     * Load view
     */
    private function view($view, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $view . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>
