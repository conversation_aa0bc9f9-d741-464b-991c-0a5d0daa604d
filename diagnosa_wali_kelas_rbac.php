<?php
/**
 * Script Diagnosa Masalah RBAC Wali Kelas
 * 
 * Script ini akan menganalisis dan mengidentifikasi masalah dalam sistem role wali kelas
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';

echo "<h1>🔍 Diagnosa Masalah RBAC Wali Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📋 Analisis Sistem RBAC Wali Kelas</h3>";
    echo "<p>Script ini akan mengecek:</p>";
    echo "<ul>";
    echo "<li>Struktur tabel users dan kelas</li>";
    echo "<li>Data wali kelas yang ada</li>";
    echo "<li>Relasi antara user dan kelas</li>";
    echo "<li>Mapping user_kelas_mapping</li>";
    echo "</ul>";
    echo "</div>";
    
    // 1. Cek struktur tabel users
    echo "<h2>👥 1. Struktur Tabel Users</h2>";
    
    $stmt = $pdo->query("DESCRIBE users");
    $userColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($userColumns as $column) {
        $highlight = $column['Field'] === 'role' ? "style='background: #fff3cd;'" : "";
        echo "<tr {$highlight}>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // 2. Cek struktur tabel kelas
    echo "<h2>🏫 2. Struktur Tabel Kelas</h2>";
    
    $stmt = $pdo->query("DESCRIBE kelas");
    $kelasColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    echo "</thead><tbody>";
    
    $hasWaliKelasId = false;
    foreach ($kelasColumns as $column) {
        if ($column['Field'] === 'wali_kelas_id') {
            $hasWaliKelasId = true;
        }
        $highlight = in_array($column['Field'], ['wali_kelas', 'wali_kelas_id']) ? "style='background: #fff3cd;'" : "";
        echo "<tr {$highlight}>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    if (!$hasWaliKelasId) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ MASALAH DITEMUKAN!</h4>";
        echo "<p>Kolom <code>wali_kelas_id</code> tidak ditemukan di tabel kelas.</p>";
        echo "<p>Ini adalah penyebab utama masalah RBAC wali kelas.</p>";
        echo "</div>";
    }
    
    // 3. Cek data users dengan role wali_kelas
    echo "<h2>👨‍🏫 3. Data Users dengan Role Wali Kelas</h2>";
    
    $stmt = $pdo->query("SELECT id, username, nama_lengkap, role FROM users WHERE role = 'wali_kelas'");
    $waliKelasUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($waliKelasUsers)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ PERINGATAN!</h4>";
        echo "<p>Tidak ada user dengan role 'wali_kelas' ditemukan.</p>";
        echo "<p>Mungkin role belum diset atau menggunakan role lain.</p>";
        echo "</div>";
        
        // Cek semua role yang ada
        $stmt = $pdo->query("SELECT DISTINCT role FROM users");
        $allRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>Role yang tersedia:</strong> " . implode(', ', $allRoles) . "</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Role</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasUsers as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><span style='background: #28a745; color: white; padding: 2px 8px; border-radius: 3px;'>{$user['role']}</span></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    }
    
    // 4. Cek data kelas dan relasi wali kelas
    echo "<h2>🏫 4. Data Kelas dan Relasi Wali Kelas</h2>";
    
    if ($hasWaliKelasId) {
        $stmt = $pdo->query("
            SELECT k.id, k.nama_kelas, k.tingkat, k.wali_kelas, k.wali_kelas_id, 
                   u.username, u.nama_lengkap as wali_nama_lengkap
            FROM kelas k
            LEFT JOIN users u ON k.wali_kelas_id = u.id
            WHERE k.is_active = 1
            ORDER BY k.tingkat, k.nama_kelas
        ");
    } else {
        $stmt = $pdo->query("
            SELECT k.id, k.nama_kelas, k.tingkat, k.wali_kelas
            FROM kelas k
            WHERE k.is_active = 1
            ORDER BY k.tingkat, k.nama_kelas
        ");
    }
    
    $kelasData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($kelasData)) {
        echo "<p>Tidak ada data kelas ditemukan.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Wali Kelas (Text)</th>";
        if ($hasWaliKelasId) {
            echo "<th>Wali Kelas ID</th><th>Username</th><th>Nama Lengkap</th>";
        }
        echo "</tr></thead><tbody>";
        
        $kelasWithoutWaliId = 0;
        foreach ($kelasData as $kelas) {
            $rowStyle = "";
            if ($hasWaliKelasId && empty($kelas['wali_kelas_id'])) {
                $kelasWithoutWaliId++;
                $rowStyle = "style='background: #f8d7da;'";
            }
            
            echo "<tr {$rowStyle}>";
            echo "<td>{$kelas['id']}</td>";
            echo "<td>{$kelas['nama_kelas']}</td>";
            echo "<td>{$kelas['tingkat']}</td>";
            echo "<td>" . ($kelas['wali_kelas'] ?? '-') . "</td>";
            
            if ($hasWaliKelasId) {
                echo "<td>" . ($kelas['wali_kelas_id'] ?? '<span style="color: red;">NULL</span>') . "</td>";
                echo "<td>" . ($kelas['username'] ?? '-') . "</td>";
                echo "<td>" . ($kelas['wali_nama_lengkap'] ?? '-') . "</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        if ($hasWaliKelasId && $kelasWithoutWaliId > 0) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ MASALAH DITEMUKAN!</h4>";
            echo "<p>{$kelasWithoutWaliId} kelas tidak memiliki wali_kelas_id yang valid.</p>";
            echo "<p>Ini menyebabkan wali kelas tidak bisa mengakses siswa di kelas mereka.</p>";
            echo "</div>";
        }
    }
    
    // 5. Cek tabel user_kelas_mapping
    echo "<h2>🔗 5. Tabel User Kelas Mapping</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM user_kelas_mapping");
        $mappingCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        if ($mappingCount == 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ PERINGATAN!</h4>";
            echo "<p>Tabel user_kelas_mapping kosong atau tidak ada.</p>";
            echo "<p>Sistem mungkin menggunakan relasi langsung melalui wali_kelas_id.</p>";
            echo "</div>";
        } else {
            $stmt = $pdo->query("
                SELECT ukm.*, u.username, u.nama_lengkap, k.nama_kelas
                FROM user_kelas_mapping ukm
                JOIN users u ON ukm.user_id = u.id
                JOIN kelas k ON ukm.kelas_id = k.id
                WHERE ukm.is_active = 1
                ORDER BY u.nama_lengkap, k.nama_kelas
            ");
            $mappingData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Total mapping aktif:</strong> {$mappingCount}</p>";
            
            if (!empty($mappingData)) {
                echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<thead style='background: #f8f9fa;'>";
                echo "<tr><th>User</th><th>Kelas</th><th>Role Type</th><th>Status</th></tr>";
                echo "</thead><tbody>";
                
                foreach ($mappingData as $mapping) {
                    echo "<tr>";
                    echo "<td>{$mapping['nama_lengkap']} ({$mapping['username']})</td>";
                    echo "<td>{$mapping['nama_kelas']}</td>";
                    echo "<td>{$mapping['role_type']}</td>";
                    echo "<td>" . ($mapping['is_active'] ? 'Aktif' : 'Tidak Aktif') . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            }
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ ERROR!</h4>";
        echo "<p>Tabel user_kelas_mapping tidak ditemukan: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // 6. Test query yang digunakan di getAllForWaliKelas
    echo "<h2>🧪 6. Test Query getAllForWaliKelas</h2>";
    
    if (!empty($waliKelasUsers)) {
        $testUserId = $waliKelasUsers[0]['id'];
        $testUsername = $waliKelasUsers[0]['username'];
        
        echo "<p><strong>Testing dengan user:</strong> {$testUsername} (ID: {$testUserId})</p>";
        
        // Test query yang ada sekarang
        $stmt = $pdo->prepare("
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.wali_kelas_id = ?
            AND s.status_siswa = 'aktif'
            AND k.is_active = 1
            ORDER BY s.nama_lengkap
        ");
        $stmt->execute([$testUserId]);
        $siswaResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Hasil query saat ini:</strong> " . count($siswaResult) . " siswa ditemukan</p>";
        
        if (empty($siswaResult)) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ MASALAH KONFIRMASI!</h4>";
            echo "<p>Query tidak mengembalikan siswa untuk wali kelas {$testUsername}.</p>";
            echo "<p>Ini konfirmasi bahwa ada masalah dengan relasi wali_kelas_id.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>✅ QUERY BERHASIL!</h4>";
            echo "<p>Query berhasil mengembalikan " . count($siswaResult) . " siswa untuk wali kelas {$testUsername}.</p>";
            echo "</div>";
        }
    }
    
    // 7. Ringkasan masalah dan solusi
    echo "<h2>📋 7. Ringkasan Masalah dan Solusi</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔍 Masalah yang Ditemukan:</h4>";
    echo "<ul>";
    
    if (!$hasWaliKelasId) {
        echo "<li>❌ Kolom wali_kelas_id tidak ada di tabel kelas</li>";
    } else if ($kelasWithoutWaliId > 0) {
        echo "<li>❌ {$kelasWithoutWaliId} kelas tidak memiliki wali_kelas_id yang valid</li>";
    }
    
    if (empty($waliKelasUsers)) {
        echo "<li>❌ Tidak ada user dengan role 'wali_kelas'</li>";
    }
    
    echo "</ul>";
    
    echo "<h4>🔧 Solusi yang Diperlukan:</h4>";
    echo "<ol>";
    
    if (!$hasWaliKelasId) {
        echo "<li>Tambah kolom wali_kelas_id ke tabel kelas</li>";
        echo "<li>Buat foreign key constraint ke tabel users</li>";
    }
    
    if (empty($waliKelasUsers)) {
        echo "<li>Buat user dengan role 'wali_kelas' atau update role user yang ada</li>";
    }
    
    if ($hasWaliKelasId && $kelasWithoutWaliId > 0) {
        echo "<li>Assign wali_kelas_id untuk kelas yang belum memiliki wali kelas</li>";
    }
    
    echo "<li>Update form create/edit kelas untuk bisa assign wali kelas</li>";
    echo "<li>Test ulang sistem RBAC wali kelas</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Diagnosa selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
