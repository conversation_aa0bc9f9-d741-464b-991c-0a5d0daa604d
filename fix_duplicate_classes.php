<?php
/**
 * Fix Duplicate Classes
 * Script ini akan memperbaiki masalah kelas duplikat
 */

require_once __DIR__ . '/app/models/Database.php';

echo "<h1>🔧 Fix Duplicate Classes</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Current Duplicate Analysis</h2>";
    
    // Find duplicate classes
    $duplicates = $db->fetchAll("
        SELECT nama_kelas, tingkat, tahun_pelajaran, COUNT(*) as count,
               GROUP_CONCAT(id ORDER BY id) as ids,
               GROUP_CONCAT(created_at ORDER BY id) as created_dates
        FROM kelas 
        WHERE is_active = 1
        GROUP BY nama_kelas, tingkat, tahun_pelajaran
        HAVING count > 1
        ORDER BY nama_kelas
    ");
    
    if (!empty($duplicates)) {
        echo "<h3>⚠️ Duplicate Classes Found:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th><PERSON><PERSON></th><th>Tingkat</th><th>Tahun</th><th>Count</th><th>IDs</th><th>Action</th>";
        echo "</tr>";
        
        foreach ($duplicates as $dup) {
            echo "<tr>";
            echo "<td>{$dup['nama_kelas']}</td>";
            echo "<td>{$dup['tingkat']}</td>";
            echo "<td>{$dup['tahun_pelajaran']}</td>";
            echo "<td style='color: red; font-weight: bold;'>{$dup['count']}</td>";
            echo "<td>{$dup['ids']}</td>";
            echo "<td>Will keep oldest, remove others</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>🔧 Fixing Duplicates:</h3>";
        
        foreach ($duplicates as $dup) {
            $ids = explode(',', $dup['ids']);
            $keepId = $ids[0]; // Keep the first (oldest) one
            $removeIds = array_slice($ids, 1); // Remove the rest
            
            echo "<p><strong>{$dup['nama_kelas']} ({$dup['tingkat']}):</strong></p>";
            echo "<ul>";
            echo "<li>✅ Keeping ID: {$keepId}</li>";
            
            foreach ($removeIds as $removeId) {
                // Check if this class has any students
                $studentCount = $db->fetch("
                    SELECT COUNT(*) as count 
                    FROM siswa 
                    WHERE kelas_id = ? AND status_siswa = 'aktif'
                ", [$removeId]);
                
                if (($studentCount['count'] ?? 0) > 0) {
                    echo "<li>⚠️ ID {$removeId} has {$studentCount['count']} students - moving to ID {$keepId}</li>";
                    
                    // Move students to the kept class
                    $db->query("
                        UPDATE siswa 
                        SET kelas_id = ? 
                        WHERE kelas_id = ?
                    ", [$keepId, $removeId]);
                    
                    echo "<li>✅ Moved {$studentCount['count']} students from ID {$removeId} to ID {$keepId}</li>";
                } else {
                    echo "<li>ℹ️ ID {$removeId} has no students</li>";
                }
                
                // Soft delete the duplicate
                $db->query("
                    UPDATE kelas 
                    SET is_active = 0, updated_at = NOW() 
                    WHERE id = ?
                ", [$removeId]);
                
                echo "<li>🗑️ Soft deleted duplicate ID: {$removeId}</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ No duplicate classes found</p>";
    }
    
    echo "<h2>📊 Current Classes Status</h2>";
    
    // Show current active classes
    $activeClasses = $db->fetchAll("
        SELECT id, nama_kelas, tingkat, kurikulum, tahun_pelajaran, 
               (SELECT COUNT(*) FROM siswa WHERE kelas_id = kelas.id AND status_siswa = 'aktif') as student_count
        FROM kelas 
        WHERE is_active = 1 AND tahun_pelajaran = '2024/2025'
        ORDER BY 
            CASE tingkat
                WHEN 'KPP' THEN 1
                WHEN 'X' THEN 2
                WHEN 'XI' THEN 3
                WHEN 'XII' THEN 4
                WHEN 'KPA' THEN 5
                ELSE 6
            END,
            nama_kelas
    ");
    
    echo "<h3>Active Classes for 2024/2025:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Kurikulum</th><th>Students</th>";
    echo "</tr>";
    
    foreach ($activeClasses as $class) {
        echo "<tr>";
        echo "<td>{$class['id']}</td>";
        echo "<td>{$class['nama_kelas']}</td>";
        echo "<td>{$class['tingkat']}</td>";
        echo "<td>{$class['kurikulum']}</td>";
        echo "<td>{$class['student_count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🧪 Test Form with Fixed Data</h2>";
    
    // Test creating a new class with a unique name
    $testClassName = 'KPP-B-' . date('His');
    
    echo "<h3>Testing with unique name: {$testClassName}</h3>";
    
    $testData = [
        'nama_kelas' => $testClassName,
        'tingkat' => 'KPP',
        'kurikulum' => 'Kurikulum Seminari',
        'tahun_pelajaran' => '2024/2025',
        'wali_kelas' => 'Test Teacher',
        'kapasitas' => 25,
        'created_by' => 1
    ];
    
    // Test the model method
    require_once __DIR__ . '/app/models/Kelas.php';
    $kelasModel = new Kelas();
    
    try {
        $result = $kelasModel->createKelas($testData);
        if ($result) {
            echo "<p style='color: green;'>✅ Test class creation successful! ID: {$result}</p>";
            
            // Verify
            $createdClass = $db->fetch("SELECT * FROM kelas WHERE id = ?", [$result]);
            echo "<p>✅ Verified: Class '{$createdClass['nama_kelas']}' created successfully</p>";
            
            // Clean up
            $db->query("DELETE FROM kelas WHERE id = ?", [$result]);
            echo "<p style='color: blue;'>🧹 Test class cleaned up</p>";
        } else {
            echo "<p style='color: red;'>❌ Test class creation failed</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Test class creation error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>💡 Suggestions for Form Usage</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Recommended Class Names:</h4>";
    echo "<p>To avoid duplicates, use these naming patterns:</p>";
    echo "<ul>";
    echo "<li><strong>KPP Classes:</strong> KPP-A, KPP-B, KPP-1, KPP-2</li>";
    echo "<li><strong>Regular Classes:</strong> X-IPA-1, X-IPS-1, XI-IPA-1, XII-IPA-1</li>";
    echo "<li><strong>KPA Classes:</strong> KPA-A, KPA-B, KPA-1, KPA-2</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ Important Notes:</h4>";
    echo "<ul>";
    echo "<li>Each class name must be unique within the same academic year</li>";
    echo "<li>The system now checks for duplicates before creating</li>";
    echo "<li>You'll get a clear error message if the name already exists</li>";
    echo "<li>Consider using numbering or letters to differentiate similar classes</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔗 Test the Fixed Form</h2>";
    
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 Try Create Form</a></p>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 View Class List</a></p>";
    
    echo "<h2>✅ Summary</h2>";
    
    $duplicateCount = count($duplicates);
    $activeCount = count($activeClasses);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎉 Duplicate Fix Complete!</h4>";
    echo "<ul>";
    echo "<li><strong>Duplicates Found:</strong> {$duplicateCount}</li>";
    echo "<li><strong>Duplicates Fixed:</strong> {$duplicateCount}</li>";
    echo "<li><strong>Active Classes:</strong> {$activeCount}</li>";
    echo "<li><strong>Form Status:</strong> ✅ Ready to use</li>";
    echo "</ul>";
    echo "<p><strong>The form should now work correctly with unique class names!</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Fix Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
