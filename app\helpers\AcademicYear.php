<?php

class AcademicYear {
    
    /**
     * Get current academic year based on current date
     * Academic year starts in July and ends in June
     */
    public static function getCurrentAcademicYear() {
        $currentDate = new DateTime();
        $currentYear = (int)$currentDate->format('Y');
        $currentMonth = (int)$currentDate->format('n');
        
        // If current month is July (7) or later, academic year is current year to next year
        // If current month is before July, academic year is previous year to current year
        if ($currentMonth >= 7) {
            $startYear = $currentYear;
            $endYear = $currentYear + 1;
        } else {
            $startYear = $currentYear - 1;
            $endYear = $currentYear;
        }
        
        return $startYear . '/' . $endYear;
    }
    
    /**
     * Get selected academic year from session or default to current
     */
    public static function getSelectedAcademicYear() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        if (isset($_SESSION['selected_academic_year']) && !empty($_SESSION['selected_academic_year'])) {
            return $_SESSION['selected_academic_year'];
        }
        
        // Default to current academic year
        $currentYear = self::getCurrentAcademicYear();
        $_SESSION['selected_academic_year'] = $currentYear;
        return $currentYear;
    }
    
    /**
     * Set selected academic year in session
     */
    public static function setSelectedAcademicYear($academicYear) {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        if (self::isValidAcademicYear($academicYear)) {
            $_SESSION['selected_academic_year'] = $academicYear;
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all available academic years from database
     */
    public static function getAvailableAcademicYears() {
        try {
            require_once __DIR__ . '/../models/Database.php';
            $db = new Database();
            
            $result = $db->fetchAll("
                SELECT DISTINCT tahun_pelajaran 
                FROM kelas 
                WHERE tahun_pelajaran IS NOT NULL 
                AND tahun_pelajaran != ''
                ORDER BY tahun_pelajaran DESC
            ");
            
            $years = [];
            foreach ($result as $row) {
                $years[] = $row['tahun_pelajaran'];
            }
            
            // If no years found in database, add current year
            if (empty($years)) {
                $years[] = self::getCurrentAcademicYear();
            }
            
            return $years;
        } catch (Exception $e) {
            error_log("Error getting available academic years: " . $e->getMessage());
            return [self::getCurrentAcademicYear()];
        }
    }
    
    /**
     * Validate academic year format (YYYY/YYYY)
     */
    public static function isValidAcademicYear($academicYear) {
        if (empty($academicYear)) {
            return false;
        }
        
        // Check format: YYYY/YYYY
        if (!preg_match('/^\d{4}\/\d{4}$/', $academicYear)) {
            return false;
        }
        
        $parts = explode('/', $academicYear);
        $startYear = (int)$parts[0];
        $endYear = (int)$parts[1];
        
        // End year should be start year + 1
        if ($endYear !== $startYear + 1) {
            return false;
        }
        
        // Years should be reasonable (between 2000 and 2100)
        if ($startYear < 2000 || $startYear > 2100) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate academic year options for dropdown
     */
    public static function generateAcademicYearOptions($selectedYear = null) {
        $availableYears = self::getAvailableAcademicYears();
        $currentYear = self::getCurrentAcademicYear();
        
        if ($selectedYear === null) {
            $selectedYear = self::getSelectedAcademicYear();
        }
        
        // Add current year if not in available years
        if (!in_array($currentYear, $availableYears)) {
            $availableYears[] = $currentYear;
            rsort($availableYears); // Sort descending
        }
        
        // Add next year option
        $nextYear = self::getNextAcademicYear($currentYear);
        if (!in_array($nextYear, $availableYears)) {
            array_unshift($availableYears, $nextYear);
        }
        
        $options = [];
        foreach ($availableYears as $year) {
            $options[] = [
                'value' => $year,
                'text' => $year,
                'selected' => ($year === $selectedYear),
                'current' => ($year === $currentYear)
            ];
        }
        
        return $options;
    }
    
    /**
     * Get next academic year
     */
    public static function getNextAcademicYear($currentYear = null) {
        if ($currentYear === null) {
            $currentYear = self::getCurrentAcademicYear();
        }
        
        $parts = explode('/', $currentYear);
        $startYear = (int)$parts[0] + 1;
        $endYear = (int)$parts[1] + 1;
        
        return $startYear . '/' . $endYear;
    }
    
    /**
     * Get previous academic year
     */
    public static function getPreviousAcademicYear($currentYear = null) {
        if ($currentYear === null) {
            $currentYear = self::getCurrentAcademicYear();
        }
        
        $parts = explode('/', $currentYear);
        $startYear = (int)$parts[0] - 1;
        $endYear = (int)$parts[1] - 1;
        
        return $startYear . '/' . $endYear;
    }
    
    /**
     * Check if academic year is current year
     */
    public static function isCurrentAcademicYear($academicYear) {
        return $academicYear === self::getCurrentAcademicYear();
    }
    
    /**
     * Get academic year display text with status
     */
    public static function getAcademicYearDisplayText($academicYear) {
        $text = $academicYear;
        
        if (self::isCurrentAcademicYear($academicYear)) {
            $text .= ' (Tahun Berjalan)';
        }
        
        return $text;
    }
}
?>
