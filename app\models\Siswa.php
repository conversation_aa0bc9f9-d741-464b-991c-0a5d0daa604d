<?php
require_once __DIR__ . '/Database.php';

class Siswa {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function getAll($academicYear = null) {
        try {
            // Check user role and filter accordingly
            require_once __DIR__ . '/../helpers/Security.php';
            require_once __DIR__ . '/../helpers/AcademicYear.php';

            // Get selected academic year if not provided
            if ($academicYear === null) {
                $academicYear = AcademicYear::getSelectedAcademicYear();
            }

            if (Security::isWaliKelas()) {
                return $this->getAllForWaliKelas($academicYear);
            }

            if (Security::isPamong()) {
                return $this->getAllForPamong($academicYear);
            }

            // Admin and Staff can see all
            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.status_siswa = 'aktif' AND k.tahun_pelajaran = ?
                ORDER BY k.tingkat, s.nama_lengkap
            ", [$academicYear]);
        } catch (Exception $e) {
            // Fallback jika tabel belum ada atau error
            return [];
        }
    }

    /**
     * Get paginated students
     *
     * @param string $academicYear Academic year
     * @param int $limit Number of records per page
     * @param int $offset Offset for pagination
     * @return array Array of students
     */
    public function getAllPaginated($academicYear = null, $limit = 10, $offset = 0) {
        try {
            // Check user role and filter accordingly
            require_once __DIR__ . '/../helpers/Security.php';
            require_once __DIR__ . '/../helpers/AcademicYear.php';

            // Get selected academic year if not provided
            if ($academicYear === null) {
                $academicYear = AcademicYear::getSelectedAcademicYear();
            }

            if (Security::isWaliKelas()) {
                return $this->getAllForWaliKelasPaginated($academicYear, $limit, $offset);
            }

            if (Security::isPamong()) {
                return $this->getAllForPamongPaginated($academicYear, $limit, $offset);
            }

            // Admin and Staff can see all
            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id AND k.tahun_pelajaran = ?
                WHERE s.status_siswa = 'aktif'
                ORDER BY k.tingkat, s.nama_lengkap
                LIMIT ? OFFSET ?
            ", [$academicYear, $limit, $offset]);
        } catch (Exception $e) {
            // Fallback jika tabel belum ada atau error
            return [];
        }
    }

    /**
     * Get total count of students
     *
     * @param string $academicYear Academic year
     * @return int Total count
     */
    public function getTotalCount($academicYear = null) {
        try {
            // Check user role and filter accordingly
            require_once __DIR__ . '/../helpers/Security.php';
            require_once __DIR__ . '/../helpers/AcademicYear.php';

            // Get selected academic year if not provided
            if ($academicYear === null) {
                $academicYear = AcademicYear::getSelectedAcademicYear();
            }

            if (Security::isWaliKelas()) {
                return $this->getTotalCountForWaliKelas($academicYear);
            }

            if (Security::isPamong()) {
                return $this->getTotalCountForPamong($academicYear);
            }

            // Admin and Staff can see all
            $result = $this->db->fetch("
                SELECT COUNT(*) as total
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id AND k.tahun_pelajaran = ?
                WHERE s.status_siswa = 'aktif'
            ", [$academicYear]);

            return (int)($result['total'] ?? 0);
        } catch (Exception $e) {
            // Fallback jika tabel belum ada atau error
            return 0;
        }
    }

    /**
     * Get all students for wali kelas (only their class)
     */
    public function getAllForWaliKelas($academicYear = null) {
        try {
            require_once __DIR__ . '/../helpers/Security.php';
            require_once __DIR__ . '/../helpers/AcademicYear.php';

            if (!Security::isWaliKelas()) {
                return [];
            }

            // Get selected academic year if not provided
            if ($academicYear === null) {
                $academicYear = AcademicYear::getSelectedAcademicYear();
            }

            $userId = $_SESSION['user_id'] ?? 0;

            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE k.wali_kelas_id = ?
                AND s.status_siswa = 'aktif'
                AND k.is_active = 1
                AND k.tahun_pelajaran = ?
                ORDER BY s.nama_lengkap
            ", [$userId, $academicYear]);
        } catch (Exception $e) {
            error_log("Error in Siswa::getAllForWaliKelas(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all students for pamong (by tingkat)
     */
    public function getAllForPamong() {
        try {
            require_once __DIR__ . '/../helpers/Security.php';

            if (!Security::isPamong()) {
                return [];
            }

            $pamongType = Security::getPamongType();
            $allowedTingkat = [];

            switch ($pamongType) {
                case 'mp':
                    $allowedTingkat = ['KPP'];
                    break;
                case 'mt':
                    $allowedTingkat = ['X'];
                    break;
                case 'mm':
                    $allowedTingkat = ['XI'];
                    break;
                case 'mu':
                    $allowedTingkat = ['XII', 'KPA'];
                    break;
                default:
                    return [];
            }

            $placeholders = str_repeat('?,', count($allowedTingkat) - 1) . '?';

            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas, k.tingkat
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE k.tingkat IN ($placeholders)
                AND s.status_siswa = 'aktif'
                AND k.is_active = 1
                ORDER BY k.tingkat, k.nama_kelas, s.nama_lengkap
            ", $allowedTingkat);
        } catch (Exception $e) {
            error_log("Error in Siswa::getAllForPamong(): " . $e->getMessage());
            return [];
        }
    }

    public function getById($id) {
        try {
            require_once __DIR__ . '/../helpers/Security.php';

            // Check if user can access this student
            if (!Security::canAccessSiswa($id)) {
                return null; // Access denied
            }

            return $this->db->fetch("
                SELECT s.*, k.nama_kelas, k.tingkat
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id_siswa = ?
            ", [$id]);
        } catch (Exception $e) {
            return null;
        }
    }

    public function create($data) {
        $sql = "INSERT INTO siswa (nis, nisn, nik, no_kk, nama_lengkap, jenis_kelamin, tempat_lahir, tanggal_lahir,
                alamat, no_telepon, email, nama_ayah, nama_ibu, pekerjaan_ayah, pekerjaan_ibu,
                asal_sekolah, asal_paroki, golongan_darah, kelas_id, tahun_masuk, status_siswa, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $data['nis'],
            $data['nisn'] ?? null,
            $data['nik'] ?? null,
            $data['no_kk'] ?? null,
            $data['nama_lengkap'],
            $data['jenis_kelamin'],
            $data['tempat_lahir'] ?? null,
            $data['tanggal_lahir'] ?? null,
            $data['alamat'] ?? null,
            $data['no_telepon'] ?? null,
            $data['email'] ?? null,
            $data['nama_ayah'] ?? null,
            $data['nama_ibu'] ?? null,
            $data['pekerjaan_ayah'] ?? null,
            $data['pekerjaan_ibu'] ?? null,
            $data['asal_sekolah'] ?? null,
            $data['asal_paroki'] ?? null,
            $data['golongan_darah'] ?? null,
            $data['kelas_id'],
            $data['tahun_masuk'],
            $data['status_siswa'] ?? 'aktif',
            $data['created_by'] ?? 1
        ];

        $this->db->query($sql, $params);
        return $this->db->lastInsertId();
    }

    public function update($id, $data) {
        $sql = "UPDATE siswa SET
                nis = ?, nama_lengkap = ?, jenis_kelamin = ?,
                kelas_id = ?, tahun_masuk = ?, updated_by = ?
                WHERE id_siswa = ?";
        $params = [
            $data['nis'],
            $data['nama_lengkap'],
            $data['jenis_kelamin'],
            $data['kelas_id'],
            $data['tahun_masuk'],
            $data['updated_by'] ?? 1,
            $id
        ];
        $this->db->query($sql, $params);
        return true;
    }

    /**
     * Update complete student data
     */
    public function updateComplete($id, $data) {
        $sql = "UPDATE siswa SET
                nis = ?, nisn = ?, nik = ?, no_kk = ?, nama_lengkap = ?, jenis_kelamin = ?,
                tempat_lahir = ?, tanggal_lahir = ?, alamat = ?, no_telepon = ?, email = ?,
                nama_ayah = ?, nama_ibu = ?, pekerjaan_ayah = ?, pekerjaan_ibu = ?,
                asal_sekolah = ?, asal_paroki = ?, golongan_darah = ?,
                kelas_id = ?, tahun_masuk = ?, status_siswa = ?, updated_by = ?, updated_at = NOW()
                WHERE id_siswa = ?";

        $params = [
            $data['nis'],
            $data['nisn'] ?? null,
            $data['nik'] ?? null,
            $data['no_kk'] ?? null,
            $data['nama_lengkap'],
            $data['jenis_kelamin'],
            $data['tempat_lahir'] ?? null,
            $data['tanggal_lahir'] ?? null,
            $data['alamat'] ?? null,
            $data['no_telepon'] ?? null,
            $data['email'] ?? null,
            $data['nama_ayah'] ?? null,
            $data['nama_ibu'] ?? null,
            $data['pekerjaan_ayah'] ?? null,
            $data['pekerjaan_ibu'] ?? null,
            $data['asal_sekolah'] ?? null,
            $data['asal_paroki'] ?? null,
            $data['golongan_darah'] ?? null,
            $data['kelas_id'],
            $data['tahun_masuk'],
            $data['status_siswa'],
            $data['updated_by'] ?? 1,
            $id
        ];

        try {
            $this->db->query($sql, $params);
            return true;
        } catch (Exception $e) {
            error_log("Update siswa error: " . $e->getMessage());
            return false;
        }
    }

    public function delete($id) {
        $this->db->query("DELETE FROM siswa WHERE id_siswa = ?", [$id]);
        return true;
    }

    public function getBySiswaId($siswaId) {
        return $this->getById($siswaId);
    }

    /**
     * Get students by class ID
     */
    public function getByKelasId($kelasId) {
        try {
            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.kelas_id = ?
                ORDER BY s.nama_lengkap
            ", [$kelasId]);
        } catch (Exception $e) {
            error_log("Error in Siswa::getByKelasId(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get student statistics by class
     */
    public function getStatisticsByKelas($kelasId) {
        try {
            $stats = [];

            // Total students in class
            $total = $this->db->fetch("SELECT COUNT(*) as count FROM siswa WHERE kelas_id = ?", [$kelasId]);
            $stats['total'] = $total['count'] ?? 0;

            // Students by gender
            $byGender = $this->db->fetchAll("
                SELECT jenis_kelamin, COUNT(*) as count
                FROM siswa
                WHERE kelas_id = ?
                GROUP BY jenis_kelamin
            ", [$kelasId]);

            $stats['by_gender'] = [
                'L' => 0,
                'P' => 0
            ];

            foreach ($byGender as $gender) {
                $stats['by_gender'][$gender['jenis_kelamin']] = $gender['count'];
            }

            // Students by status
            $byStatus = $this->db->fetchAll("
                SELECT status_siswa, COUNT(*) as count
                FROM siswa
                WHERE kelas_id = ?
                GROUP BY status_siswa
            ", [$kelasId]);

            $stats['by_status'] = [
                'aktif' => 0,
                'lulus' => 0,
                'mutasi' => 0,
                'dropout' => 0
            ];

            foreach ($byStatus as $status) {
                $statusKey = $status['status_siswa'] ?? 'aktif';
                if (isset($stats['by_status'][$statusKey])) {
                    $stats['by_status'][$statusKey] = $status['count'];
                }
            }

            return $stats;
        } catch (Exception $e) {
            error_log("Error in Siswa::getStatisticsByKelas(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update foto siswa
     */
    public function updateFoto($id, $data) {
        try {
            $sql = "UPDATE siswa SET foto = ?, updated_at = NOW() WHERE id_siswa = ?";
            $params = [$data['foto'], $id];

            $this->db->query($sql, $params);
            return true;
        } catch (Exception $e) {
            error_log("Error in Siswa::updateFoto(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get paginated students for Wali Kelas
     */
    private function getAllForWaliKelasPaginated($academicYear, $limit, $offset) {
        $userId = $_SESSION['user']['id'] ?? 0;
        return $this->db->fetchAll("
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif'
            AND k.tahun_pelajaran = ?
            AND k.wali_kelas_id = ?
            ORDER BY s.nama_lengkap
            LIMIT ? OFFSET ?
        ", [$academicYear, $userId, $limit, $offset]);
    }

    /**
     * Get total count for Wali Kelas
     */
    private function getTotalCountForWaliKelas($academicYear) {
        $userId = $_SESSION['user']['id'] ?? 0;
        $result = $this->db->fetch("
            SELECT COUNT(*) as total
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif'
            AND k.tahun_pelajaran = ?
            AND k.wali_kelas_id = ?
        ", [$academicYear, $userId]);

        return (int)($result['total'] ?? 0);
    }

    /**
     * Get paginated students for Pamong
     */
    private function getAllForPamongPaginated($academicYear, $limit, $offset) {
        $userId = $_SESSION['user']['id'] ?? 0;
        return $this->db->fetchAll("
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif'
            AND k.tahun_pelajaran = ?
            AND s.pamong_id = ?
            ORDER BY k.tingkat, s.nama_lengkap
            LIMIT ? OFFSET ?
        ", [$academicYear, $userId, $limit, $offset]);
    }

    /**
     * Get total count for Pamong
     */
    private function getTotalCountForPamong($academicYear) {
        $userId = $_SESSION['user']['id'] ?? 0;
        $result = $this->db->fetch("
            SELECT COUNT(*) as total
            FROM siswa s
            LEFT JOIN kelas k ON s.kelas_id = k.id
            WHERE s.status_siswa = 'aktif'
            AND k.tahun_pelajaran = ?
            AND s.pamong_id = ?
        ", [$academicYear, $userId]);

        return (int)($result['total'] ?? 0);
    }

    /**
     * Get siswa with foto for display
     */
    public function getAllWithFoto() {
        try {
            return $this->db->fetchAll("
                SELECT s.*, k.nama_kelas,
                       CASE
                           WHEN s.foto IS NOT NULL AND s.foto != ''
                           THEN CONCAT('/siswa-app/public/uploads/foto_siswa/', s.foto)
                           ELSE NULL
                       END as foto_url
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                ORDER BY s.nama_lengkap
            ");
        } catch (Exception $e) {
            error_log("Error in Siswa::getAllWithFoto(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get foto statistics
     */
    public function getFotoStatistics() {
        try {
            $stats = [];

            // Total siswa
            $total = $this->db->fetch("SELECT COUNT(*) as count FROM siswa");
            $stats['total_siswa'] = $total['count'] ?? 0;

            // Siswa dengan foto
            $withFoto = $this->db->fetch("SELECT COUNT(*) as count FROM siswa WHERE foto IS NOT NULL AND foto != ''");
            $stats['with_foto'] = $withFoto['count'] ?? 0;

            // Siswa tanpa foto
            $stats['without_foto'] = $stats['total_siswa'] - $stats['with_foto'];

            // Persentase
            if ($stats['total_siswa'] > 0) {
                $stats['foto_percentage'] = round(($stats['with_foto'] / $stats['total_siswa']) * 100, 1);
            } else {
                $stats['foto_percentage'] = 0;
            }

            return $stats;
        } catch (Exception $e) {
            error_log("Error in Siswa::getFotoStatistics(): " . $e->getMessage());
            return [
                'total_siswa' => 0,
                'with_foto' => 0,
                'without_foto' => 0,
                'foto_percentage' => 0
            ];
        }
    }
}
?>