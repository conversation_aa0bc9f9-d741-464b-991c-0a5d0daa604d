<?php
/**
 * Script Diagnosa Sinkronisasi Role dan <PERSON>
 * 
 * Menganalisis masalah sinkronisasi antara role user dan data kelas
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/User.php';
require_once __DIR__ . '/app/models/Kelas.php';

echo "<h1>🔍 Diagnosa Sinkronisasi Role dan Data Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    $userModel = new User();
    $kelasModel = new Kelas();
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Masalah yang Ditemukan</h3>";
    echo "<ul>";
    echo "<li>Role user tidak mencantumkan nama kelas yang spesifik</li>";
    echo "<li>Data kelas untuk pemilihan wali kelas belum tepat</li>";
    echo "<li>Kemungkinan tidak ada sinkronisasi antara role dan assignment kelas</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 1: Analisis struktur tabel kelas
    echo "<h2>🔧 Test 1: Struktur Tabel Kelas</h2>";
    
    $stmt = $pdo->query("DESCRIBE kelas");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    echo "</thead><tbody>";
    
    $hasWaliKelasId = false;
    $hasWaliKelas = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'wali_kelas_id') $hasWaliKelasId = true;
        if ($column['Field'] === 'wali_kelas') $hasWaliKelas = true;
        
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    echo "<p><strong>Analisis:</strong></p>";
    echo "<ul>";
    echo "<li>Kolom wali_kelas_id: " . ($hasWaliKelasId ? '✅ Ada' : '❌ Tidak ada') . "</li>";
    echo "<li>Kolom wali_kelas: " . ($hasWaliKelas ? '✅ Ada' : '❌ Tidak ada') . "</li>";
    echo "</ul>";
    
    // Test 2: Analisis data kelas dan wali kelas
    echo "<h2>📊 Test 2: Data Kelas dan Assignment Wali Kelas</h2>";
    
    $stmt = $pdo->query("
        SELECT k.id, k.nama_kelas, k.tingkat, k.wali_kelas, k.wali_kelas_id,
               u.username, u.nama_lengkap, u.role
        FROM kelas k
        LEFT JOIN users u ON k.wali_kelas_id = u.id
        WHERE k.is_active = 1
        ORDER BY k.tingkat, k.nama_kelas
    ");
    $kelasData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($kelasData)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Kelas</th><th>Tingkat</th><th>Wali Kelas (Text)</th><th>Wali Kelas ID</th><th>User Assigned</th><th>Role User</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        $masalahSinkronisasi = 0;
        
        foreach ($kelasData as $kelas) {
            $status = '✅ OK';
            $statusColor = 'green';
            
            // Cek masalah sinkronisasi
            if ($kelas['wali_kelas_id'] && !$kelas['username']) {
                $status = '❌ User tidak ditemukan';
                $statusColor = 'red';
                $masalahSinkronisasi++;
            } elseif (!$kelas['wali_kelas_id']) {
                $status = '⚠️ Belum ada wali kelas';
                $statusColor = 'orange';
                $masalahSinkronisasi++;
            } elseif ($kelas['role'] && strpos($kelas['role'], 'wali_kelas_') === 0) {
                // Cek apakah role sesuai dengan nama kelas
                $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $kelas['nama_kelas']));
                if ($kelas['role'] !== $expectedRole) {
                    $status = '⚠️ Role tidak sesuai';
                    $statusColor = 'orange';
                    $masalahSinkronisasi++;
                }
            }
            
            echo "<tr>";
            echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
            echo "<td>{$kelas['tingkat']}</td>";
            echo "<td>{$kelas['wali_kelas']}</td>";
            echo "<td>{$kelas['wali_kelas_id']}</td>";
            echo "<td>{$kelas['nama_lengkap']} ({$kelas['username']})</td>";
            echo "<td><code>{$kelas['role']}</code></td>";
            echo "<td style='color: {$statusColor};'>{$status}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        echo "<p><strong>Ringkasan:</strong></p>";
        echo "<ul>";
        echo "<li>Total kelas: " . count($kelasData) . "</li>";
        echo "<li>Masalah sinkronisasi: {$masalahSinkronisasi}</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Tidak ada data kelas ditemukan</p>";
    }
    
    // Test 3: Analisis role user wali kelas
    echo "<h2>👥 Test 3: Analisis Role User Wali Kelas</h2>";
    
    $stmt = $pdo->query("
        SELECT id, username, nama_lengkap, role, email, is_active
        FROM users 
        WHERE role LIKE 'wali_kelas%'
        ORDER BY role
    ");
    $waliKelasUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($waliKelasUsers)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Kelas yang Dikelola</th><th>Status Sinkronisasi</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasUsers as $user) {
            // Cek kelas yang dikelola
            $stmt = $pdo->prepare("SELECT nama_kelas FROM kelas WHERE wali_kelas_id = ? AND is_active = 1");
            $stmt->execute([$user['id']]);
            $kelasYangDikelola = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Analisis sinkronisasi role
            $statusSinkronisasi = '✅ Sinkron';
            $statusColor = 'green';
            
            if (strpos($user['role'], 'wali_kelas_') === 0) {
                // Role spesifik, cek apakah sesuai dengan kelas
                $expectedClass = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $user['role']));
                
                if (empty($kelasYangDikelola)) {
                    $statusSinkronisasi = '❌ Tidak ada kelas';
                    $statusColor = 'red';
                } elseif (!in_array($expectedClass, $kelasYangDikelola)) {
                    $statusSinkronisasi = '⚠️ Kelas tidak sesuai role';
                    $statusColor = 'orange';
                }
            } else {
                // Role umum wali_kelas
                if (empty($kelasYangDikelola)) {
                    $statusSinkronisasi = '⚠️ Belum ada assignment';
                    $statusColor = 'orange';
                }
            }
            
            echo "<tr>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><code>{$user['role']}</code></td>";
            echo "<td>" . (empty($kelasYangDikelola) ? '<em>Tidak ada</em>' : implode(', ', $kelasYangDikelola)) . "</td>";
            echo "<td style='color: {$statusColor};'>{$statusSinkronisasi}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Tidak ada user dengan role wali kelas</p>";
    }
    
    // Test 4: Rekomendasi perbaikan
    echo "<h2>💡 Test 4: Rekomendasi Perbaikan</h2>";
    
    $rekomendasi = [];
    
    // Cek apakah perlu sinkronisasi role dengan kelas
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u
        JOIN kelas k ON u.id = k.wali_kelas_id
        WHERE u.role LIKE 'wali_kelas_%' 
        AND u.role != CONCAT('wali_kelas_', LOWER(REPLACE(k.nama_kelas, '-', '_')))
    ");
    $roleMismatch = $stmt->fetchColumn();
    
    if ($roleMismatch > 0) {
        $rekomendasi[] = [
            'masalah' => 'Role user tidak sesuai dengan kelas yang dikelola',
            'solusi' => 'Update role user agar sesuai dengan nama kelas',
            'prioritas' => 'Tinggi'
        ];
    }
    
    // Cek kelas tanpa wali kelas
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM kelas WHERE wali_kelas_id IS NULL AND is_active = 1");
    $kelasWithoutWali = $stmt->fetchColumn();
    
    if ($kelasWithoutWali > 0) {
        $rekomendasi[] = [
            'masalah' => "{$kelasWithoutWali} kelas belum memiliki wali kelas",
            'solusi' => 'Assign wali kelas untuk setiap kelas atau buat user wali kelas baru',
            'prioritas' => 'Sedang'
        ];
    }
    
    // Cek user wali kelas tanpa kelas
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u
        WHERE u.role LIKE 'wali_kelas%'
        AND u.id NOT IN (SELECT wali_kelas_id FROM kelas WHERE wali_kelas_id IS NOT NULL AND is_active = 1)
    ");
    $userWithoutKelas = $stmt->fetchColumn();
    
    if ($userWithoutKelas > 0) {
        $rekomendasi[] = [
            'masalah' => "{$userWithoutKelas} user wali kelas tidak mengelola kelas apapun",
            'solusi' => 'Assign kelas untuk user tersebut atau ubah role mereka',
            'prioritas' => 'Rendah'
        ];
    }
    
    if (!empty($rekomendasi)) {
        echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Masalah</th><th>Solusi</th><th>Prioritas</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($rekomendasi as $item) {
            $prioritasColor = $item['prioritas'] === 'Tinggi' ? 'red' : ($item['prioritas'] === 'Sedang' ? 'orange' : 'blue');
            
            echo "<tr>";
            echo "<td>{$item['masalah']}</td>";
            echo "<td>{$item['solusi']}</td>";
            echo "<td style='color: {$prioritasColor}; font-weight: bold;'>{$item['prioritas']}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ Tidak ada masalah sinkronisasi yang ditemukan</p>";
    }
    
    // Test 5: Script perbaikan otomatis
    echo "<h2>🔧 Test 5: Script Perbaikan Otomatis</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Langkah Perbaikan yang Disarankan:</h4>";
    echo "<ol>";
    echo "<li><strong>Sinkronisasi Role dengan Kelas:</strong> Update role user agar sesuai dengan kelas yang dikelola</li>";
    echo "<li><strong>Assignment Wali Kelas:</strong> Assign wali kelas untuk kelas yang belum memiliki</li>";
    echo "<li><strong>Update Form Kelas:</strong> Pastikan form create/edit kelas menggunakan dropdown yang benar</li>";
    echo "<li><strong>Validasi Data:</strong> Tambah validasi untuk memastikan konsistensi data</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔗 Script Perbaikan:</h4>";
    echo "<ul>";
    echo "<li><a href='fix_role_kelas_sync.php' target='_blank'>fix_role_kelas_sync.php</a> - Script untuk sinkronisasi role dan kelas</li>";
    echo "<li><a href='update_kelas_form.php' target='_blank'>update_kelas_form.php</a> - Update form kelas dengan dropdown yang benar</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Diagnosa selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
