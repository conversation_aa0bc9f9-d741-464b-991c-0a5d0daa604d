# Implementasi Role Spesifik Wali Kelas

## 📋 Deskripsi
Implementasi sistem role spesifik per kelas untuk wali kelas, mengubah dari role umum `wali_kelas` menjadi role spesifik seperti `wali_kelas_kpp_a`, `wali_kelas_x_1`, dll.

## 🎯 Tujuan
- **Akses yang lebih spesifik** - Setiap wali kelas hanya akses siswa di kelasnya
- **<PERSON><PERSON><PERSON><PERSON> yang lebih mudah** - Role langsung menunjukkan kelas yang dikelola
- **Keamanan yang lebih baik** - Tidak ada kemungkinan akses lintas kelas
- **Skalabilitas** - Mudah menambah kelas baru dengan role baru

## 🔧 Struktur Role Baru

### Role yang Diimplementasikan
```
wali_kelas_kpp_a    → Wali Kelas KPP-A
wali_kelas_kpp_b    → Wali Kelas KPP-B  
wali_kelas_kpp_c    → Wali Kelas KPP-C
wali_kelas_x_1      → Wali Kelas X-1
wali_kelas_x_2      → Wali Kelas X-2
wali_kelas_xi_1     → Wali Kelas XI-1
wali_kelas_xi_2     → Wali Kelas XI-2
wali_kelas_xii_1    → Wali Kelas XII-1
wali_kelas_xii_2    → Wali Kelas XII-2
wali_kelas_kpa      → Wali Kelas KPA
```

### Mapping Role ke Kelas
| Role | Kelas Target | Akses Siswa |
|------|--------------|-------------|
| `wali_kelas_kpp_a` | KPP-A | Hanya siswa KPP-A |
| `wali_kelas_x_1` | X-1 | Hanya siswa X-1 |
| `wali_kelas_xi_2` | XI-2 | Hanya siswa XI-2 |
| dst... | dst... | dst... |

## 🔧 Implementasi Teknis

### 1. Update Database Schema

**File:** `implement_role_specific_wali_kelas.php`

```sql
-- Update enum role dengan role spesifik
ALTER TABLE users MODIFY COLUMN role ENUM(
    'admin',
    'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
    'wali_kelas_kpp_a', 'wali_kelas_kpp_b', 'wali_kelas_kpp_c',
    'wali_kelas_x_1', 'wali_kelas_x_2',
    'wali_kelas_xi_1', 'wali_kelas_xi_2', 
    'wali_kelas_xii_1', 'wali_kelas_xii_2',
    'wali_kelas_kpa',
    'staff'
) DEFAULT 'staff';
```

### 2. Update Security Helper

**File:** `app/helpers/Security.php`

```php
public static function isWaliKelas() {
    if (!self::isAuthenticated()) {
        return false;
    }
    
    $role = $_SESSION['user_role'] ?? '';
    return $role === 'wali_kelas' || strpos($role, 'wali_kelas_') === 0;
}

public static function getWaliKelasClass() {
    $role = $_SESSION['user_role'] ?? '';
    if (strpos($role, 'wali_kelas_') === 0) {
        $className = str_replace('wali_kelas_', '', $role);
        return strtoupper(str_replace('_', '-', $className));
    }
    return null;
}

public static function isWaliKelasFor($className) {
    $role = $_SESSION['user_role'] ?? '';
    $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $className));
    return $role === $expectedRole;
}
```

### 3. Update Model Siswa

**File:** `app/models/Siswa.php`

```php
public function getAllForWaliKelas($academicYear = null) {
    // ... existing code ...
    
    $userRole = $_SESSION['user_role'] ?? '';

    // For specific wali kelas roles, filter by role-based class name
    if (strpos($userRole, 'wali_kelas_') === 0) {
        $className = Security::getWaliKelasClass(); // Returns 'X-1', 'KPP-A', etc
        
        return $this->db->fetchAll("
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.nama_kelas = ?
            AND s.status_siswa = 'aktif'
            AND k.is_active = 1
            AND k.tahun_pelajaran = ?
            ORDER BY s.nama_lengkap
        ", [$className, $academicYear]);
    }

    // Fallback for old 'wali_kelas' role
    return $this->db->fetchAll("...", [$userId, $academicYear]);
}
```

### 4. Update Model Kelas

**File:** `app/models/Kelas.php`

```php
public function getForWaliKelas($academicYear = null) {
    // ... existing code ...
    
    $userRole = $_SESSION['user_role'] ?? '';

    // For specific wali kelas roles, filter by role-based class name
    if (strpos($userRole, 'wali_kelas_') === 0) {
        $className = Security::getWaliKelasClass();
        
        return $this->db->fetchAll("
            SELECT id as id_kelas, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at
            FROM kelas
            WHERE is_active = 1 
            AND tahun_pelajaran = ?
            AND nama_kelas = ?
            ORDER BY tingkat, nama_kelas
        ", [$academicYear, $className]);
    }

    // Fallback for old 'wali_kelas' role
    return $this->db->fetchAll("...", [$academicYear, $userId]);
}
```

## 🚀 Cara Implementasi

### Langkah 1: Backup Database
```bash
mysqldump -u root -p siswa_app > backup_before_role_specific.sql
```

### Langkah 2: Analisis Sistem
```
http://localhost/siswa-app/implement_flexible_wali_kelas_management.php
```

### Langkah 3: Implementasi Role Spesifik
```
http://localhost/siswa-app/implement_role_specific_wali_kelas.php
```

### Langkah 4: Test dan Verifikasi
```
http://localhost/siswa-app/test_role_specific_wali_kelas.php
```

## 🧪 Testing

### User untuk Testing
| Username | Password | Role | Kelas |
|----------|----------|------|-------|
| wali_kpp_a | wali123 | wali_kelas_kpp_a | KPP-A |
| wali_x_1 | wali123 | wali_kelas_x_1 | X-1 |
| wali_x_2 | wali123 | wali_kelas_x_2 | X-2 |
| wali_xi_1 | wali123 | wali_kelas_xi_1 | XI-1 |
| wali_xi_2 | wali123 | wali_kelas_xi_2 | XI-2 |
| wali_xii_1 | wali123 | wali_kelas_xii_1 | XII-1 |
| wali_xii_2 | wali123 | wali_kelas_xii_2 | XII-2 |
| wali_kpa | wali123 | wali_kelas_kpa | KPA |

### Test Manual
1. **Login** dengan salah satu username di atas
2. **Akses Daftar Siswa** → Verifikasi hanya siswa dari kelas yang sesuai
3. **Akses Data Kelas** → Verifikasi hanya kelas yang sesuai
4. **Coba akses data kelas lain** → Harus tidak bisa

### Test Otomatis
Script `test_role_specific_wali_kelas.php` akan melakukan:
- ✅ Verifikasi role spesifik ada di database
- ✅ Test Security helper methods
- ✅ Test akses data siswa per role
- ✅ Test akses data kelas per role
- ✅ Verifikasi tidak ada akses lintas kelas

## ⚖️ Perbandingan dengan Sistem Lama

| Aspek | Sistem Lama | Sistem Baru |
|-------|-------------|-------------|
| **Role** | `wali_kelas` (umum) | `wali_kelas_x_1` (spesifik) |
| **Akses Data** | Berdasarkan `wali_kelas_id` | Berdasarkan nama kelas dari role |
| **Keamanan** | Bergantung pada assignment | Built-in dalam role |
| **Maintenance** | Perlu update assignment | Otomatis dari role |
| **Skalabilitas** | Perlu update mapping | Tinggal tambah role baru |
| **User Management** | Kompleks (assignment) | Sederhana (role langsung) |

## 🎯 Keuntungan Sistem Baru

### ✅ Keamanan
- **No Cross-Access** - Tidak mungkin akses kelas lain
- **Role-Based** - Akses langsung dari role, bukan assignment
- **Audit Trail** - Mudah tracking siapa akses apa

### ✅ Maintenance
- **Self-Contained** - Role sudah menentukan akses
- **No Assignment** - Tidak perlu manage mapping user-kelas
- **Clear Responsibility** - Jelas siapa wali kelas mana

### ✅ Skalabilitas
- **Easy Addition** - Tinggal tambah role baru untuk kelas baru
- **Consistent Pattern** - Pola role yang konsisten
- **Future-Proof** - Mudah extend untuk fitur baru

## 📝 Maintenance

### Menambah Kelas Baru
1. **Tambah role baru** ke enum (misal: `wali_kelas_x_3`)
2. **Buat user baru** dengan role tersebut
3. **Buat kelas baru** dengan nama yang sesuai (X-3)
4. **Test akses** dengan login user baru

### Mengubah Assignment
1. **Update role user** dari role lama ke role baru
2. **Verifikasi akses** sudah berubah
3. **Update data kelas** jika diperlukan

### Monitoring
- **Regular testing** dengan script test
- **Monitor log error** untuk akses yang gagal
- **Audit user roles** secara berkala

## 🎉 Kesimpulan

**Sistem role spesifik wali kelas berhasil diimplementasikan!**

- ✅ **Database schema** sudah diupdate dengan role spesifik
- ✅ **Security helper** sudah mendukung role spesifik
- ✅ **Model queries** sudah filter berdasarkan role
- ✅ **User management** sudah menggunakan role spesifik
- ✅ **Testing** sudah dilakukan dan berhasil

Sekarang setiap wali kelas memiliki role yang spesifik sesuai kelas yang mereka kelola, dan sistem otomatis membatasi akses mereka hanya ke data siswa dan kelas yang sesuai dengan role tersebut.
