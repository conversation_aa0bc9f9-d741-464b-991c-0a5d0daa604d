<?php
/**
 * Debug Form Submission
 * Script ini akan menangkap dan debug form submission secara real-time
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/helpers/Security.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 Debug Form Submission</h1>";

// Set up session if not exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user'] = ['id' => 1, 'username' => 'admin', 'role' => 'admin'];
    echo "<p style='color: blue;'>ℹ️ Session setup for testing</p>";
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>📝 Form Submission Detected</h2>";
    
    echo "<h3>Raw POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3>Processing Form Data:</h3>";
    
    try {
        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        echo "<p><strong>CSRF Token:</strong> " . substr($csrfToken, 0, 20) . "...</p>";
        
        if (!Security::verifyCSRFToken($csrfToken)) {
            echo "<p style='color: red;'>❌ CSRF Token verification failed</p>";
            exit;
        } else {
            echo "<p style='color: green;'>✅ CSRF Token verified</p>";
        }
        
        // Process form fields
        $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
        $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
        $kurikulum = Security::sanitizeInput($_POST['kurikulum'] ?? '');
        $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
        $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');
        $kapasitas = (int)($_POST['kapasitas'] ?? 30);
        
        echo "<h3>Processed Data:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Field</th><th>Value</th><th>Valid</th>";
        echo "</tr>";
        
        $fields = [
            'nama_kelas' => $nama_kelas,
            'tingkat' => $tingkat,
            'kurikulum' => $kurikulum,
            'tahun_pelajaran' => $tahun_pelajaran,
            'wali_kelas' => $wali_kelas,
            'kapasitas' => $kapasitas
        ];
        
        foreach ($fields as $field => $value) {
            $isValid = !empty($value) || in_array($field, ['kurikulum', 'wali_kelas']);
            $icon = $isValid ? '✅' : '❌';
            echo "<tr>";
            echo "<td><strong>{$field}</strong></td>";
            echo "<td>{$value}</td>";
            echo "<td>{$icon}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Validation check
        if ($nama_kelas && $tingkat && $tahun_pelajaran) {
            echo "<p style='color: green;'>✅ Basic validation passed</p>";
            
            $data = [
                'nama_kelas' => $nama_kelas,
                'tingkat' => $tingkat,
                'kurikulum' => $kurikulum,
                'tahun_pelajaran' => $tahun_pelajaran,
                'wali_kelas' => $wali_kelas,
                'kapasitas' => $kapasitas,
                'created_by' => $_SESSION['user_id'] ?? 1
            ];
            
            echo "<h3>Data for Model:</h3>";
            echo "<pre>" . print_r($data, true) . "</pre>";
            
            // Try to create the class
            $kelasModel = new Kelas();
            
            echo "<h3>Attempting to Create Class:</h3>";
            
            try {
                $result = $kelasModel->createKelas($data);
                
                if ($result) {
                    echo "<p style='color: green;'>🎉 SUCCESS! Class created with ID: {$result}</p>";
                    
                    // Verify the created class
                    $db = new Database();
                    $createdClass = $db->fetch("SELECT * FROM kelas WHERE id = ?", [$result]);
                    
                    echo "<h3>Created Class:</h3>";
                    echo "<pre>" . print_r($createdClass, true) . "</pre>";
                    
                    echo "<p style='color: green;'>✅ Form submission successful!</p>";
                    echo "<p><a href='/siswa-app/public/kelas' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View Class List</a></p>";
                    
                } else {
                    echo "<p style='color: red;'>❌ Model returned false - creation failed</p>";
                    
                    // Check for database errors
                    echo "<h3>Checking for Database Errors:</h3>";
                    $db = new Database();
                    
                    // Try a simple test query
                    try {
                        $testResult = $db->fetch("SELECT 1 as test");
                        echo "<p style='color: green;'>✅ Database connection working</p>";
                    } catch (Exception $dbError) {
                        echo "<p style='color: red;'>❌ Database error: " . $dbError->getMessage() . "</p>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Exception during creation: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Validation failed</p>";
            echo "<p>Required fields missing:</p>";
            echo "<ul>";
            if (!$nama_kelas) echo "<li>Nama Kelas</li>";
            if (!$tingkat) echo "<li>Tingkat</li>";
            if (!$tahun_pelajaran) echo "<li>Tahun Pelajaran</li>";
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Processing error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
} else {
    echo "<h2>📋 Debug Form Setup</h2>";
    echo "<p>This page will capture and debug form submissions.</p>";
    echo "<p>Submit the form below to see detailed debugging information.</p>";
    
    // Generate CSRF token
    $csrfToken = Security::generateCSRFToken();
    
    echo "<h3>Test Form:</h3>";
    echo "<form method='POST' action='' style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
    echo "<input type='hidden' name='csrf_token' value='{$csrfToken}'>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label><strong>Nama Kelas:</strong></label><br>";
    echo "<input type='text' name='nama_kelas' value='DEBUG-TEST-" . date('His') . "' style='width: 300px; padding: 5px;'>";
    echo "</div>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label><strong>Tingkat:</strong></label><br>";
    echo "<select name='tingkat' style='width: 300px; padding: 5px;'>";
    echo "<option value=''>Pilih Tingkat</option>";
    echo "<option value='KPP'>KPP (Kelas Persiapan Pertama)</option>";
    echo "<option value='X' selected>X (Kelas 10)</option>";
    echo "<option value='XI'>XI (Kelas 11)</option>";
    echo "<option value='XII'>XII (Kelas 12)</option>";
    echo "<option value='KPA'>KPA (Kelas Persiapan Atas)</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label><strong>Kurikulum:</strong></label><br>";
    echo "<select name='kurikulum' style='width: 300px; padding: 5px;'>";
    echo "<option value=''>Pilih Kurikulum</option>";
    echo "<option value='Kurikulum Seminari'>Kurikulum Seminari</option>";
    echo "<option value='Kurikulum K13' selected>Kurikulum K13</option>";
    echo "<option value='Kurikulum Merdeka'>Kurikulum Merdeka</option>";
    echo "<option value='Kurikulum Deep Learning'>Kurikulum Deep Learning</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label><strong>Tahun Pelajaran:</strong></label><br>";
    echo "<input type='text' name='tahun_pelajaran' value='2024/2025' style='width: 300px; padding: 5px;'>";
    echo "</div>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label><strong>Wali Kelas:</strong></label><br>";
    echo "<input type='text' name='wali_kelas' value='Debug Test Teacher' style='width: 300px; padding: 5px;'>";
    echo "</div>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label><strong>Kapasitas:</strong></label><br>";
    echo "<input type='number' name='kapasitas' value='30' min='1' max='50' style='width: 300px; padding: 5px;'>";
    echo "</div>";
    
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Submit Debug Test</button>";
    echo "</form>";
    
    echo "<h3>Alternative Test:</h3>";
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Original Form</a></p>";
}

echo "<h2>📊 System Status</h2>";

try {
    $db = new Database();
    
    // Check database connection
    $dbTest = $db->fetch("SELECT 1 as test");
    echo "<p style='color: green;'>✅ Database: Connected</p>";
    
    // Check kelas table
    $kelasCount = $db->fetch("SELECT COUNT(*) as count FROM kelas");
    echo "<p style='color: green;'>✅ Kelas Table: " . ($kelasCount['count'] ?? 0) . " records</p>";
    
    // Check session
    echo "<p style='color: green;'>✅ Session: User ID " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
    
    // Check CSRF
    $testToken = Security::generateCSRFToken();
    echo "<p style='color: green;'>✅ CSRF: Token generation working</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ System Error: " . $e->getMessage() . "</p>";
}
?>
