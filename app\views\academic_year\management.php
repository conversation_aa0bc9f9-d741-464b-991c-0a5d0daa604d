<?php
require_once __DIR__ . '/../../helpers/Security.php';
require_once __DIR__ . '/../../helpers/AcademicYear.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-calendar-range text-primary"></i>
                        Manaj<PERSON><PERSON>
                    </h1>
                    <p class="text-muted mb-0"><PERSON><PERSON><PERSON> tahun a<PERSON> sekolah</p>
                </div>
                <div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addYearModal">
                            <i class="bi bi-plus-circle"></i> Tambah Tahun Ajaran
                        </button>
                        <?php if (Security::hasRole(['admin'])): ?>
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#generateYearsModal">
                            <i class="bi bi-arrow-repeat"></i> Generate Bulk
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($success)): ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?= htmlspecialchars($success) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Current Year Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-calendar-check text-primary"></i>
                        Tahun Ajaran Saat Ini
                    </h5>
                    <h3 class="text-primary mb-0"><?= htmlspecialchars($current_year) ?></h3>
                    <small class="text-muted">Tahun ajaran yang sedang berjalan berdasarkan tanggal sistem</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Academic Years List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-list-ul"></i>
                        Daftar Tahun Ajaran Tersedia
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($available_years)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">Belum ada tahun ajaran yang terdaftar</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-primary">
                                <tr>
                                    <th>Tahun Ajaran</th>
                                    <th>Status</th>
                                    <th>Total Siswa</th>
                                    <th>Kelas Aktif</th>
                                    <th>Total Kelas</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($available_years as $year): ?>
                                <?php $stats = $year_stats[$year] ?? []; ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($year) ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($stats['is_current'] ?? false): ?>
                                            <span class="badge bg-success">
                                                <i class="bi bi-play-circle"></i> Berjalan
                                            </span>
                                        <?php elseif ($stats['has_data'] ?? false): ?>
                                            <span class="badge bg-info">
                                                <i class="bi bi-archive"></i> Berisi Data
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-circle"></i> Kosong
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary rounded-pill">
                                            <?= number_format($stats['total_siswa'] ?? 0) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success rounded-pill">
                                            <?= number_format($stats['total_kelas_aktif'] ?? 0) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary rounded-pill">
                                            <?= number_format($stats['total_kelas'] ?? 0) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/siswa-app/public/siswa?year=<?= urlencode($year) ?>"
                                               class="btn btn-outline-primary" title="Lihat Siswa">
                                                <i class="bi bi-people"></i>
                                            </a>
                                            <a href="/siswa-app/public/kelas?year=<?= urlencode($year) ?>"
                                               class="btn btn-outline-info" title="Lihat Kelas">
                                                <i class="bi bi-grid-3x3"></i>
                                            </a>
                                            <?php if (Security::hasRole(['admin']) && !($stats['is_current'] ?? false) && !($stats['has_data'] ?? false)): ?>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmDelete('<?= htmlspecialchars($year) ?>')"
                                                    title="Hapus">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Add Year Modal -->
    <div class="modal fade" id="addYearModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="/siswa-app/public/academic-year-management/add">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-plus-circle"></i>
                            Tambah Tahun Ajaran
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="academic_year" class="form-label">Tahun Ajaran</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="academic_year" 
                                   name="academic_year" 
                                   placeholder="2023/2024" 
                                   pattern="[0-9]{4}/[0-9]{4}"
                                   required>
                            <div class="form-text">Format: YYYY/YYYY (contoh: 2023/2024)</div>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Catatan:</strong> Tahun ajaran yang ditambahkan akan tersedia untuk pembuatan kelas dan pendaftaran siswa.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Tambah
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Generate Years Modal -->
    <?php if (Security::hasRole(['admin'])): ?>
    <div class="modal fade" id="generateYearsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="/siswa-app/public/academic-year-management/generate">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-arrow-repeat"></i>
                            Generate Tahun Ajaran Bulk
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_year" class="form-label">Tahun Mulai</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="start_year" 
                                           name="start_year" 
                                           min="2000" 
                                           max="2050" 
                                           value="2020"
                                           required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_year" class="form-label">Tahun Akhir</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="end_year" 
                                           name="end_year" 
                                           min="2000" 
                                           max="2050" 
                                           value="<?= date('Y') ?>"
                                           required>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Perhatian:</strong> Fitur ini akan membuat tahun ajaran dari <strong>Tahun Mulai</strong> hingga <strong>Tahun Akhir</strong>. 
                            Maksimal 20 tahun ajaran dapat dibuat sekaligus.
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            Contoh: Tahun Mulai = 2020, Tahun Akhir = 2023 akan membuat:
                            <ul class="mb-0 mt-2">
                                <li>2020/2021</li>
                                <li>2021/2022</li>
                                <li>2022/2023</li>
                                <li>2023/2024</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-arrow-repeat"></i> Generate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="/siswa-app/public/academic-year-management/delete" id="deleteForm">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle"></i>
                            Konfirmasi Hapus
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Apakah Anda yakin ingin menghapus tahun ajaran <strong id="deleteYearText"></strong>?</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Perhatian:</strong> Tahun ajaran yang memiliki data siswa atau kelas aktif tidak dapat dihapus.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        <input type="hidden" name="academic_year" id="deleteYearInput">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash"></i> Hapus
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

<script>
function confirmDelete(academicYear) {
    document.getElementById('deleteYearText').textContent = academicYear;
    document.getElementById('deleteYearInput').value = academicYear;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-format academic year input
document.getElementById('academic_year').addEventListener('input', function(e) {
    let value = e.target.value.replace(/[^0-9]/g, '');
    if (value.length >= 4) {
        value = value.substring(0, 4) + '/' + value.substring(4, 8);
    }
    e.target.value = value;
});

// Validate year range in generate modal
document.getElementById('start_year').addEventListener('change', validateYearRange);
document.getElementById('end_year').addEventListener('change', validateYearRange);

function validateYearRange() {
    const startYear = parseInt(document.getElementById('start_year').value);
    const endYear = parseInt(document.getElementById('end_year').value);

    if (startYear && endYear && startYear > endYear) {
        alert('Tahun mulai tidak boleh lebih besar dari tahun akhir');
        document.getElementById('start_year').focus();
    }
}
</script>
