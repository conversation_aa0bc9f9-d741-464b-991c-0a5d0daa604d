<?php
/**
 * Test Script untuk Sub-Accordion BK
 * Script ini akan menambahkan data test catatan BK untuk menguji implementasi sub-accordion
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/CatatanSiswa.php';
require_once __DIR__ . '/app/models/Siswa.php';

echo "<h1>🧪 Test Sub-Accordion BK Implementation</h1>";

try {
    $db = new Database();
    $catatanModel = new CatatanSiswa();
    $siswaModel = new Siswa();
    
    echo "<h2>📋 Checking Prerequisites</h2>";
    
    // Check if we have test student
    $testSiswa = $db->fetchOne("SELECT * FROM siswa LIMIT 1");
    if (!$testSiswa) {
        echo "<p style='color: red;'>❌ No test student found. Please add a student first.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Test student found: {$testSiswa['nama_lengkap']} (ID: {$testSiswa['id_siswa']})</p>";
    
    // Check if BK categories exist
    $bkCategories = $db->fetchAll("SELECT * FROM kategori_catatan WHERE kode_kategori LIKE 'bk_%'");
    echo "<p style='color: green;'>✅ Found " . count($bkCategories) . " BK categories</p>";
    
    if (count($bkCategories) < 4) {
        echo "<p style='color: orange;'>⚠️ Adding missing BK categories...</p>";
        
        $requiredCategories = [
            ['bk_konseling', 'BK Konseling', 'Catatan Bimbingan Konseling', '#17a2b8', 'bi-heart'],
            ['bk_prestasi', 'BK Prestasi', 'Catatan Prestasi Siswa', '#ffc107', 'bi-award'],
            ['bk_pelanggaran', 'BK Pelanggaran', 'Catatan Pelanggaran Siswa', '#dc3545', 'bi-exclamation-triangle'],
            ['bk_lainnya', 'BK Lainnya', 'Catatan BK Lainnya', '#6c757d', 'bi-chat-dots']
        ];
        
        foreach ($requiredCategories as $cat) {
            try {
                $db->query("INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES (?, ?, ?, ?, ?)", $cat);
                echo "<p style='color: green;'>✅ Added category: {$cat[1]}</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Category {$cat[1]} already exists</p>";
            }
        }
    }
    
    echo "<h2>📝 Adding Test BK Records</h2>";
    
    // Sample BK catatan data
    $testCatatanBK = [
        // Konseling
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'bk_konseling',
            'judul_catatan' => 'Konseling Adaptasi Sekolah',
            'isi_catatan' => 'Siswa mengalami kesulitan adaptasi dengan lingkungan sekolah baru. Dilakukan sesi konseling untuk membantu proses adaptasi. Siswa menunjukkan progress positif setelah 2 sesi konseling.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-5 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Lanjutkan sesi konseling mingguan dan monitor progress adaptasi',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+7 days')),
            'created_by' => 1
        ],
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'bk_konseling',
            'judul_catatan' => 'Konseling Karir',
            'isi_catatan' => 'Siswa meminta bimbingan untuk memilih jurusan kuliah yang sesuai dengan minat dan bakat. Dilakukan tes minat bakat dan diskusi mengenai pilihan karir.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-3 days')),
            'tingkat_prioritas' => 'rendah',
            'status_catatan' => 'selesai',
            'tindak_lanjut' => null,
            'tanggal_tindak_lanjut' => null,
            'created_by' => 1
        ],
        
        // Prestasi
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'bk_prestasi',
            'judul_catatan' => 'Juara 1 Lomba Matematika',
            'isi_catatan' => 'Siswa berhasil meraih juara 1 dalam lomba matematika tingkat kabupaten. Menunjukkan kemampuan analisis yang sangat baik dan dedikasi tinggi dalam belajar.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-10 days')),
            'tingkat_prioritas' => 'tinggi',
            'status_catatan' => 'selesai',
            'tindak_lanjut' => 'Daftarkan untuk lomba tingkat provinsi',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+14 days')),
            'created_by' => 1
        ],
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'bk_prestasi',
            'judul_catatan' => 'Prestasi Olahraga Basket',
            'isi_catatan' => 'Siswa terpilih menjadi kapten tim basket sekolah dan berhasil membawa tim ke final kompetisi antar sekolah. Menunjukkan leadership yang baik.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-7 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Monitor performa tim dan persiapan untuk kompetisi selanjutnya',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+21 days')),
            'created_by' => 1
        ],
        
        // Pelanggaran
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'bk_pelanggaran',
            'judul_catatan' => 'Terlambat Masuk Kelas',
            'isi_catatan' => 'Siswa terlambat masuk kelas sebanyak 3 kali dalam seminggu. Sudah diberikan teguran lisan dan diminta membuat surat pernyataan.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-2 days')),
            'tingkat_prioritas' => 'rendah',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Monitor kedisiplinan dan panggil orang tua jika masih berulang',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+7 days')),
            'created_by' => 1
        ],
        
        // Lainnya
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'bk_lainnya',
            'judul_catatan' => 'Konsultasi Masalah Keluarga',
            'isi_catatan' => 'Siswa mengalami masalah keluarga yang mempengaruhi konsentrasi belajar. Dilakukan konseling untuk memberikan dukungan emosional dan strategi coping.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-1 day')),
            'tingkat_prioritas' => 'tinggi',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Lanjutkan sesi konseling dan koordinasi dengan wali kelas',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+3 days')),
            'created_by' => 1
        ]
    ];
    
    // Insert test data
    $successCount = 0;
    foreach ($testCatatanBK as $catatan) {
        try {
            $result = $catatanModel->create($catatan);
            if ($result) {
                echo "<p style='color: green;'>✅ Added: {$catatan['judul_catatan']} ({$catatan['jenis_catatan']})</p>";
                $successCount++;
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Failed to add: {$catatan['judul_catatan']} - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📊 Test Results</h2>";
    echo "<p style='color: green;'>✅ Successfully added {$successCount} test BK records</p>";
    
    // Test the new method
    echo "<h2>🧪 Testing getBKGroupedBySiswaId Method</h2>";
    $bkGrouped = $catatanModel->getBKGroupedBySiswaId($testSiswa['id_siswa']);
    
    foreach ($bkGrouped as $jenis => $catatanList) {
        $count = count($catatanList);
        if ($count > 0) {
            echo "<p style='color: green;'>✅ {$jenis}: {$count} records</p>";
        } else {
            echo "<p style='color: gray;'>⚪ {$jenis}: 0 records</p>";
        }
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/siswa/detail/{$testSiswa['id_siswa']}' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 View Student Detail Page (Test Sub-Accordion)
    </a></p>";
    
    echo "<p><a href='/siswa-app/public/catatan/add/{$testSiswa['id_siswa']}' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Add More BK Records
    </a></p>";
    
    echo "<h2>✅ Test Complete</h2>";
    echo "<p style='color: green; font-weight: bold;'>
        Sub-accordion implementation is ready for testing! 
        Visit the student detail page to see the BK sub-categories in action.
    </p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
