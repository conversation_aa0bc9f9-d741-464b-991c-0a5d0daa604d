<?php
/**
 * Test Script untuk Sub-Accordion Pamong dan Wali <PERSON>
 * Script ini akan menambahkan data test catatan Pamong dan Wali Kelas untuk menguji implementasi sub-accordion
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/CatatanSiswa.php';
require_once __DIR__ . '/app/models/Siswa.php';

echo "<h1>🧪 Test Sub-Accordion Pamong & Wali Kelas Implementation</h1>";

try {
    $db = new Database();
    $catatanModel = new CatatanSiswa();
    $siswaModel = new Siswa();
    
    echo "<h2>📋 Checking Prerequisites</h2>";
    
    // Check if we have test student
    $testSiswa = $db->fetchOne("SELECT * FROM siswa LIMIT 1");
    if (!$testSiswa) {
        echo "<p style='color: red;'>❌ No test student found. Please add a student first.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Test student found: {$testSiswa['nama_lengkap']} (ID: {$testSiswa['id_siswa']})</p>";
    
    // Check if Pamong categories exist
    $pamongCategories = $db->fetchAll("SELECT * FROM kategori_catatan WHERE kode_kategori LIKE 'pamong_%'");
    echo "<p style='color: green;'>✅ Found " . count($pamongCategories) . " Pamong categories</p>";
    
    // Check if Wali Kelas categories exist
    $waliKelasCategories = $db->fetchAll("SELECT * FROM kategori_catatan WHERE kode_kategori LIKE 'wali_%'");
    echo "<p style='color: green;'>✅ Found " . count($waliKelasCategories) . " Wali Kelas categories</p>";
    
    // Add missing categories if needed
    if (count($pamongCategories) < 4 || count($waliKelasCategories) < 5) {
        echo "<p style='color: orange;'>⚠️ Adding missing categories...</p>";
        
        $requiredCategories = [
            // Pamong
            ['pamong_mp', 'Pamong MP', 'Catatan Pamong Masa Persiapan (KPP)', '#17a2b8', 'bi-person-badge'],
            ['pamong_mt', 'Pamong MT', 'Catatan Pamong Masa Transisi (X)', '#28a745', 'bi-person-check'],
            ['pamong_mm', 'Pamong MM', 'Catatan Pamong Masa Mandiri (XI)', '#ffc107', 'bi-person-gear'],
            ['pamong_mu', 'Pamong MU', 'Catatan Pamong Masa Uji (XII & KPA)', '#fd7e14', 'bi-person-exclamation'],
            
            // Wali Kelas
            ['wali_kpp', 'Wali Kelas KPP', 'Catatan Wali Kelas Persiapan Profesi', '#6f42c1', 'bi-mortarboard'],
            ['wali_x', 'Wali Kelas X', 'Catatan Wali Kelas X', '#007bff', 'bi-book'],
            ['wali_xi', 'Wali Kelas XI', 'Catatan Wali Kelas XI', '#0d6efd', 'bi-journal'],
            ['wali_xii', 'Wali Kelas XII', 'Catatan Wali Kelas XII', '#6610f2', 'bi-graduation'],
            ['wali_kpa', 'Wali Kelas KPA', 'Catatan Wali Kelas Pasca', '#e83e8c', 'bi-award']
        ];
        
        foreach ($requiredCategories as $cat) {
            try {
                $db->query("INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES (?, ?, ?, ?, ?)", $cat);
                echo "<p style='color: green;'>✅ Added category: {$cat[1]}</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Category {$cat[1]} already exists</p>";
            }
        }
    }
    
    echo "<h2>📝 Adding Test Pamong Records</h2>";
    
    // Sample Pamong catatan data
    $testCatatanPamong = [
        // Pamong MP (KPP)
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'pamong_mp',
            'judul_catatan' => 'Orientasi Siswa Baru KPP',
            'isi_catatan' => 'Siswa mengikuti program orientasi dengan baik. Menunjukkan antusiasme tinggi dalam mengikuti kegiatan persiapan profesi. Perlu bimbingan lebih lanjut dalam memahami tata tertib sekolah.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-8 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Lanjutkan bimbingan tata tertib dan monitor adaptasi',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+7 days')),
            'created_by' => 1
        ],
        
        // Pamong MT (X)
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'pamong_mt',
            'judul_catatan' => 'Evaluasi Transisi ke Kelas X',
            'isi_catatan' => 'Siswa menunjukkan kemajuan yang baik dalam masa transisi dari KPP ke kelas X. Kemampuan akademik meningkat dan mulai beradaptasi dengan sistem pembelajaran yang lebih mandiri.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-6 days')),
            'tingkat_prioritas' => 'rendah',
            'status_catatan' => 'selesai',
            'tindak_lanjut' => null,
            'tanggal_tindak_lanjut' => null,
            'created_by' => 1
        ],
        
        // Pamong MM (XI)
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'pamong_mm',
            'judul_catatan' => 'Pengembangan Kemandirian Belajar',
            'isi_catatan' => 'Siswa telah menunjukkan kemandirian yang baik dalam belajar. Mampu mengatur waktu belajar sendiri dan aktif dalam diskusi kelas. Perlu ditingkatkan kemampuan leadership.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-4 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Berikan kesempatan memimpin proyek kelas',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+14 days')),
            'created_by' => 1
        ],
        
        // Pamong MU (XII & KPA)
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'pamong_mu',
            'judul_catatan' => 'Persiapan Ujian Akhir',
            'isi_catatan' => 'Siswa dalam tahap persiapan ujian akhir. Menunjukkan dedikasi tinggi dalam belajar. Perlu dukungan dalam manajemen stress dan persiapan mental menghadapi ujian.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-2 days')),
            'tingkat_prioritas' => 'tinggi',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Konseling persiapan ujian dan manajemen stress',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+3 days')),
            'created_by' => 1
        ]
    ];
    
    echo "<h2>📝 Adding Test Wali Kelas Records</h2>";
    
    // Sample Wali Kelas catatan data
    $testCatatanWaliKelas = [
        // Wali KPP
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'wali_kpp',
            'judul_catatan' => 'Monitoring Kehadiran KPP',
            'isi_catatan' => 'Tingkat kehadiran siswa sangat baik (98%). Aktif dalam kegiatan kelas dan menunjukkan motivasi belajar yang tinggi. Hubungan dengan teman sekelas juga baik.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-7 days')),
            'tingkat_prioritas' => 'rendah',
            'status_catatan' => 'selesai',
            'tindak_lanjut' => null,
            'tanggal_tindak_lanjut' => null,
            'created_by' => 1
        ],
        
        // Wali X
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'wali_x',
            'judul_catatan' => 'Evaluasi Semester Kelas X',
            'isi_catatan' => 'Prestasi akademik siswa di kelas X cukup memuaskan. Nilai rata-rata 8.2. Perlu peningkatan di mata pelajaran Matematika dan Fisika. Sikap dan perilaku sangat baik.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-5 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Bimbingan belajar Matematika dan Fisika',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+10 days')),
            'created_by' => 1
        ],
        
        // Wali XI
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'wali_xi',
            'judul_catatan' => 'Pemilihan Jurusan Kuliah',
            'isi_catatan' => 'Diskusi dengan siswa mengenai rencana studi lanjut. Siswa tertarik pada bidang Teknik Informatika. Prestasi di bidang eksata mendukung pilihan tersebut.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-3 days')),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Koordinasi dengan guru BK untuk konseling karir',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+7 days')),
            'created_by' => 1
        ],
        
        // Wali XII
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'wali_xii',
            'judul_catatan' => 'Persiapan Kelulusan',
            'isi_catatan' => 'Siswa dalam tahap persiapan kelulusan. Semua persyaratan akademik telah terpenuhi. Perlu finalisasi berkas untuk pendaftaran universitas.',
            'tanggal_catatan' => date('Y-m-d', strtotime('-1 day')),
            'tingkat_prioritas' => 'tinggi',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Bantu finalisasi berkas pendaftaran universitas',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+5 days')),
            'created_by' => 1
        ],
        
        // Wali KPA
        [
            'siswa_id' => $testSiswa['id_siswa'],
            'jenis_catatan' => 'wali_kpa',
            'judul_catatan' => 'Program Akselerasi',
            'isi_catatan' => 'Siswa mengikuti program akselerasi dengan sangat baik. Kemampuan analisis dan pemecahan masalah di atas rata-rata. Siap untuk tantangan yang lebih tinggi.',
            'tanggal_catatan' => date('Y-m-d'),
            'tingkat_prioritas' => 'sedang',
            'status_catatan' => 'aktif',
            'tindak_lanjut' => 'Monitor progress dan berikan tantangan tambahan',
            'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+14 days')),
            'created_by' => 1
        ]
    ];
    
    // Combine all test data
    $allTestCatatan = array_merge($testCatatanPamong, $testCatatanWaliKelas);
    
    // Insert test data
    $successCount = 0;
    foreach ($allTestCatatan as $catatan) {
        try {
            $result = $catatanModel->create($catatan);
            if ($result) {
                echo "<p style='color: green;'>✅ Added: {$catatan['judul_catatan']} ({$catatan['jenis_catatan']})</p>";
                $successCount++;
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Failed to add: {$catatan['judul_catatan']} - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📊 Test Results</h2>";
    echo "<p style='color: green;'>✅ Successfully added {$successCount} test records</p>";
    
    // Test the new methods
    echo "<h2>🧪 Testing New Grouping Methods</h2>";
    
    echo "<h3>Pamong Grouped:</h3>";
    $pamongGrouped = $catatanModel->getPamongGroupedBySiswaId($testSiswa['id_siswa']);
    foreach ($pamongGrouped as $jenis => $catatanList) {
        $count = count($catatanList);
        if ($count > 0) {
            echo "<p style='color: green;'>✅ {$jenis}: {$count} records</p>";
        } else {
            echo "<p style='color: gray;'>⚪ {$jenis}: 0 records</p>";
        }
    }
    
    echo "<h3>Wali Kelas Grouped:</h3>";
    $waliKelasGrouped = $catatanModel->getWaliKelasGroupedBySiswaId($testSiswa['id_siswa']);
    foreach ($waliKelasGrouped as $jenis => $catatanList) {
        $count = count($catatanList);
        if ($count > 0) {
            echo "<p style='color: green;'>✅ {$jenis}: {$count} records</p>";
        } else {
            echo "<p style='color: gray;'>⚪ {$jenis}: 0 records</p>";
        }
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/siswa/detail/{$testSiswa['id_siswa']}' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 View Student Detail Page (Test All Sub-Accordions)
    </a></p>";
    
    echo "<p><a href='/siswa-app/public/catatan/add/{$testSiswa['id_siswa']}' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Add More Records
    </a></p>";
    
    echo "<h2>✅ Test Complete</h2>";
    echo "<p style='color: green; font-weight: bold;'>
        Sub-accordion implementation for Pamong and Wali Kelas is ready for testing! 
        Visit the student detail page to see all sub-categories in action.
    </p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
