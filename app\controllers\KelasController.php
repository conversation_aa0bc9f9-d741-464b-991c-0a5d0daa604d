<?php
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../helpers/Security.php';

class KelasController {
    private $kelasModel;

    public function __construct() {
        $this->kelasModel = new Kelas();
    }

    public function index() {
        require_once __DIR__ . '/../helpers/AcademicYear.php';

        $selectedYear = AcademicYear::getSelectedAcademicYear();
        $data = [
            'title' => 'Daftar Kelas - Tahun Ajaran ' . $selectedYear,
            'kelas' => $this->kelasModel->getAll($selectedYear),
            'selected_academic_year' => $selectedYear,
            'academic_year_display' => AcademicYear::getAcademicYearDisplayText($selectedYear),
            'csrf_token' => Security::generateCSRFToken()
        ];
        $this->view('kelas/list', $data);
    }

    public function create() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas/create');
                exit;
            }

            $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
            $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
            $kurikulum = Security::sanitizeInput($_POST['kurikulum'] ?? '');
            $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
            $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');
            $wali_kelas_id = (int)($_POST['wali_kelas_id'] ?? 0);
            $kapasitas = (int)($_POST['kapasitas'] ?? 30);

            if ($nama_kelas && $tingkat && $tahun_pelajaran && $wali_kelas_id) {
                $data = [
                    'nama_kelas' => $nama_kelas,
                    'tingkat' => $tingkat,
                    'kurikulum' => $kurikulum,
                    'tahun_pelajaran' => $tahun_pelajaran,
                    'wali_kelas' => $wali_kelas,
                    'wali_kelas_id' => $wali_kelas_id,
                    'kapasitas' => $kapasitas,
                    'created_by' => $_SESSION['user_id'] ?? 1
                ];

                try {
                    $result = $this->kelasModel->createKelas($data);
                    if ($result) {
                        $_SESSION['success'] = 'Kelas berhasil ditambahkan.';
                        header('Location: /siswa-app/public/kelas');
                        exit;
                    } else {
                        $_SESSION['error'] = 'Gagal menambahkan kelas. Model returned false.';
                        error_log("Kelas creation failed: Model returned false for data: " . print_r($data, true));
                    }
                } catch (Exception $e) {
                    $_SESSION['error'] = 'Gagal menambahkan kelas: ' . $e->getMessage();
                    error_log("Kelas creation exception: " . $e->getMessage() . " | Data: " . print_r($data, true));
                }
            } else {
                $_SESSION['error'] = 'Nama kelas, tingkat, tahun pelajaran, dan wali kelas wajib diisi.';
            }
        }

        // Get selected academic year for default value
        require_once __DIR__ . '/../helpers/AcademicYear.php';
        $selectedYear = AcademicYear::getSelectedAcademicYear();

        // Get wali kelas list
        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();
        $waliKelasList = $userModel->getUsersByRole('wali_kelas');

        $data = [
            'title' => 'Tambah Kelas',
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null,
            'default_academic_year' => $selectedYear,
            'wali_kelas_list' => $waliKelasList
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('kelas/form', $data);
    }

    public function edit($id) {
        $kelas = $this->kelasModel->getById($id);
        if (!$kelas) {
            $_SESSION['error'] = 'Kelas tidak ditemukan.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas/edit/' . $id);
                exit;
            }

            $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
            $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
            $kurikulum = Security::sanitizeInput($_POST['kurikulum'] ?? '');
            $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
            $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');
            $wali_kelas_id = (int)($_POST['wali_kelas_id'] ?? 0);
            $kapasitas = (int)($_POST['kapasitas'] ?? 30);

            if ($nama_kelas && $tingkat && $tahun_pelajaran && $wali_kelas_id) {
                $data = [
                    'nama_kelas' => $nama_kelas,
                    'tingkat' => $tingkat,
                    'kurikulum' => $kurikulum,
                    'tahun_pelajaran' => $tahun_pelajaran,
                    'wali_kelas' => $wali_kelas,
                    'wali_kelas_id' => $wali_kelas_id,
                    'kapasitas' => $kapasitas,
                    'updated_by' => $_SESSION['user_id'] ?? 1
                ];

                $result = $this->kelasModel->updateKelas($id, $data);
                if ($result) {
                    $_SESSION['success'] = 'Kelas berhasil diperbarui.';
                    header('Location: /siswa-app/public/kelas');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal memperbarui kelas.';
                }
            } else {
                $_SESSION['error'] = 'Nama kelas, tingkat, tahun pelajaran, dan wali kelas wajib diisi.';
            }
        }

        // Get wali kelas list
        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();
        $waliKelasList = $userModel->getUsersByRole('wali_kelas');

        $data = [
            'title' => 'Edit Kelas',
            'kelas' => $kelas,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null,
            'wali_kelas_list' => $waliKelasList
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('kelas/form', $data);
    }

    public function detail($id) {
        $kelas = $this->kelasModel->getById($id);
        if (!$kelas) {
            $_SESSION['error'] = 'Kelas tidak ditemukan.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        // Get students in this class
        require_once __DIR__ . '/../models/Siswa.php';
        $siswaModel = new Siswa();
        $siswaList = $siswaModel->getByKelasId($id);

        $data = [
            'title' => 'Detail Kelas - ' . $kelas['nama_kelas'],
            'kelas' => $kelas,
            'siswa_list' => $siswaList,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('kelas/detail', $data);
    }

    public function delete($id) {
        Security::requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas');
                exit;
            }

            $result = $this->kelasModel->deleteKelas($id);
            if ($result) {
                $_SESSION['success'] = 'Kelas berhasil dihapus.';
            } else {
                $_SESSION['error'] = 'Gagal menghapus kelas.';
            }
        } else {
            // For GET request, redirect to delete confirmation
            $kelas = $this->kelasModel->getById($id);
            if (!$kelas) {
                $_SESSION['error'] = 'Kelas tidak ditemukan.';
                header('Location: /siswa-app/public/kelas');
                exit;
            }

            $result = $this->kelasModel->deleteKelas($id);
            if ($result) {
                $_SESSION['success'] = 'Kelas berhasil dihapus.';
            } else {
                $_SESSION['error'] = 'Gagal menghapus kelas.';
            }
        }

        header('Location: /siswa-app/public/kelas');
        exit;
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>