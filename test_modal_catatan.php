<?php
/**
 * Test Modal Tambah Catatan
 * Test untuk memastikan modal form berfungsi dengan baik
 */

// Start session
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'admin';

require_once 'app/models/Database.php';
require_once 'app/models/Siswa.php';
require_once 'app/models/CatatanSiswa.php';
require_once 'app/helpers/Security.php';

echo "<h2>🧪 Test Modal Tambah Catatan</h2>";

try {
    $db = new Database();
    $siswaModel = new Siswa();
    $catatanModel = new CatatanSiswa();
    
    // Get test siswa
    $siswa = $siswaModel->getById(1);
    if (!$siswa) {
        echo "<p style='color: red;'>❌ Test siswa tidak ditemukan</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Test siswa: {$siswa['nama_lengkap']}</p>";
    
    // Generate CSRF token
    $csrf_token = Security::generateCSRFToken();
    
    // Get categories
    $categories = $catatanModel->getCategoriesGrouped();
    $filteredCategories = Security::filterCatatanCategoriesByRole($categories);
    
    echo "<p style='color: green;'>✅ Categories loaded: " . count($filteredCategories) . " groups</p>";
    
    // Test form submission simulation
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<h3>📝 Processing Form Submission</h3>";
        
        // Simulate form data
        $formData = [
            'csrf_token' => $_POST['csrf_token'] ?? '',
            'siswa_id' => $_POST['siswa_id'] ?? '',
            'jenis_catatan' => $_POST['jenis_catatan'] ?? '',
            'judul_catatan' => $_POST['judul_catatan'] ?? '',
            'isi_catatan' => $_POST['isi_catatan'] ?? '',
            'tanggal_catatan' => $_POST['tanggal_catatan'] ?? '',
            'tingkat_prioritas' => $_POST['tingkat_prioritas'] ?? 'sedang',
            'status_catatan' => $_POST['status_catatan'] ?? 'aktif',
            'tindak_lanjut' => $_POST['tindak_lanjut'] ?? '',
            'tanggal_tindak_lanjut' => $_POST['tanggal_tindak_lanjut'] ?? ''
        ];
        
        echo "<pre>" . print_r($formData, true) . "</pre>";
        
        // Validate CSRF
        if (Security::validateCSRFToken($formData['csrf_token'])) {
            echo "<p style='color: green;'>✅ CSRF token valid</p>";
        } else {
            echo "<p style='color: red;'>❌ CSRF token invalid</p>";
        }
        
        // Try to create catatan
        try {
            $result = $catatanModel->create([
                'siswa_id' => $formData['siswa_id'],
                'jenis_catatan' => $formData['jenis_catatan'],
                'judul_catatan' => $formData['judul_catatan'],
                'isi_catatan' => $formData['isi_catatan'],
                'tanggal_catatan' => $formData['tanggal_catatan'],
                'tingkat_prioritas' => $formData['tingkat_prioritas'],
                'status_catatan' => $formData['status_catatan'],
                'tindak_lanjut' => !empty($formData['tindak_lanjut']) ? $formData['tindak_lanjut'] : null,
                'tanggal_tindak_lanjut' => !empty($formData['tanggal_tindak_lanjut']) ? $formData['tanggal_tindak_lanjut'] : null,
                'created_by' => 1
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Catatan berhasil dibuat dengan ID: {$result}</p>";
                echo "<p><a href='public/siswa/detail/1'>👀 Lihat hasil di detail siswa</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Gagal membuat catatan</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
    }
    
    ?>
    
    <!DOCTYPE html>
    <html lang="id">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Test Modal Catatan</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h3>🎯 Test Modal Form</h3>
            <p>Klik tombol di bawah untuk membuka modal tambah catatan:</p>
            
            <button class="btn btn-info text-white" data-bs-toggle="modal" data-bs-target="#addCatatanModal">
                <i class="bi bi-plus-circle me-1"></i>
                Test Modal Tambah Catatan
            </button>
            
            <div class="mt-3">
                <h5>Form Test Manual:</h5>
                <form method="POST" class="border p-3 rounded">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">Jenis Catatan</label>
                        <select name="jenis_catatan" class="form-select" required>
                            <option value="">Pilih Jenis</option>
                            <?php foreach ($filteredCategories as $groupName => $groupCategories): ?>
                                <optgroup label="<?= ucfirst(str_replace('_', ' ', $groupName)) ?>">
                                    <?php foreach ($groupCategories as $category): ?>
                                    <option value="<?= $category['kode_kategori'] ?>">
                                        <?= $category['nama_kategori'] ?>
                                    </option>
                                    <?php endforeach; ?>
                                </optgroup>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Judul Catatan</label>
                        <input type="text" name="judul_catatan" class="form-control" value="Test Modal Catatan - <?= date('H:i:s') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Isi Catatan</label>
                        <textarea name="isi_catatan" class="form-control" rows="3" required>Ini adalah test catatan dari modal form yang telah diperbaiki.</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Tanggal</label>
                        <input type="date" name="tanggal_catatan" class="form-control" value="<?= date('Y-m-d') ?>" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send me-1"></i>
                        Test Submit
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Modal yang sama seperti di detail siswa -->
        <div class="modal fade" id="addCatatanModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-journal-plus me-2"></i>
                            Tambah Catatan Siswa
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="<?= $_SERVER['PHP_SELF'] ?>" method="POST" id="modalCatatanForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                            <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?>">
                            
                            <div id="modalAlertContainer"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="modal_jenis_catatan" class="form-label">
                                            <i class="bi bi-tag me-1"></i>Jenis Catatan
                                        </label>
                                        <select class="form-select" id="modal_jenis_catatan" name="jenis_catatan" required>
                                            <option value="">Pilih Jenis Catatan</option>
                                            <?php foreach ($filteredCategories as $groupName => $groupCategories): ?>
                                                <optgroup label="<?= ucfirst(str_replace('_', ' ', $groupName)) ?>">
                                                    <?php foreach ($groupCategories as $category): ?>
                                                    <option value="<?= $category['kode_kategori'] ?>">
                                                        <?= $category['nama_kategori'] ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </optgroup>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="modal_tanggal_catatan" class="form-label">
                                            <i class="bi bi-calendar me-1"></i>Tanggal
                                        </label>
                                        <input type="date" class="form-control" id="modal_tanggal_catatan" name="tanggal_catatan" value="<?= date('Y-m-d') ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="modal_judul_catatan" class="form-label">
                                    <i class="bi bi-pencil me-1"></i>Judul Catatan
                                </label>
                                <input type="text" class="form-control" id="modal_judul_catatan" name="judul_catatan" placeholder="Masukkan judul catatan..." maxlength="255" required>
                                <div class="form-text">
                                    <small class="text-muted" id="modalJudulCounter">0/255 karakter</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="modal_isi_catatan" class="form-label">
                                    <i class="bi bi-journal-text me-1"></i>Isi Catatan
                                </label>
                                <textarea class="form-control" id="modal_isi_catatan" name="isi_catatan" rows="5" placeholder="Tulis catatan lengkap di sini..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-info text-white">
                                <i class="bi bi-save me-1"></i>
                                Simpan Catatan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
        // Copy validation script dari detail siswa
        document.addEventListener('DOMContentLoaded', function() {
            const modalCatatanForm = document.getElementById('modalCatatanForm');
            const modalJudulInput = document.getElementById('modal_judul_catatan');
            const modalJudulCounter = document.getElementById('modalJudulCounter');
            
            // Character counter
            if (modalJudulInput && modalJudulCounter) {
                modalJudulInput.addEventListener('input', function() {
                    const length = this.value.length;
                    modalJudulCounter.textContent = `${length}/255 karakter`;
                    
                    if (length > 200) {
                        modalJudulCounter.className = 'text-warning';
                    } else if (length > 240) {
                        modalJudulCounter.className = 'text-danger';
                    } else {
                        modalJudulCounter.className = 'text-muted';
                    }
                });
            }
        });
        </script>
    </body>
    </html>
    
    <?php
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='public/siswa/detail/1'>👤 Kembali ke Detail Siswa</a></p>";
echo "<p><a href='test_tambah_catatan.php'>🧪 Test Functionality</a></p>";
?>
