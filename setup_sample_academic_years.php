<?php
/**
 * Setup Sample Academic Years
 * Script ini akan menambahkan beberapa tahun ajaran contoh untuk testing
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/helpers/AcademicYear.php';

echo "<h1>🔧 Setup Sample Academic Years</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Current System Status</h2>";
    
    // Check current academic year
    $currentYear = AcademicYear::getCurrentAcademicYear();
    echo "<p style='color: green;'>✅ Current Academic Year: <strong>{$currentYear}</strong></p>";
    
    // Check existing years
    $existingYears = AcademicYear::getAvailableAcademicYears();
    echo "<p style='color: blue;'>ℹ️ Existing Years: " . count($existingYears) . " years</p>";
    foreach ($existingYears as $year) {
        echo "<p style='margin-left: 20px;'>📅 {$year}</p>";
    }
    
    echo "<h2>🎯 Adding Sample Academic Years</h2>";
    
    // Sample years to add (including historical years)
    $sampleYears = [
        '2020/2021',
        '2021/2022', 
        '2022/2023',
        '2023/2024',
        '2024/2025',
        '2025/2026'
    ];
    
    $addedYears = [];
    $skippedYears = [];
    
    foreach ($sampleYears as $year) {
        if (in_array($year, $existingYears)) {
            $skippedYears[] = $year;
            echo "<p style='color: orange;'>⚠️ Skipped {$year} (already exists)</p>";
            continue;
        }
        
        try {
            // Create placeholder entry for the academic year
            $db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                'PLACEHOLDER_' . str_replace('/', '_', $year),
                0, // Special tingkat for placeholder
                $year,
                'System Generated (Sample)',
                0,
                false, // Not active
                1 // Assuming user ID 1 exists
            ]);
            
            $addedYears[] = $year;
            echo "<p style='color: green;'>✅ Added {$year}</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add {$year}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📊 Adding Sample Data for Some Years</h2>";
    
    // Add some sample classes for 2023/2024 to make it look realistic
    $sampleClasses = [
        ['X-IPA-1', 10, '2023/2024', 'Pak Budi', 30, true],
        ['X-IPA-2', 10, '2023/2024', 'Bu Sari', 28, true],
        ['XI-IPA-1', 11, '2023/2024', 'Pak Joko', 25, true],
        ['XI-IPA-2', 11, '2023/2024', 'Bu Rina', 27, true],
        ['XII-IPA-1', 12, '2023/2024', 'Pak Andi', 22, false], // Graduated
        ['XII-IPA-2', 12, '2023/2024', 'Bu Maya', 24, false], // Graduated
    ];
    
    foreach ($sampleClasses as $kelas) {
        try {
            // Check if class already exists
            $existing = $db->fetch("
                SELECT id FROM kelas 
                WHERE nama_kelas = ? AND tahun_pelajaran = ?
            ", [$kelas[0], $kelas[2]]);
            
            if ($existing) {
                echo "<p style='color: orange;'>⚠️ Class {$kelas[0]} already exists</p>";
                continue;
            }
            
            $db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                $kelas[0], // nama_kelas
                $kelas[1], // tingkat
                $kelas[2], // tahun_pelajaran
                $kelas[3], // wali_kelas
                $kelas[4], // kapasitas
                $kelas[5], // is_active
                1 // created_by
            ]);
            
            echo "<p style='color: green;'>✅ Added class {$kelas[0]} for {$kelas[2]}</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add class {$kelas[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    // Add some sample classes for current year
    $currentYearClasses = [
        ['X-IPA-1', 10, $currentYear, 'Pak Ahmad', 32, true],
        ['X-IPA-2', 10, $currentYear, 'Bu Dewi', 30, true],
        ['X-IPS-1', 10, $currentYear, 'Pak Rudi', 28, true],
        ['XI-IPA-1', 11, $currentYear, 'Bu Lina', 29, true],
        ['XI-IPA-2', 11, $currentYear, 'Pak Hadi', 31, true],
        ['XI-IPS-1', 11, $currentYear, 'Bu Tari', 26, true],
        ['XII-IPA-1', 12, $currentYear, 'Pak Doni', 27, true],
        ['XII-IPA-2', 12, $currentYear, 'Bu Sinta', 25, true],
        ['XII-IPS-1', 12, $currentYear, 'Pak Yudi', 24, true],
    ];
    
    foreach ($currentYearClasses as $kelas) {
        try {
            // Check if class already exists
            $existing = $db->fetch("
                SELECT id FROM kelas 
                WHERE nama_kelas = ? AND tahun_pelajaran = ?
            ", [$kelas[0], $kelas[2]]);
            
            if ($existing) {
                echo "<p style='color: orange;'>⚠️ Class {$kelas[0]} already exists for current year</p>";
                continue;
            }
            
            $db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                $kelas[0], // nama_kelas
                $kelas[1], // tingkat
                $kelas[2], // tahun_pelajaran
                $kelas[3], // wali_kelas
                $kelas[4], // kapasitas
                $kelas[5], // is_active
                1 // created_by
            ]);
            
            echo "<p style='color: green;'>✅ Added class {$kelas[0]} for current year {$kelas[2]}</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add class {$kelas[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📈 Final Statistics</h2>";
    
    // Get updated statistics
    $updatedYears = AcademicYear::getAvailableAcademicYears();
    echo "<p style='color: green;'>✅ Total Academic Years: <strong>" . count($updatedYears) . "</strong></p>";
    
    foreach ($updatedYears as $year) {
        // Count classes for this year
        $classCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM kelas 
            WHERE tahun_pelajaran = ? AND tingkat > 0
        ", [$year]);
        
        $activeClassCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM kelas 
            WHERE tahun_pelajaran = ? AND tingkat > 0 AND is_active = 1
        ", [$year]);
        
        $isCurrent = AcademicYear::isCurrentAcademicYear($year) ? ' (Current)' : '';
        echo "<p style='margin-left: 20px;'>📅 {$year}{$isCurrent}: " . 
             ($classCount['count'] ?? 0) . " classes (" . 
             ($activeClassCount['count'] ?? 0) . " active)</p>";
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/academic-year-management' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Open Academic Year Management Page
    </a></p>";
    
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 View Classes (Test Academic Year Selector)
    </a></p>";
    
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 View Students (Test Academic Year Selector)
    </a></p>";
    
    echo "<h2>✅ Setup Complete</h2>";
    echo "<p style='color: green; font-weight: bold;'>
        Sample academic years and classes have been set up successfully! 
        You can now test the Academic Year Management features.
    </p>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎯 What You Can Test Now:</h4>";
    echo "<ul>";
    echo "<li><strong>Academic Year Management:</strong> View, add, and manage academic years</li>";
    echo "<li><strong>Academic Year Selector:</strong> Switch between years in navbar</li>";
    echo "<li><strong>Statistics:</strong> See class and student counts per year</li>";
    echo "<li><strong>Navigation:</strong> Jump directly to classes/students for specific years</li>";
    echo "<li><strong>Bulk Generation:</strong> Test adding multiple years at once</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
