<?php
/**
 * Script untuk Implementasi Manajemen Wali Kelas yang Fleksibel
 * 
 * Sistem ini memungkinkan:
 * 1. Satu user bisa jadi wali beberapa kelas
 * 2. Role spesifik per kelas (wali_kelas_kpp_a, dll)
 * 3. Manaj<PERSON>en yang mudah melalui interface
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';

echo "<h1>🔧 Implementasi Manajemen Wali Kelas Fleksibel</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Opsi Implementasi</h3>";
    echo "<p>Pilih salah satu pendekatan:</p>";
    echo "<ol>";
    echo "<li><strong>Role Spesifik per Kelas</strong> - Role seperti 'wali_kelas_kpp_a'</li>";
    echo "<li><strong>Role dengan Metadata JSON</strong> - Role 'wali_kelas' + metadata</li>";
    echo "<li><strong>Sistem Mapping Fleksibel</strong> - Menggunakan tabel user_kelas_mapping</li>";
    echo "</ol>";
    echo "</div>";
    
    // Implementasi Opsi 1: Role Spesifik per Kelas
    echo "<h2>📋 Opsi 1: Role Spesifik per Kelas</h2>";
    
    echo "<h4>Struktur Role yang Diusulkan:</h4>";
    $proposedRoles = [
        'admin' => 'Administrator sistem',
        'pamong_mp' => 'Pamong Muda Pertama',
        'pamong_mt' => 'Pamong Muda Tingkat',
        'pamong_mm' => 'Pamong Muda Madya',
        'pamong_mu' => 'Pamong Muda Utama',
        'wali_kelas_kpp_a' => 'Wali Kelas KPP A',
        'wali_kelas_kpp_b' => 'Wali Kelas KPP B', 
        'wali_kelas_kpp_c' => 'Wali Kelas KPP C',
        'wali_kelas_x_1' => 'Wali Kelas X-1',
        'wali_kelas_x_2' => 'Wali Kelas X-2',
        'wali_kelas_xi_1' => 'Wali Kelas XI-1',
        'wali_kelas_xi_2' => 'Wali Kelas XI-2',
        'wali_kelas_xii_1' => 'Wali Kelas XII-1',
        'wali_kelas_xii_2' => 'Wali Kelas XII-2',
        'wali_kelas_kpa' => 'Wali Kelas KPA',
        'staff' => 'Staff umum'
    ];
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Role</th><th>Deskripsi</th><th>Akses</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($proposedRoles as $role => $desc) {
        $access = 'Semua data';
        if (strpos($role, 'wali_kelas_') === 0) {
            $kelasName = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $role));
            $access = "Hanya kelas {$kelasName}";
        }
        
        $highlight = strpos($role, 'wali_kelas_') === 0 ? "style='background: #fff3cd;'" : "";
        echo "<tr {$highlight}>";
        echo "<td><code>{$role}</code></td>";
        echo "<td>{$desc}</td>";
        echo "<td>{$access}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Implementasi Opsi 2: Role dengan Metadata
    echo "<h2>📋 Opsi 2: Role dengan Metadata JSON</h2>";
    
    echo "<h4>Struktur Database:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo "ALTER TABLE users ADD COLUMN role_metadata JSON NULL AFTER role;\n\n";
    echo "-- Contoh data:\n";
    echo "role = 'wali_kelas'\n";
    echo "role_metadata = {\n";
    echo "  \"kelas\": [\"KPP-A\", \"KPP-B\"],\n";
    echo "  \"tingkat\": \"KPP\",\n";
    echo "  \"tahun\": \"2025/2026\",\n";
    echo "  \"permissions\": [\"view_siswa\", \"edit_siswa\"]\n";
    echo "}";
    echo "</pre>";
    
    // Implementasi Opsi 3: Sistem Mapping
    echo "<h2>📋 Opsi 3: Sistem Mapping Fleksibel (Direkomendasikan)</h2>";
    
    // Cek apakah tabel user_kelas_mapping sudah ada
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_kelas_mapping'");
    $hasMappingTable = $stmt->rowCount() > 0;
    
    if ($hasMappingTable) {
        echo "<p style='color: green;'>✅ Tabel user_kelas_mapping sudah ada</p>";
        
        // Tampilkan struktur tabel
        $stmt = $pdo->query("DESCRIBE user_kelas_mapping");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Struktur Tabel user_kelas_mapping:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        // Contoh implementasi
        echo "<h4>Contoh Implementasi Mapping:</h4>";
        
        // Get sample data
        $stmt = $pdo->query("SELECT id, nama_kelas, tingkat FROM kelas WHERE is_active = 1 ORDER BY tingkat, nama_kelas LIMIT 10");
        $sampleKelas = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $stmt = $pdo->query("SELECT id, username, nama_lengkap FROM users WHERE role = 'wali_kelas' LIMIT 5");
        $sampleUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($sampleKelas) && !empty($sampleUsers)) {
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<thead style='background: #f8f9fa;'>";
            echo "<tr><th>User</th><th>Kelas yang Dikelola</th><th>Role Type</th><th>Status</th></tr>";
            echo "</thead><tbody>";
            
            // Contoh mapping
            $exampleMappings = [
                ['user' => $sampleUsers[0] ?? null, 'kelas' => [$sampleKelas[0] ?? null], 'role_type' => 'wali_kelas'],
                ['user' => $sampleUsers[1] ?? null, 'kelas' => [$sampleKelas[1] ?? null, $sampleKelas[2] ?? null], 'role_type' => 'wali_kelas'],
                ['user' => $sampleUsers[2] ?? null, 'kelas' => [$sampleKelas[3] ?? null], 'role_type' => 'wali_kelas']
            ];
            
            foreach ($exampleMappings as $mapping) {
                if ($mapping['user'] && !empty($mapping['kelas'])) {
                    $kelasNames = array_map(function($k) { return $k['nama_kelas'] ?? ''; }, $mapping['kelas']);
                    echo "<tr>";
                    echo "<td>{$mapping['user']['nama_lengkap']} ({$mapping['user']['username']})</td>";
                    echo "<td>" . implode(', ', $kelasNames) . "</td>";
                    echo "<td><span style='background: #28a745; color: white; padding: 2px 8px; border-radius: 3px;'>{$mapping['role_type']}</span></td>";
                    echo "<td><span style='background: #17a2b8; color: white; padding: 2px 8px; border-radius: 3px;'>Aktif</span></td>";
                    echo "</tr>";
                }
            }
            echo "</tbody></table>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ Tabel user_kelas_mapping belum ada</p>";
        echo "<p>Silakan jalankan script fix_wali_kelas_database.php terlebih dahulu.</p>";
    }
    
    // Keuntungan masing-masing opsi
    echo "<h2>⚖️ Perbandingan Opsi</h2>";
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Aspek</th><th>Opsi 1: Role Spesifik</th><th>Opsi 2: Metadata JSON</th><th>Opsi 3: Mapping Table</th></tr>";
    echo "</thead><tbody>";
    
    $comparisons = [
        ['Aspek' => 'Kemudahan Implementasi', 'Opsi1' => '⭐⭐⭐⭐⭐', 'Opsi2' => '⭐⭐⭐', 'Opsi3' => '⭐⭐⭐⭐'],
        ['Aspek' => 'Fleksibilitas', 'Opsi1' => '⭐⭐', 'Opsi2' => '⭐⭐⭐⭐', 'Opsi3' => '⭐⭐⭐⭐⭐'],
        ['Aspek' => 'Skalabilitas', 'Opsi1' => '⭐⭐', 'Opsi2' => '⭐⭐⭐⭐', 'Opsi3' => '⭐⭐⭐⭐⭐'],
        ['Aspek' => 'Performance', 'Opsi1' => '⭐⭐⭐⭐⭐', 'Opsi2' => '⭐⭐⭐⭐', 'Opsi3' => '⭐⭐⭐⭐'],
        ['Aspek' => 'Maintenance', 'Opsi1' => '⭐⭐', 'Opsi2' => '⭐⭐⭐', 'Opsi3' => '⭐⭐⭐⭐⭐'],
        ['Aspek' => 'User Management', 'Opsi1' => '⭐⭐⭐', 'Opsi2' => '⭐⭐⭐⭐', 'Opsi3' => '⭐⭐⭐⭐⭐']
    ];
    
    foreach ($comparisons as $comp) {
        echo "<tr>";
        echo "<td><strong>{$comp['Aspek']}</strong></td>";
        echo "<td>{$comp['Opsi1']}</td>";
        echo "<td>{$comp['Opsi2']}</td>";
        echo "<td>{$comp['Opsi3']}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Rekomendasi
    echo "<h2>🎯 Rekomendasi</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🏆 Opsi 3: Sistem Mapping Fleksibel (Direkomendasikan)</h4>";
    echo "<p><strong>Keuntungan:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Sangat fleksibel</strong> - Satu user bisa wali beberapa kelas</li>";
    echo "<li>✅ <strong>Mudah maintenance</strong> - Tidak perlu ubah enum role</li>";
    echo "<li>✅ <strong>Skalabel</strong> - Mudah tambah kelas baru</li>";
    echo "<li>✅ <strong>Sudah ada infrastruktur</strong> - Tabel dan logic sudah dibuat</li>";
    echo "<li>✅ <strong>Support multiple role</strong> - Bisa jadi wali_kelas + guru_mapel</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚡ Opsi 1: Role Spesifik (Untuk Kesederhanaan)</h4>";
    echo "<p><strong>Cocok jika:</strong></p>";
    echo "<ul>";
    echo "<li>🎯 Satu user hanya wali satu kelas</li>";
    echo "<li>🎯 Struktur kelas tidak sering berubah</li>";
    echo "<li>🎯 Ingin implementasi yang sangat sederhana</li>";
    echo "</ul>";
    echo "</div>";
    
    // Next steps
    echo "<h2>📝 Langkah Selanjutnya</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Untuk Implementasi Opsi 3 (Mapping):</h4>";
    echo "<ol>";
    echo "<li>✅ <strong>Database sudah siap</strong> - Tabel user_kelas_mapping sudah ada</li>";
    echo "<li>🔄 <strong>Buat interface management</strong> - Form untuk assign user ke kelas</li>";
    echo "<li>🔄 <strong>Update Security helper</strong> - Method untuk cek mapping</li>";
    echo "<li>🔄 <strong>Update model queries</strong> - Join dengan mapping table</li>";
    echo "<li>🔄 <strong>Buat bulk assignment</strong> - Assign banyak kelas sekaligus</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Untuk Implementasi Opsi 1 (Role Spesifik):</h4>";
    echo "<ol>";
    echo "<li>🔄 <strong>Update enum role</strong> - Tambah role per kelas</li>";
    echo "<li>🔄 <strong>Update Security helper</strong> - Method isWaliKelas() untuk role spesifik</li>";
    echo "<li>🔄 <strong>Update model queries</strong> - Filter berdasarkan role spesifik</li>";
    echo "<li>🔄 <strong>Buat mapping role ke kelas</strong> - Helper function</li>";
    echo "<li>🔄 <strong>Update user management</strong> - Dropdown role per kelas</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Analisis selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
