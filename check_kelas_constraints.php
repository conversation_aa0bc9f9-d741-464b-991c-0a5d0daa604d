<?php
/**
 * Check Kelas Constraints
 * Script ini akan memeriksa constraint dan masalah potensial pada tabel kelas
 */

require_once __DIR__ . '/app/models/Database.php';

echo "<h1>🔍 Check Kelas Constraints</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Table Structure Analysis</h2>";
    
    // Check table structure
    $tableInfo = $db->fetchAll("SHOW CREATE TABLE kelas");
    echo "<h3>Table Creation SQL:</h3>";
    echo "<pre>" . htmlspecialchars($tableInfo[0]['Create Table']) . "</pre>";
    
    // Check indexes
    $indexes = $db->fetchAll("SHOW INDEX FROM kelas");
    echo "<h3>Table Indexes:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Key Name</th><th>Column</th><th>Unique</th><th>Type</th>";
    echo "</tr>";
    
    foreach ($indexes as $index) {
        $unique = $index['Non_unique'] == 0 ? 'YES' : 'NO';
        echo "<tr>";
        echo "<td>{$index['Key_name']}</td>";
        echo "<td>{$index['Column_name']}</td>";
        echo "<td>{$unique}</td>";
        echo "<td>{$index['Index_type']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔍 Data Analysis</h2>";
    
    // Check existing data
    $existingClasses = $db->fetchAll("
        SELECT nama_kelas, tingkat, tahun_pelajaran, COUNT(*) as count
        FROM kelas 
        GROUP BY nama_kelas, tingkat, tahun_pelajaran
        HAVING count > 1
    ");
    
    if (!empty($existingClasses)) {
        echo "<h3>⚠️ Duplicate Classes Found:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Nama Kelas</th><th>Tingkat</th><th>Tahun Pelajaran</th><th>Count</th>";
        echo "</tr>";
        
        foreach ($existingClasses as $class) {
            echo "<tr>";
            echo "<td>{$class['nama_kelas']}</td>";
            echo "<td>{$class['tingkat']}</td>";
            echo "<td>{$class['tahun_pelajaran']}</td>";
            echo "<td style='color: red; font-weight: bold;'>{$class['count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: green;'>✅ No duplicate classes found</p>";
    }
    
    // Check for potential naming conflicts
    echo "<h3>Existing Classes in 2024/2025:</h3>";
    $currentClasses = $db->fetchAll("
        SELECT id, nama_kelas, tingkat, kurikulum, wali_kelas, is_active
        FROM kelas 
        WHERE tahun_pelajaran = '2024/2025'
        ORDER BY tingkat, nama_kelas
    ");
    
    if (!empty($currentClasses)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Kurikulum</th><th>Wali Kelas</th><th>Active</th>";
        echo "</tr>";
        
        foreach ($currentClasses as $class) {
            $activeIcon = $class['is_active'] ? '✅' : '❌';
            echo "<tr>";
            echo "<td>{$class['id']}</td>";
            echo "<td>{$class['nama_kelas']}</td>";
            echo "<td>{$class['tingkat']}</td>";
            echo "<td>{$class['kurikulum']}</td>";
            echo "<td>{$class['wali_kelas']}</td>";
            echo "<td>{$activeIcon}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No classes found for 2024/2025</p>";
    }
    
    echo "<h2>🧪 Test Insert with Unique Name</h2>";
    
    // Generate a unique test name
    $testName = 'TEST-UNIQUE-' . date('YmdHis') . '-' . rand(1000, 9999);
    
    $testData = [
        'nama_kelas' => $testName,
        'tingkat' => 'X',
        'kurikulum' => 'Kurikulum K13',
        'tahun_pelajaran' => '2024/2025',
        'wali_kelas' => 'Test Teacher Unique',
        'kapasitas' => 30,
        'created_by' => 1
    ];
    
    echo "<h3>Test Data:</h3>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    $sql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $params = [
        $testData['nama_kelas'],
        $testData['tingkat'],
        $testData['kurikulum'],
        $testData['tahun_pelajaran'],
        $testData['wali_kelas'],
        $testData['kapasitas'],
        $testData['created_by']
    ];
    
    try {
        $db->query($sql, $params);
        $insertId = $db->lastInsertId();
        
        echo "<p style='color: green;'>✅ Test insert successful! Insert ID: {$insertId}</p>";
        
        // Verify the inserted data
        $insertedData = $db->fetch("SELECT * FROM kelas WHERE id = ?", [$insertId]);
        echo "<h3>Inserted Data Verification:</h3>";
        echo "<pre>" . print_r($insertedData, true) . "</pre>";
        
        // Clean up test data
        $db->query("DELETE FROM kelas WHERE id = ?", [$insertId]);
        echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Test insert failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
        
        // Check if it's a duplicate entry error
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "<p style='color: orange;'>⚠️ This appears to be a duplicate entry constraint violation</p>";
        }
    }
    
    echo "<h2>🔧 Test with Form Data</h2>";
    
    // Test with the exact data from the form
    $formTestData = [
        'nama_kelas' => 'KPP A',
        'tingkat' => 'KPP',
        'kurikulum' => 'Kurikulum Seminari',
        'tahun_pelajaran' => '2024/2025',
        'wali_kelas' => 'Maria Agustina Reforma Putri, S.Pd.',
        'kapasitas' => 10,
        'created_by' => 1
    ];
    
    echo "<h3>Form Test Data:</h3>";
    echo "<pre>" . print_r($formTestData, true) . "</pre>";
    
    // Check if this exact combination already exists
    $existingCheck = $db->fetch("
        SELECT id, nama_kelas, tingkat, tahun_pelajaran 
        FROM kelas 
        WHERE nama_kelas = ? AND tingkat = ? AND tahun_pelajaran = ?
    ", [
        $formTestData['nama_kelas'],
        $formTestData['tingkat'],
        $formTestData['tahun_pelajaran']
    ]);
    
    if ($existingCheck) {
        echo "<p style='color: red;'>❌ Class with this name already exists!</p>";
        echo "<p><strong>Existing Class ID:</strong> {$existingCheck['id']}</p>";
        echo "<p><strong>This is likely the cause of the form failure.</strong></p>";
        
        echo "<h3>Suggested Solutions:</h3>";
        echo "<ol>";
        echo "<li>Use a different class name (e.g., 'KPP B', 'KPP-1', etc.)</li>";
        echo "<li>Check if the existing class should be updated instead</li>";
        echo "<li>Delete the existing class if it's a duplicate</li>";
        echo "</ol>";
        
    } else {
        echo "<p style='color: green;'>✅ No existing class with this name found</p>";
        
        // Try to insert the form data
        try {
            $formParams = [
                $formTestData['nama_kelas'],
                $formTestData['tingkat'],
                $formTestData['kurikulum'],
                $formTestData['tahun_pelajaran'],
                $formTestData['wali_kelas'],
                $formTestData['kapasitas'],
                $formTestData['created_by']
            ];
            
            $db->query($sql, $formParams);
            $formInsertId = $db->lastInsertId();
            
            echo "<p style='color: green;'>✅ Form data insert successful! Insert ID: {$formInsertId}</p>";
            
            // Clean up
            $db->query("DELETE FROM kelas WHERE id = ?", [$formInsertId]);
            echo "<p style='color: blue;'>🧹 Form test data cleaned up</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Form data insert failed: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🔍 Constraint Analysis</h2>";
    
    // Check for foreign key constraints
    $constraints = $db->fetchAll("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'kelas' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    if (!empty($constraints)) {
        echo "<h3>Foreign Key Constraints:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Constraint</th><th>Column</th><th>References Table</th><th>References Column</th>";
        echo "</tr>";
        
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
            echo "<td>{$constraint['COLUMN_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: green;'>✅ No foreign key constraints found</p>";
    }
    
    echo "<h2>✅ Summary</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔍 Analysis Results:</h4>";
    echo "<ul>";
    echo "<li>✅ Table structure verified</li>";
    echo "<li>✅ Constraints checked</li>";
    echo "<li>✅ Duplicate detection performed</li>";
    echo "<li>✅ Insert functionality tested</li>";
    echo "</ul>";
    
    if ($existingCheck) {
        echo "<p style='color: red;'><strong>Issue Found:</strong> Class name 'KPP A' already exists for 2024/2025</p>";
        echo "<p><strong>Solution:</strong> Use a different class name or update the existing class</p>";
    } else {
        echo "<p style='color: green;'><strong>No issues found:</strong> The form should work correctly</p>";
    }
    echo "</div>";
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔗 Try Form Again</a></p>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔗 View Class List</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Analysis Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
