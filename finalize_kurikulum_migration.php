<?php
/**
 * Finalize Kurikulum Migration
 * Script ini akan menyelesaikan migrasi dengan menghapus kolom jurusan lama
 */

require_once __DIR__ . '/app/models/Database.php';

echo "<h1>🏁 Finalize Kurikulum Migration</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Pre-Finalization Check</h2>";
    
    // Check if kurikulum column exists and has data
    $kurikulumCheck = $db->fetchAll("SHOW COLUMNS FROM kelas LIKE 'kurikulum'");
    if (empty($kurikulumCheck)) {
        echo "<p style='color: red;'>❌ Kurikulum column not found. Please run migration first.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Kurikulum column exists</p>";
    
    // Check kurikulum data distribution
    $kurikulumData = $db->fetchAll("
        SELECT kurikulum, COUNT(*) as count 
        FROM kelas 
        WHERE kurikulum IS NOT NULL
        GROUP BY kurikulum
        ORDER BY count DESC
    ");
    
    echo "<h3>Current Kurikulum Distribution:</h3>";
    $totalWithKurikulum = 0;
    foreach ($kurikulumData as $data) {
        echo "<p>📊 {$data['kurikulum']}: {$data['count']} classes</p>";
        $totalWithKurikulum += $data['count'];
    }
    
    // Check if there are classes without kurikulum
    $classesWithoutKurikulum = $db->fetch("
        SELECT COUNT(*) as count 
        FROM kelas 
        WHERE kurikulum IS NULL
    ");
    
    if (($classesWithoutKurikulum['count'] ?? 0) > 0) {
        echo "<p style='color: orange;'>⚠️ {$classesWithoutKurikulum['count']} classes without kurikulum</p>";
        
        // Set default kurikulum for classes without it
        echo "<h3>Setting Default Kurikulum:</h3>";
        $db->query("
            UPDATE kelas 
            SET kurikulum = 'Kurikulum K13' 
            WHERE kurikulum IS NULL
        ");
        echo "<p style='color: green;'>✅ Set default 'Kurikulum K13' for classes without kurikulum</p>";
    } else {
        echo "<p style='color: green;'>✅ All classes have kurikulum assigned</p>";
    }
    
    // Check if jurusan column still exists
    $jurusanCheck = $db->fetchAll("SHOW COLUMNS FROM kelas LIKE 'jurusan'");
    if (empty($jurusanCheck)) {
        echo "<p style='color: blue;'>ℹ️ Jurusan column already removed</p>";
        echo "<h2>✅ Migration Already Finalized</h2>";
        echo "<p>The migration has already been completed. No further action needed.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Jurusan column still exists</p>";
        
        // Show comparison between old and new data
        echo "<h3>Data Comparison (Old vs New):</h3>";
        $comparisonData = $db->fetchAll("
            SELECT id, nama_kelas, tingkat, jurusan, kurikulum, tahun_pelajaran
            FROM kelas 
            ORDER BY tingkat, nama_kelas 
            LIMIT 10
        ");
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Jurusan (Old)</th><th>Kurikulum (New)</th><th>Tahun</th>";
        echo "</tr>";
        
        foreach ($comparisonData as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['nama_kelas']}</td>";
            echo "<td>{$row['tingkat']}</td>";
            echo "<td style='color: gray;'>{$row['jurusan']}</td>";
            echo "<td style='color: green; font-weight: bold;'>{$row['kurikulum']}</td>";
            echo "<td>{$row['tahun_pelajaran']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>🗑️ Removing Old Jurusan Column</h2>";
        
        // Confirm before deletion
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Warning</h4>";
        echo "<p>This action will permanently remove the 'jurusan' column from the database.</p>";
        echo "<p>Make sure you have tested the application thoroughly with the new 'kurikulum' field.</p>";
        echo "</div>";
        
        // Check if we should proceed (in a real scenario, you might want user confirmation)
        $proceed = true; // Set to true for automatic execution
        
        if ($proceed) {
            try {
                // Drop the jurusan column
                $db->query("ALTER TABLE kelas DROP COLUMN jurusan");
                echo "<p style='color: green;'>✅ Successfully removed 'jurusan' column</p>";
                
                // Verify the column is gone
                $verifyDrop = $db->fetchAll("SHOW COLUMNS FROM kelas LIKE 'jurusan'");
                if (empty($verifyDrop)) {
                    echo "<p style='color: green;'>✅ Verified: 'jurusan' column has been removed</p>";
                } else {
                    echo "<p style='color: red;'>❌ Error: 'jurusan' column still exists</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to remove 'jurusan' column: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>📊 Final Database Schema</h2>";
    
    // Show current kelas table structure
    $tableStructure = $db->fetchAll("SHOW COLUMNS FROM kelas");
    
    echo "<h3>Kelas Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    foreach ($tableStructure as $column) {
        $highlight = ($column['Field'] === 'kurikulum') ? 'style="background: #d4edda; font-weight: bold;"' : '';
        echo "<tr {$highlight}>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🧪 Testing Application Integration</h2>";
    
    // Test if the application can read kurikulum data properly
    try {
        $testData = $db->fetchAll("
            SELECT id, nama_kelas, tingkat, kurikulum, tahun_pelajaran
            FROM kelas 
            WHERE kurikulum IS NOT NULL
            LIMIT 5
        ");
        
        echo "<h3>Sample Data Test:</h3>";
        foreach ($testData as $row) {
            echo "<p>🏫 {$row['nama_kelas']} (Tingkat {$row['tingkat']}) - <strong>{$row['kurikulum']}</strong> - {$row['tahun_pelajaran']}</p>";
        }
        
        echo "<p style='color: green;'>✅ Application can read kurikulum data successfully</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error reading kurikulum data: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue;'>🔗 Test Class List (should show Kurikulum column)</a></p>";
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='color: blue;'>🔗 Test Create Class Form (should have Kurikulum dropdown)</a></p>";
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue;'>🔗 Test Student List</a></p>";
    
    echo "<h2>✅ Migration Finalization Complete</h2>";
    
    $finalKurikulumCount = $db->fetch("SELECT COUNT(*) as total FROM kelas WHERE kurikulum IS NOT NULL");
    $totalClasses = $db->fetch("SELECT COUNT(*) as total FROM kelas");
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎉 Migration Successfully Finalized!</h4>";
    echo "<ul>";
    echo "<li><strong>Total Classes:</strong> " . ($totalClasses['total'] ?? 0) . "</li>";
    echo "<li><strong>Classes with Kurikulum:</strong> " . ($finalKurikulumCount['total'] ?? 0) . "</li>";
    echo "<li><strong>Old 'jurusan' column:</strong> ❌ Removed</li>";
    echo "<li><strong>New 'kurikulum' column:</strong> ✅ Active</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📚 Available Kurikulum Options:</h4>";
    echo "<ul>";
    echo "<li>🎓 <strong>Kurikulum Seminari</strong> - Untuk program seminari khusus</li>";
    echo "<li>📚 <strong>Kurikulum K13</strong> - Kurikulum 2013 standar nasional</li>";
    echo "<li>🚀 <strong>Kurikulum Merdeka</strong> - Kurikulum Merdeka Belajar terbaru</li>";
    echo "<li>🧠 <strong>Kurikulum Deep Learning</strong> - Program pembelajaran mendalam dan teknologi</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎯 What's Changed:</h4>";
    echo "<ul>";
    echo "<li>✅ Field 'jurusan' → 'kurikulum' in database</li>";
    echo "<li>✅ Dropdown options updated in forms</li>";
    echo "<li>✅ Display labels updated in views</li>";
    echo "<li>✅ Controller logic updated</li>";
    echo "<li>✅ Model methods updated</li>";
    echo "<li>✅ All data migrated successfully</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Finalization Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
