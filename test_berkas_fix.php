<?php
/**
 * Test Berkas Fix - PHP Notice Error
 */

require_once 'app/models/Database.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/User.php';
require_once 'app/models/Siswa.php';
require_once 'app/models/Berkas.php';

// Initialize session manager
$sessionManager = new SimpleSessionManager();
$userModel = new User();
$siswaModel = new Siswa();
$berkasModel = new Berkas();

echo "<h2>🔧 Test Berkas Fix - PHP Notice Error</h2>";

// Login sebagai admin
$result = $userModel->authenticate('admin', 'admin123');

if ($result['success']) {
    $sessionManager->loginUser($result['user']);
    echo "<p style='color: green;'>✅ Login berhasil sebagai: " . htmlspecialchars($result['user']['nama_lengkap']) . "</p>";
    
    // Get sample siswa
    $siswaList = $siswaModel->getAll();
    if (!empty($siswaList)) {
        $siswa = $siswaList[0];
        $siswaId = $siswa['id_siswa'] ?? $siswa['id'];
        
        echo "<p>📚 Testing dengan siswa: <strong>" . htmlspecialchars($siswa['nama_lengkap']) . "</strong> (ID: $siswaId)</p>";
        
        // Test 1: Check berkas data structure
        echo "<h3>Test 1: Berkas Data Structure</h3>";
        $berkas = $berkasModel->getBySiswaId($siswaId);
        
        if (!empty($berkas)) {
            $sampleBerkas = $berkas[0];
            echo "<p>✅ Sample berkas data structure:</p>";
            echo "<pre>";
            foreach ($sampleBerkas as $key => $value) {
                echo "<strong>$key:</strong> " . (is_string($value) ? htmlspecialchars($value) : var_export($value, true)) . "\n";
            }
            echo "</pre>";
            
            // Test field access
            echo "<h4>Field Access Test:</h4>";
            $fields = ['nama_file', 'nama_berkas', 'nama_file_asli', 'nama_file_sistem', 'file_path'];
            foreach ($fields as $field) {
                if (isset($sampleBerkas[$field])) {
                    echo "<p style='color: green;'>✅ <code>$field</code>: " . htmlspecialchars($sampleBerkas[$field]) . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ <code>$field</code>: NOT FOUND</p>";
                }
            }
            
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada berkas untuk testing. Mari buat sample data.</p>";
            
            // Create sample berkas data
            $sampleData = [
                'siswa_id' => $siswaId,
                'jenis_berkas' => 'kartu_keluarga',
                'nama_berkas' => 'Kartu Keluarga Test',
                'nama_file_asli' => 'kartu_keluarga_test.pdf',
                'nama_file_sistem' => 'Kartu_Keluarga_Siswa_' . $siswaId . '_' . date('YmdHis') . '.pdf',
                'ukuran_file' => 1024000,
                'mime_type' => 'application/pdf',
                'file_path' => 'uploads/berkas/identitas/test_file.pdf',
                'file_hash' => hash('sha256', 'test_content'),
                'keterangan' => 'Test berkas untuk debugging',
                'uploaded_by' => 1
            ];
            
            try {
                $berkasId = $berkasModel->create($sampleData);
                echo "<p style='color: green;'>✅ Sample berkas created with ID: $berkasId</p>";
                
                // Re-fetch berkas
                $berkas = $berkasModel->getBySiswaId($siswaId);
                if (!empty($berkas)) {
                    $sampleBerkas = $berkas[0];
                    echo "<p>✅ Sample berkas data structure:</p>";
                    echo "<pre>";
                    foreach ($sampleBerkas as $key => $value) {
                        echo "<strong>$key:</strong> " . (is_string($value) ? htmlspecialchars($value) : var_export($value, true)) . "\n";
                    }
                    echo "</pre>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error creating sample berkas: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        // Test 2: Simulate view rendering
        echo "<h3>Test 2: Simulate View Rendering</h3>";
        if (!empty($berkas)) {
            foreach (array_slice($berkas, 0, 2) as $file) {
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
                echo "<h5>File: " . htmlspecialchars($file['jenis_berkas']) . "</h5>";
                
                // Test the problematic code
                echo "<p><strong>Testing field access:</strong></p>";
                
                // Original problematic code (commented)
                // $extension = strtolower(pathinfo($file['nama_file'], PATHINFO_EXTENSION));
                
                // Fixed code
                $fileName = $file['nama_file_asli'] ?? $file['nama_berkas'] ?? '';
                $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                
                echo "<p>✅ File name: " . htmlspecialchars($fileName) . "</p>";
                echo "<p>✅ Extension: " . htmlspecialchars($extension) . "</p>";
                
                // Test icon logic
                $iconClass = 'bi-file-earmark';
                $iconColor = 'text-secondary';
                
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                    $iconClass = 'bi-file-earmark-image';
                    $iconColor = 'text-success';
                } elseif ($extension === 'pdf') {
                    $iconClass = 'bi-file-earmark-pdf';
                    $iconColor = 'text-danger';
                } elseif (in_array($extension, ['doc', 'docx'])) {
                    $iconClass = 'bi-file-earmark-word';
                    $iconColor = 'text-primary';
                }
                
                echo "<p>✅ Icon: <i class='$iconClass $iconColor'></i> $iconClass $iconColor</p>";
                
                // Test file path
                $filePath = $file['file_path'] ?? '';
                echo "<p>✅ File path: " . htmlspecialchars($filePath) . "</p>";
                
                // Test download URL
                $downloadUrl = "/siswa-app/public/berkas/download/" . $file['id'];
                echo "<p>✅ Download URL: <a href='$downloadUrl'>$downloadUrl</a></p>";
                
                echo "</div>";
            }
        }
        
        // Test 3: Routes
        echo "<h3>Test 3: Routes Test</h3>";
        $routes = [
            '/siswa-app/public/berkas/download/1' => 'Download berkas',
            '/siswa-app/public/berkas/delete/1' => 'Delete berkas',
            '/siswa-app/public/upload/berkas/' . $siswaId => 'Upload berkas'
        ];
        
        foreach ($routes as $route => $description) {
            echo "<p>✅ <strong>$description:</strong> <code>$route</code></p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Tidak ada data siswa untuk testing</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Login gagal: " . htmlspecialchars($result['message']) . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Summary Perbaikan</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724; margin-top: 0;'>✅ PHP Notice Error Berhasil Diperbaiki!</h4>";

echo "<p><strong>Masalah:</strong></p>";
echo "<ul>";
echo "<li>❌ <code>Notice: Undefined index: nama_file</code> pada line 797</li>";
echo "<li>❌ <code>Notice: Undefined index: nama_file</code> pada line 818</li>";
echo "<li>❌ Link download menggunakan field yang salah</li>";
echo "</ul>";

echo "<p><strong>Perbaikan:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Field Access:</strong> Gunakan <code>nama_file_asli</code> dengan fallback ke <code>nama_berkas</code></li>";
echo "<li>✅ <strong>File Path:</strong> Gunakan <code>file_path</code> untuk link view</li>";
echo "<li>✅ <strong>Download:</strong> Tambah route <code>/berkas/download/{id}</code></li>";
echo "<li>✅ <strong>Delete:</strong> Tambah route <code>/berkas/delete/{id}</code></li>";
echo "<li>✅ <strong>Keterangan:</strong> Tampilkan keterangan berkas jika ada</li>";
echo "</ul>";

echo "<p><strong>Struktur Database Berkas:</strong></p>";
echo "<ul>";
echo "<li><code>nama_berkas</code> - Nama berkas untuk display</li>";
echo "<li><code>nama_file_asli</code> - Nama file asli yang diupload</li>";
echo "<li><code>nama_file_sistem</code> - Nama file yang disimpan di sistem</li>";
echo "<li><code>file_path</code> - Path lengkap file untuk akses</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='public/siswa/detail/$siswaId' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Detail Siswa</a> ";
echo "<a href='public/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Dashboard</a>";
echo "</p>";
?>
