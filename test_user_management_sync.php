<?php
/**
 * Script Test untuk Verifikasi Sinkronisasi Manajemen User
 * 
 * Test apakah manajemen user sudah sinkron dengan implementasi role spesifik
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/User.php';

echo "<h1>🔍 Test Sinkronisasi Manajemen User</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    $userModel = new User();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Test yang Akan <PERSON></h3>";
    echo "<ul>";
    echo "<li>Verifikasi method getRoleOptions() sudah update</li>";
    echo "<li>Test method getUserStats() dengan role spesifik</li>";
    echo "<li>Verifikasi statistik dashboard akurat</li>";
    echo "<li>Test form create user dengan role baru</li>";
    echo "<li>Verifikasi tampilan role di daftar user</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 1: Verifikasi getRoleOptions()
    echo "<h2>📋 Test 1: Method getRoleOptions()</h2>";
    
    $roleOptions = $userModel->getRoleOptions();
    
    echo "<p><strong>Total role options:</strong> " . count($roleOptions) . "</p>";
    
    // Check for specific wali kelas roles
    $waliKelasRoles = array_filter($roleOptions, function($key) {
        return strpos($key, 'wali_kelas_') === 0;
    }, ARRAY_FILTER_USE_KEY);
    
    echo "<p><strong>Role wali kelas spesifik:</strong> " . count($waliKelasRoles) . "</p>";
    
    if (count($waliKelasRoles) > 0) {
        echo "<p style='color: green;'>✅ Method getRoleOptions() sudah diupdate dengan role spesifik</p>";
        
        echo "<h4>Role Wali Kelas yang Tersedia:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Role Key</th><th>Display Name</th><th>Kelas</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasRoles as $key => $display) {
            $className = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $key));
            echo "<tr>";
            echo "<td><code>{$key}</code></td>";
            echo "<td>{$display}</td>";
            echo "<td><strong>{$className}</strong></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Method getRoleOptions() belum diupdate</p>";
    }
    
    // Check for old wali_kelas role
    if (isset($roleOptions['wali_kelas'])) {
        echo "<p style='color: orange;'>⚠️ Role 'wali_kelas' lama masih ada - sebaiknya dihapus</p>";
    } else {
        echo "<p style='color: green;'>✅ Role 'wali_kelas' lama sudah dihapus</p>";
    }
    
    // Test 2: Verifikasi getUserStats()
    echo "<h2>📊 Test 2: Method getUserStats()</h2>";
    
    $stats = $userModel->getUserStats();
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Statistik</th><th>Nilai</th><th>Status</th></tr>";
    echo "</thead><tbody>";
    
    $statsToCheck = [
        'total_users' => 'Total User',
        'active_users' => 'User Aktif',
        'admin_count' => 'Admin',
        'pamong_count' => 'Pamong',
        'wali_kelas_count' => 'Wali Kelas',
        'staff_count' => 'Staff'
    ];
    
    foreach ($statsToCheck as $key => $label) {
        $value = $stats[$key] ?? 0;
        $status = $value > 0 ? '✅' : '⚠️';
        
        echo "<tr>";
        echo "<td>{$label}</td>";
        echo "<td><strong>{$value}</strong></td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Verify wali_kelas_count includes specific roles
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role LIKE 'wali_kelas%'");
    $actualWaliKelasCount = $stmt->fetchColumn();
    
    if ($stats['wali_kelas_count'] == $actualWaliKelasCount) {
        echo "<p style='color: green;'>✅ Statistik wali kelas sudah akurat (termasuk role spesifik)</p>";
    } else {
        echo "<p style='color: red;'>❌ Statistik wali kelas tidak akurat. Expected: {$actualWaliKelasCount}, Got: {$stats['wali_kelas_count']}</p>";
    }
    
    // Test 3: Verifikasi data user dengan role spesifik
    echo "<h2>👥 Test 3: Data User dengan Role Spesifik</h2>";
    
    $stmt = $pdo->query("
        SELECT role, COUNT(*) as count, GROUP_CONCAT(username) as usernames
        FROM users 
        WHERE role LIKE 'wali_kelas_%' 
        GROUP BY role 
        ORDER BY role
    ");
    $specificRoleUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($specificRoleUsers)) {
        echo "<p style='color: green;'>✅ Ditemukan " . count($specificRoleUsers) . " role wali kelas spesifik di database</p>";
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Role</th><th>Display Name</th><th>Jumlah User</th><th>Username</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($specificRoleUsers as $roleData) {
            $role = $roleData['role'];
            $displayName = $userModel->getRoleDisplayName($role);
            $count = $roleData['count'];
            $usernames = $roleData['usernames'];
            
            echo "<tr>";
            echo "<td><code>{$role}</code></td>";
            echo "<td>{$displayName}</td>";
            echo "<td>{$count}</td>";
            echo "<td>{$usernames}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Tidak ada user dengan role wali kelas spesifik</p>";
        echo "<p>Silakan jalankan script implement_role_specific_wali_kelas.php terlebih dahulu.</p>";
    }
    
    // Test 4: Test getRoleDisplayName()
    echo "<h2>🏷️ Test 4: Method getRoleDisplayName()</h2>";
    
    $testRoles = ['wali_kelas_x_1', 'wali_kelas_kpp_a', 'admin', 'pamong_mp'];
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Role</th><th>Display Name</th><th>Status</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($testRoles as $role) {
        $displayName = $userModel->getRoleDisplayName($role);
        $status = $displayName !== $role ? '✅ OK' : '❌ Not Found';
        
        echo "<tr>";
        echo "<td><code>{$role}</code></td>";
        echo "<td>{$displayName}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Test 5: Simulasi form create user
    echo "<h2>📝 Test 5: Simulasi Form Create User</h2>";
    
    echo "<h4>Role Options untuk Dropdown:</h4>";
    echo "<select class='form-select' style='width: 100%; padding: 8px; margin: 10px 0;'>";
    echo "<option value=''>Pilih Role</option>";
    
    foreach ($roleOptions as $key => $display) {
        $selected = strpos($key, 'wali_kelas_') === 0 ? 'style="background: #fff3cd;"' : '';
        echo "<option value='{$key}' {$selected}>{$display}</option>";
    }
    echo "</select>";
    
    echo "<p><strong>Total options:</strong> " . count($roleOptions) . "</p>";
    echo "<p><strong>Wali kelas options:</strong> " . count($waliKelasRoles) . "</p>";
    
    // Test 6: Verifikasi konsistensi
    echo "<h2>🔄 Test 6: Verifikasi Konsistensi</h2>";
    
    $issues = [];
    
    // Check if all role options exist in database enum
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
    $roleColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    $enumValues = $roleColumn['Type'];
    
    foreach ($roleOptions as $role => $display) {
        if (strpos($enumValues, "'{$role}'") === false) {
            $issues[] = "Role '{$role}' tidak ada di enum database";
        }
    }
    
    // Check if all database roles have display names
    preg_match_all("/'([^']+)'/", $enumValues, $matches);
    $dbRoles = $matches[1];
    
    foreach ($dbRoles as $dbRole) {
        if (!isset($roleOptions[$dbRole])) {
            $issues[] = "Role '{$dbRole}' dari database tidak ada di getRoleOptions()";
        }
    }
    
    if (empty($issues)) {
        echo "<p style='color: green;'>✅ Semua role konsisten antara database dan model</p>";
    } else {
        echo "<p style='color: red;'>❌ Ditemukan " . count($issues) . " masalah konsistensi:</p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
    }
    
    // Test 7: Ringkasan hasil
    echo "<h2>📋 Test 7: Ringkasan Hasil</h2>";
    
    $totalTests = 6;
    $passedTests = 0;
    
    // Simple scoring
    if (count($waliKelasRoles) > 0) $passedTests++;
    if ($stats['wali_kelas_count'] == $actualWaliKelasCount) $passedTests++;
    if (!empty($specificRoleUsers)) $passedTests++;
    if ($userModel->getRoleDisplayName('wali_kelas_x_1') !== 'wali_kelas_x_1') $passedTests++;
    if (count($roleOptions) > 10) $passedTests++;
    if (empty($issues)) $passedTests++;
    
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Statistik Test:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Test:</strong> {$totalTests}</li>";
    echo "<li><strong>Test Passed:</strong> {$passedTests}</li>";
    echo "<li><strong>Success Rate:</strong> " . round($successRate, 1) . "%</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($successRate >= 80) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 MANAJEMEN USER SUDAH SINKRON!</h3>";
        echo "<p>Manajemen user sudah berhasil disinkronkan dengan implementasi role spesifik wali kelas.</p>";
        echo "<ul>";
        echo "<li>✅ Role options sudah diupdate</li>";
        echo "<li>✅ Statistik sudah akurat</li>";
        echo "<li>✅ Form create/edit user sudah mendukung role baru</li>";
        echo "<li>✅ Display name sudah sesuai</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ MANAJEMEN USER BELUM SEPENUHNYA SINKRON</h3>";
        echo "<p>Masih ada beberapa masalah yang perlu diperbaiki.</p>";
        echo "</div>";
    }
    
    // Rekomendasi
    echo "<h2>💡 Rekomendasi</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Langkah Selanjutnya:</h4>";
    echo "<ol>";
    echo "<li><strong>Test Manual:</strong> Akses halaman manajemen user di admin panel</li>";
    echo "<li><strong>Verifikasi Form:</strong> Coba buat user baru dengan role wali kelas spesifik</li>";
    echo "<li><strong>Cek Statistik:</strong> Pastikan dashboard menampilkan angka yang benar</li>";
    echo "<li><strong>Test Edit User:</strong> Coba edit user existing ke role baru</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔗 Link untuk Test Manual:</h4>";
    echo "<ul>";
    echo "<li><a href='/siswa-app/public/admin/users' target='_blank'>Manajemen User</a></li>";
    echo "<li><a href='/siswa-app/public/admin/users/create' target='_blank'>Tambah User Baru</a></li>";
    echo "<li><a href='/siswa-app/public/dashboard' target='_blank'>Dashboard Admin</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
