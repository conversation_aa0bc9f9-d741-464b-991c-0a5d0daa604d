<?php
/**
 * Test Berkas Upload Redirect
 * Test apakah redirect dari /berkas/upload/ ke /upload/berkas/ berfungsi
 */

// Start session dan set user untuk testing
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'admin';

echo "<h2>🔄 Test Berkas Upload Redirect</h2>";

// Test 1: Simulate request ke /berkas/upload/57
echo "<h3>Test 1: Redirect /berkas/upload/57</h3>";

// Simulate the routing logic
$uri = '/berkas/upload/57';
$uri = explode('/', trim($uri, '/'));

echo "<p><strong>Original URI:</strong> /berkas/upload/57</p>";
echo "<p><strong>Parsed URI:</strong> " . print_r($uri, true) . "</p>";

if ($uri[0] === 'berkas' && $uri[1] === 'upload' && isset($uri[2])) {
    $redirectUrl = '/siswa-app/public/upload/berkas/' . $uri[2];
    echo "<p style='color: green;'>✅ Redirect akan dilakukan ke: <code>$redirectUrl</code></p>";
} else {
    echo "<p style='color: red;'>❌ Redirect tidak akan terjadi</p>";
}

// Test 2: Test URL yang benar
echo "<h3>Test 2: URL yang benar /upload/berkas/57</h3>";

$uri2 = '/upload/berkas/57';
$uri2 = explode('/', trim($uri2, '/'));

echo "<p><strong>Correct URI:</strong> /upload/berkas/57</p>";
echo "<p><strong>Parsed URI:</strong> " . print_r($uri2, true) . "</p>";

if ($uri2[0] === 'upload' && $uri2[1] === 'berkas' && isset($uri2[2])) {
    echo "<p style='color: green;'>✅ URL ini akan ditangani oleh UploadController->berkas() dengan ID: " . $uri2[2] . "</p>";
} else {
    echo "<p style='color: red;'>❌ URL ini tidak akan ditangani dengan benar</p>";
}

// Test 3: Test dengan curl
echo "<h3>Test 3: Test dengan curl</h3>";

$testUrls = [
    'http://localhost/siswa-app/public/berkas/upload/57',
    'http://localhost/siswa-app/public/upload/berkas/57'
];

foreach ($testUrls as $url) {
    echo "<p><strong>Testing:</strong> $url</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 302) {
        // Extract Location header
        if (preg_match('/Location: (.+)/', $response, $matches)) {
            $location = trim($matches[1]);
            echo "<p style='color: blue;'>🔄 Redirect (302) ke: <code>$location</code></p>";
        } else {
            echo "<p style='color: blue;'>🔄 Redirect (302) detected</p>";
        }
    } elseif ($httpCode == 200) {
        echo "<p style='color: green;'>✅ OK (200) - Halaman berhasil dimuat</p>";
    } elseif ($httpCode == 404) {
        echo "<p style='color: red;'>❌ Not Found (404)</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ HTTP Code: $httpCode</p>";
    }
}

echo "<hr>";
echo "<p><a href='public/'>Kembali ke Aplikasi</a></p>";
?>
