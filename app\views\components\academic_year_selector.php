<?php
require_once __DIR__ . '/../../helpers/AcademicYear.php';
require_once __DIR__ . '/../../helpers/Security.php';

$selectedYear = AcademicYear::getSelectedAcademicYear();
$currentYear = AcademicYear::getCurrentAcademicYear();
$yearOptions = AcademicYear::generateAcademicYearOptions();
$isCurrentYear = AcademicYear::isCurrentAcademicYear($selectedYear);
?>

<div class="academic-year-selector">
    <div class="dropdown">
        <button class="btn btn-outline-primary dropdown-toggle" type="button" id="academicYearDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-calendar-event"></i>
            <span id="selectedYearText"><?= htmlspecialchars($selectedYear) ?></span>
            <?php if ($isCurrentYear): ?>
                <span class="badge bg-success ms-1">Aktif</span>
            <?php else: ?>
                <span class="badge bg-warning ms-1">Arsip</span>
            <?php endif; ?>
        </button>
        
        <ul class="dropdown-menu" aria-labelledby="academicYearDropdown">
            <li><h6 class="dropdown-header">Pilih Tahun Ajaran</h6></li>
            
            <?php foreach ($yearOptions as $option): ?>
                <li>
                    <a class="dropdown-item academic-year-option <?= $option['selected'] ? 'active' : '' ?>" 
                       href="#" 
                       data-year="<?= htmlspecialchars($option['value']) ?>">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><?= htmlspecialchars($option['text']) ?></span>
                            <div>
                                <?php if ($option['current']): ?>
                                    <span class="badge bg-success">Berjalan</span>
                                <?php endif; ?>
                                <?php if ($option['selected']): ?>
                                    <i class="bi bi-check-circle text-primary"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </li>
            <?php endforeach; ?>
            
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item text-muted" href="#" id="resetToCurrentYear">
                    <i class="bi bi-arrow-clockwise"></i>
                    Reset ke Tahun Berjalan
                </a>
            </li>
        </ul>
    </div>
</div>

<style>
.academic-year-selector .dropdown-toggle {
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    font-weight: 500;
}

.academic-year-selector .dropdown-toggle:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.academic-year-selector .dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.academic-year-selector .dropdown-menu {
    min-width: 280px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.academic-year-selector .dropdown-item {
    padding: 0.5rem 1rem;
}

.academic-year-selector .dropdown-item:hover {
    background-color: #f8f9fa;
}

.academic-year-selector .dropdown-item.active {
    background-color: #007bff;
    color: white;
}

.academic-year-selector .badge {
    font-size: 0.7em;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const academicYearOptions = document.querySelectorAll('.academic-year-option');
    const resetButton = document.getElementById('resetToCurrentYear');
    const selectedYearText = document.getElementById('selectedYearText');
    
    // Handle academic year selection
    academicYearOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            
            const selectedYear = this.dataset.year;
            changeAcademicYear(selectedYear);
        });
    });
    
    // Handle reset to current year
    if (resetButton) {
        resetButton.addEventListener('click', function(e) {
            e.preventDefault();
            resetToCurrentYear();
        });
    }
    
    function changeAcademicYear(academicYear) {
        // Show loading state
        const button = document.getElementById('academicYearDropdown');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Mengubah...';
        button.disabled = true;
        
        // Prepare form data
        const formData = new FormData();
        formData.append('academic_year', academicYear);
        formData.append('csrf_token', '<?= Security::generateCSRFToken() ?>');
        
        fetch('/siswa-app/public/academic-year/change', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update UI
                selectedYearText.textContent = data.academic_year;
                
                // Show success message
                showNotification('success', data.message);
                
                // Reload page to refresh data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('error', data.message || 'Gagal mengubah tahun ajaran');
                
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'Terjadi kesalahan saat mengubah tahun ajaran');
            
            // Restore button
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
    
    function resetToCurrentYear() {
        // Show loading state
        const button = document.getElementById('academicYearDropdown');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Reset...';
        button.disabled = true;
        
        // Prepare form data
        const formData = new FormData();
        formData.append('csrf_token', '<?= Security::generateCSRFToken() ?>');
        
        fetch('/siswa-app/public/academic-year/reset', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update UI
                selectedYearText.textContent = data.academic_year;
                
                // Show success message
                showNotification('success', data.message);
                
                // Reload page to refresh data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('error', data.message || 'Gagal mereset tahun ajaran');
                
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'Terjadi kesalahan saat mereset tahun ajaran');
            
            // Restore button
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
    
    function showNotification(type, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
});
</script>
