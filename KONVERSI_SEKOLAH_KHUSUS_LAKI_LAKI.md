# Konversi Sistem ke Sekolah Khusus Laki-laki

## 📋 Deskripsi
Dokumentasi lengkap untuk mengkonversi sistem manajemen siswa dari sekolah campuran menjadi sekolah khusus laki-laki dengan menghapus semua referensi dan opsi perempuan.

## 🎯 Tujuan Konversi
- Menghapus semua opsi jenis kelamin perempuan dari sistem
- Menyederhanakan interface dengan menghilangkan pilihan jenis kelamin
- Mengoptimalkan sistem untuk sekolah khusus putra
- Memastikan konsistensi data dengan hanya siswa laki-laki

## ⚠️ PERINGATAN PENTING

**BACKUP DATABASE TERLEBIH DAHULU!**
```bash
mysqldump -u root -p siswa_app > backup_before_male_only_conversion.sql
```

## 🔧 Perubahan yang Dilakukan

### 1. Database Schema
**File:** `database/convert_to_male_only_school.sql`
- Mengubah field `jenis_kelamin` dari `ENUM('L', 'P')` menjadi `ENUM('L')`
- Set default value menjadi `'L'`
- Opsi untuk mengkonversi atau menghapus data siswa perempuan yang ada

### 2. Form Create Siswa
**File:** `app/views/siswa/create.php`
- Menghapus dropdown jenis kelamin
- Menambah hidden field dengan value `'L'`
- Menampilkan badge "Laki-laki" dengan keterangan "Sekolah khusus putra"

### 3. Form Edit Siswa
**File:** `app/views/siswa/edit.php`
- Menghapus dropdown jenis kelamin
- Menambah hidden field dengan value `'L'`
- Menampilkan badge "Laki-laki" dengan keterangan "Sekolah khusus putra"

### 4. View Detail Siswa
**File:** `app/views/siswa/detail.php`
- Menghapus logika conditional untuk jenis kelamin
- Selalu menampilkan ikon laki-laki dan text "Laki-laki"
- Menambah keterangan "Sekolah khusus putra"

### 5. View List Siswa
**File:** `app/views/siswa/list.php`
- Menghapus kolom "Jenis Kelamin" dari tabel
- Menyesuaikan lebar kolom lainnya

### 6. View Detail Kelas
**File:** `app/views/kelas/detail.php`
- Menghapus kolom "Jenis Kelamin" dari tabel siswa dalam kelas
- Menghapus logika conditional untuk badge jenis kelamin

### 7. Controller Siswa
**File:** `app/controllers/SiswaController.php`
- Menghapus validasi jenis kelamin
- Set default `jenis_kelamin` menjadi `'L'` untuk create dan update
- Menghapus dependency pada input jenis kelamin dari form

### 8. Sample Data Generator
**File:** `setup_sample_students.php`
- Menghapus nama-nama yang umumnya untuk perempuan
- Mengubah array `$jenisKelamin` menjadi hanya `['L']`
- Menambah nama-nama laki-laki Indonesia

## 🚀 Cara Menjalankan Konversi

### Langkah 1: Backup Database
```bash
mysqldump -u root -p siswa_app > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Langkah 2: Jalankan Script Konversi
```bash
# Via browser
http://localhost/siswa-app/run_male_only_conversion.php

# Atau via command line
php run_male_only_conversion.php
```

### Langkah 3: Verifikasi Perubahan
1. Cek struktur tabel: `DESCRIBE siswa;`
2. Cek data siswa: `SELECT jenis_kelamin, COUNT(*) FROM siswa GROUP BY jenis_kelamin;`
3. Test semua fungsi CRUD siswa

## 📊 Opsi Penanganan Data Siswa Perempuan

### Opsi A: Konversi (Direkomendasikan)
```sql
UPDATE siswa SET jenis_kelamin = 'L' WHERE jenis_kelamin = 'P';
```
- **Keuntungan:** Data siswa tidak hilang
- **Kerugian:** Data jenis kelamin tidak akurat secara historis

### Opsi B: Hapus Data
```sql
DELETE FROM siswa WHERE jenis_kelamin = 'P';
```
- **Keuntungan:** Data konsisten
- **Kerugian:** Data siswa hilang permanen

## 🧪 Testing Checklist

### ✅ Form Create Siswa
- [ ] Tidak ada dropdown jenis kelamin
- [ ] Ada badge "Laki-laki" dengan keterangan
- [ ] Hidden field jenis_kelamin = 'L'
- [ ] Form submit berhasil
- [ ] Data tersimpan dengan jenis_kelamin = 'L'

### ✅ Form Edit Siswa
- [ ] Tidak ada dropdown jenis kelamin
- [ ] Ada badge "Laki-laki" dengan keterangan
- [ ] Hidden field jenis_kelamin = 'L'
- [ ] Form submit berhasil
- [ ] Data terupdate dengan jenis_kelamin = 'L'

### ✅ View Detail Siswa
- [ ] Selalu tampil ikon laki-laki
- [ ] Text "Laki-laki" dengan keterangan "Sekolah khusus putra"
- [ ] Tidak ada conditional logic untuk perempuan

### ✅ View List Siswa
- [ ] Tidak ada kolom "Jenis Kelamin"
- [ ] Tabel tetap rapi tanpa kolom tersebut
- [ ] Semua fungsi lain normal

### ✅ View Detail Kelas
- [ ] Tabel siswa tidak ada kolom "Jenis Kelamin"
- [ ] Daftar siswa tetap tampil normal

### ✅ Database
- [ ] Field jenis_kelamin = ENUM('L')
- [ ] Default value = 'L'
- [ ] Semua siswa memiliki jenis_kelamin = 'L'
- [ ] Tidak ada record dengan jenis_kelamin = 'P'

## 📁 File yang Terlibat

### File Utama yang Dimodifikasi
1. `app/views/siswa/create.php` - Form tambah siswa
2. `app/views/siswa/edit.php` - Form edit siswa
3. `app/views/siswa/detail.php` - Detail siswa
4. `app/views/siswa/list.php` - Daftar siswa
5. `app/views/kelas/detail.php` - Detail kelas
6. `app/controllers/SiswaController.php` - Controller siswa
7. `setup_sample_students.php` - Generator data sample

### File Baru yang Dibuat
1. `database/convert_to_male_only_school.sql` - Script SQL konversi
2. `run_male_only_conversion.php` - Script PHP untuk menjalankan konversi
3. `KONVERSI_SEKOLAH_KHUSUS_LAKI_LAKI.md` - Dokumentasi ini

## 🔄 Rollback (Jika Diperlukan)

Jika perlu mengembalikan ke sistem campuran:

```sql
-- Restore dari backup
mysql -u root -p siswa_app < backup_before_male_only_conversion.sql

-- Atau ubah struktur tabel kembali
ALTER TABLE siswa MODIFY COLUMN jenis_kelamin ENUM('L', 'P') NOT NULL;
```

## 📝 Catatan Tambahan

### Keuntungan Konversi
- Interface lebih sederhana dan fokus
- Tidak ada kebingungan dalam input data
- Konsisten dengan kebijakan sekolah khusus putra
- Mengurangi kemungkinan error input

### Pertimbangan
- Data historis jenis kelamin mungkin tidak akurat jika menggunakan opsi konversi
- Perlu informasi ke pengguna tentang perubahan sistem
- Dokumentasi sistem perlu diupdate

### Maintenance
- Monitor sistem setelah konversi
- Update backup schedule
- Training pengguna jika diperlukan

## 🎉 Hasil Akhir

Setelah konversi berhasil:
- ✅ Sistem hanya mendukung siswa laki-laki
- ✅ Interface lebih sederhana tanpa pilihan jenis kelamin
- ✅ Data konsisten dengan kebijakan sekolah khusus putra
- ✅ Semua fungsi CRUD tetap berfungsi normal
