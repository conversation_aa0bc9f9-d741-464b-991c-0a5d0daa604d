    </main>

    <!-- Footer -->
    <footer class="bg-gradient-dark text-light mt-5">
        <!-- Main Footer Content -->
        <div class="py-5">
            <div class="container">
                <div class="row g-4">
                    <!-- About Section -->
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-section">
                            <h5 class="fw-bold mb-3">
                                <i class="bi bi-mortarboard text-primary me-2"></i>
                                SISWA APP
                            </h5>
                            <p class="text-light-emphasis mb-3">
                                Sistem Informasi Akademik Siswa yang modern, aman, dan mudah digunakan untuk mengelola data siswa, kelas, dan administrasi sekolah.
                            </p>
                            <div class="d-flex gap-2">
                                <span class="badge bg-primary">
                                    <i class="bi bi-shield-check me-1"></i>
                                    Secure
                                </span>
                                <span class="badge bg-success">
                                    <i class="bi bi-speedometer2 me-1"></i>
                                    Fast
                                </span>
                                <span class="badge bg-info">
                                    <i class="bi bi-phone me-1"></i>
                                    Responsive
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-section">
                            <h6 class="fw-bold mb-3">Menu Utama</h6>
                            <ul class="list-unstyled footer-links">
                                <li><a href="/siswa-app/public/dashboard" class="text-light-emphasis text-decoration-none">
                                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                                </a></li>
                                <li><a href="/siswa-app/public/siswa" class="text-light-emphasis text-decoration-none">
                                    <i class="bi bi-people me-2"></i>Data Siswa
                                </a></li>
                                <li><a href="/siswa-app/public/kelas" class="text-light-emphasis text-decoration-none">
                                    <i class="bi bi-building me-2"></i>Data Kelas
                                </a></li>
                                <?php if (Security::hasRole(['admin', 'staff'])): ?>
                                <li><a href="/siswa-app/public/academic-year-management" class="text-light-emphasis text-decoration-none">
                                    <i class="bi bi-calendar-range me-2"></i>Tahun Ajaran
                                </a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-section">
                            <h6 class="fw-bold mb-3">Fitur Unggulan</h6>
                            <ul class="list-unstyled footer-features">
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <span class="text-light-emphasis">Manajemen Data Siswa</span>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <span class="text-light-emphasis">Catatan BK & Konseling</span>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <span class="text-light-emphasis">Upload Berkas Digital</span>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <span class="text-light-emphasis">Multi Tahun Ajaran</span>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <span class="text-light-emphasis">Role-based Access</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- System Info -->
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-section">
                            <h6 class="fw-bold mb-3">Informasi Sistem</h6>
                            <div class="system-info">
                                <div class="info-item mb-2">
                                    <small class="text-light-emphasis d-block">
                                        <i class="bi bi-calendar-event me-2"></i>
                                        Tahun Ajaran Aktif
                                    </small>
                                    <span class="badge bg-primary">
                                        <?= AcademicYear::getCurrentAcademicYear() ?>
                                    </span>
                                </div>
                                <div class="info-item mb-2">
                                    <small class="text-light-emphasis d-block">
                                        <i class="bi bi-person-circle me-2"></i>
                                        Login sebagai
                                    </small>
                                    <span class="text-warning fw-bold">
                                        <?= htmlspecialchars($_SESSION['user']['nama_lengkap'] ?? 'Guest') ?>
                                    </span>
                                </div>
                                <div class="info-item mb-2">
                                    <small class="text-light-emphasis d-block">
                                        <i class="bi bi-shield-check me-2"></i>
                                        Role
                                    </small>
                                    <span class="badge bg-<?= ($_SESSION['user']['role'] ?? '') === 'admin' ? 'danger' : (($_SESSION['user']['role'] ?? '') === 'staff' ? 'warning' : 'info') ?>">
                                        <?= ucfirst($_SESSION['user']['role'] ?? 'Guest') ?>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <small class="text-light-emphasis d-block">
                                        <i class="bi bi-clock me-2"></i>
                                        Last Login
                                    </small>
                                    <small class="text-muted">
                                        <?= date('d/m/Y H:i', strtotime($_SESSION['user']['last_login'] ?? 'now')) ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-top border-secondary">
            <div class="container py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0 text-light-emphasis">
                            <i class="bi bi-c-circle me-1"></i>
                            2025 <strong>SISWA APP</strong>. All rights reserved.
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-flex justify-content-md-end align-items-center gap-3">
                            <small class="text-muted">
                                <i class="bi bi-code-slash me-1"></i>
                                Version 2.0
                            </small>
                            <small class="text-muted">
                                <i class="bi bi-shield-lock me-1"></i>
                                Security Enhanced
                            </small>
                            <div class="vr text-secondary d-none d-md-block"></div>
                            <small class="text-success">
                                <i class="bi bi-wifi me-1"></i>
                                Online
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS -->
    <script src="/assets/js/main.js"></script>

    <!-- Security: Auto logout on idle -->
    <script>
        let idleTimer;
        const idleTime = 30 * 60 * 1000; // 30 minutes

        function resetIdleTimer() {
            clearTimeout(idleTimer);
            idleTimer = setTimeout(() => {
                if (confirm('Sesi Anda akan berakhir karena tidak aktif. Klik OK untuk melanjutkan atau Cancel untuk logout.')) {
                    resetIdleTimer();
                } else {
                    window.location.href = '/logout';
                }
            }, idleTime);
        }

        // Reset timer on user activity
        document.addEventListener('mousemove', resetIdleTimer);
        document.addEventListener('keypress', resetIdleTimer);
        document.addEventListener('click', resetIdleTimer);

        // Start timer
        resetIdleTimer();
    </script>
</body>
</html>