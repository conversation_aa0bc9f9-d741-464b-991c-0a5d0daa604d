<?php
/**
 * Direct Database Update for Berkas
 */

require_once 'app/models/Database.php';

echo "<h2>🔧 Direct Database Update for Berkas</h2>";

try {
    $db = new Database();
    
    // Direct SQL updates
    $updates = [
        "UPDATE berkas SET jenis_berkas = 'rapor_kelas_x' WHERE id = 16",
        "UPDATE berkas SET jenis_berkas = 'rapor_kelas_xi' WHERE id = 17", 
        "UPDATE berkas SET jenis_berkas = 'rapor_kelas_xii' WHERE id = 18"
    ];
    
    foreach ($updates as $sql) {
        $db->query($sql);
        echo "<p>✅ Executed: <code>$sql</code></p>";
    }
    
    echo "<hr>";
    echo "<h3>📊 Verification</h3>";
    
    // Check results
    $results = $db->fetchAll("SELECT id, jenis_berkas, nama_file_sistem FROM berkas WHERE siswa_id = 57 ORDER BY id");
    
    foreach ($results as $berkas) {
        echo "<p>";
        echo "<strong>ID " . $berkas['id'] . ":</strong> ";
        echo "<code>" . ($berkas['jenis_berkas'] ?: 'EMPTY') . "</code> - ";
        echo htmlspecialchars($berkas['nama_file_sistem']);
        echo "</p>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Database Updated Successfully!</h4>";
    echo "<p>Berkas jenis telah diupdate dengan benar.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='test_berkas_grouped.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Grouped Display</a> ";
echo "<a href='public/siswa/detail/57' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👁️ Detail Siswa</a>";
echo "</p>";
?>
