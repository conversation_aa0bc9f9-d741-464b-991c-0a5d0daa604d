<?php
/**
 * Migration Script: <PERSON>rusan to Kurikulum
 * Script ini akan mengubah field 'jurusan' menjadi 'kurikulum' dengan pilihan yang baru
 */

require_once __DIR__ . '/app/models/Database.php';

echo "<h1>🔄 Migration: Jurusan → Kurikulum</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Pre-Migration Analysis</h2>";
    
    // Check current jurusan data
    $currentJurusan = $db->fetchAll("
        SELECT jurusan, COUNT(*) as count 
        FROM kelas 
        WHERE jurusan IS NOT NULL AND jurusan != ''
        GROUP BY jurusan
        ORDER BY count DESC
    ");
    
    echo "<h3>Current Jurusan Distribution:</h3>";
    if (!empty($currentJurusan)) {
        foreach ($currentJurusan as $data) {
            echo "<p>📊 {$data['jurusan']}: {$data['count']} classes</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No jurusan data found</p>";
    }
    
    // Check if kurikulum column already exists
    $columns = $db->fetchAll("SHOW COLUMNS FROM kelas LIKE 'kurikulum'");
    $kurikulumExists = !empty($columns);
    
    if ($kurikulumExists) {
        echo "<p style='color: blue;'>ℹ️ Kurikulum column already exists</p>";
    } else {
        echo "<p style='color: green;'>✅ Ready to add kurikulum column</p>";
    }
    
    echo "<h2>🚀 Starting Migration</h2>";
    
    // Step 1: Create backup table
    echo "<h3>Step 1: Creating Backup</h3>";
    try {
        $db->query("
            CREATE TABLE IF NOT EXISTS backup_kelas_jurusan AS 
            SELECT id, nama_kelas, jurusan, created_at 
            FROM kelas 
            WHERE jurusan IS NOT NULL
        ");
        echo "<p style='color: green;'>✅ Backup table created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Backup might already exist: " . $e->getMessage() . "</p>";
    }
    
    // Step 2: Add kurikulum column if not exists
    if (!$kurikulumExists) {
        echo "<h3>Step 2: Adding Kurikulum Column</h3>";
        try {
            $db->query("
                ALTER TABLE kelas 
                ADD COLUMN kurikulum ENUM(
                    'Kurikulum Seminari',
                    'Kurikulum K13', 
                    'Kurikulum Merdeka',
                    'Kurikulum Deep Learning'
                ) AFTER tingkat
            ");
            echo "<p style='color: green;'>✅ Kurikulum column added successfully</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add kurikulum column: " . $e->getMessage() . "</p>";
            throw $e;
        }
    }
    
    // Step 3: Migrate data from jurusan to kurikulum
    echo "<h3>Step 3: Migrating Data</h3>";
    
    $migrationMapping = [
        'IPA' => 'Kurikulum K13',
        'IPS' => 'Kurikulum K13', 
        'Bahasa' => 'Kurikulum Merdeka',
        'Umum' => 'Kurikulum Seminari',
        '' => 'Kurikulum K13',
        null => 'Kurikulum K13'
    ];
    
    echo "<h4>Migration Mapping:</h4>";
    foreach ($migrationMapping as $old => $new) {
        $oldDisplay = $old ?: '(empty/null)';
        echo "<p>🔄 {$oldDisplay} → {$new}</p>";
    }
    
    try {
        $db->query("
            UPDATE kelas SET kurikulum = 
                CASE 
                    WHEN jurusan = 'IPA' THEN 'Kurikulum K13'
                    WHEN jurusan = 'IPS' THEN 'Kurikulum K13'
                    WHEN jurusan = 'Bahasa' THEN 'Kurikulum Merdeka'
                    WHEN jurusan = 'Umum' THEN 'Kurikulum Seminari'
                    WHEN jurusan IS NULL OR jurusan = '' THEN 'Kurikulum K13'
                    ELSE 'Kurikulum K13'
                END
            WHERE kurikulum IS NULL
        ");
        echo "<p style='color: green;'>✅ Data migration completed</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Failed to migrate data: " . $e->getMessage() . "</p>";
        throw $e;
    }
    
    // Step 4: Add index for performance
    echo "<h3>Step 4: Adding Index</h3>";
    try {
        $db->query("ALTER TABLE kelas ADD INDEX idx_kurikulum (kurikulum)");
        echo "<p style='color: green;'>✅ Index added for kurikulum column</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index might already exist: " . $e->getMessage() . "</p>";
    }
    
    // Step 5: Add column comment
    echo "<h3>Step 5: Adding Documentation</h3>";
    try {
        $db->query("
            ALTER TABLE kelas 
            MODIFY COLUMN kurikulum ENUM(
                'Kurikulum Seminari',
                'Kurikulum K13', 
                'Kurikulum Merdeka',
                'Kurikulum Deep Learning'
            ) COMMENT 'Jenis kurikulum yang digunakan kelas'
        ");
        echo "<p style='color: green;'>✅ Column comment added</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Comment update failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>📊 Post-Migration Analysis</h2>";
    
    // Check migration results
    $kurikulumDistribution = $db->fetchAll("
        SELECT kurikulum, COUNT(*) as count 
        FROM kelas 
        WHERE kurikulum IS NOT NULL
        GROUP BY kurikulum
        ORDER BY count DESC
    ");
    
    echo "<h3>New Kurikulum Distribution:</h3>";
    foreach ($kurikulumDistribution as $data) {
        echo "<p>📊 {$data['kurikulum']}: {$data['count']} classes</p>";
    }
    
    // Show sample migrated data
    $sampleData = $db->fetchAll("
        SELECT id, nama_kelas, tingkat, kurikulum, tahun_pelajaran, jurusan as old_jurusan
        FROM kelas 
        ORDER BY tingkat, nama_kelas 
        LIMIT 10
    ");
    
    echo "<h3>Sample Migrated Data:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Kurikulum (New)</th><th>Jurusan (Old)</th><th>Tahun</th>";
    echo "</tr>";
    
    foreach ($sampleData as $row) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['nama_kelas']}</td>";
        echo "<td>{$row['tingkat']}</td>";
        echo "<td style='color: green; font-weight: bold;'>{$row['kurikulum']}</td>";
        echo "<td style='color: gray;'>{$row['old_jurusan']}</td>";
        echo "<td>{$row['tahun_pelajaran']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>⚠️ Important Notes</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Next Steps Required:</h4>";
    echo "<ol>";
    echo "<li><strong>Update Application Code:</strong> Change references from 'jurusan' to 'kurikulum'</li>";
    echo "<li><strong>Update Forms:</strong> Change dropdown options in class forms</li>";
    echo "<li><strong>Update Views:</strong> Change display labels and values</li>";
    echo "<li><strong>Test Thoroughly:</strong> Ensure all functionality works with new field</li>";
    echo "<li><strong>Remove Old Column:</strong> After testing, remove 'jurusan' column</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue;'>🔗 Test Class Management (will need code updates)</a></p>";
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue;'>🔗 Test Student List</a></p>";
    
    echo "<h2>✅ Migration Summary</h2>";
    
    $totalClasses = $db->fetch("SELECT COUNT(*) as total FROM kelas");
    $classesWithKurikulum = $db->fetch("SELECT COUNT(*) as total FROM kelas WHERE kurikulum IS NOT NULL");
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎉 Migration Completed Successfully!</h4>";
    echo "<ul>";
    echo "<li><strong>Total Classes:</strong> " . ($totalClasses['total'] ?? 0) . "</li>";
    echo "<li><strong>Classes with Kurikulum:</strong> " . ($classesWithKurikulum['total'] ?? 0) . "</li>";
    echo "<li><strong>Migration Status:</strong> ✅ Complete</li>";
    echo "<li><strong>Backup Created:</strong> ✅ backup_kelas_jurusan table</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Available Kurikulum Options:</h4>";
    echo "<ul>";
    echo "<li>🎓 <strong>Kurikulum Seminari</strong> - Untuk program seminari</li>";
    echo "<li>📚 <strong>Kurikulum K13</strong> - Kurikulum 2013</li>";
    echo "<li>🚀 <strong>Kurikulum Merdeka</strong> - Kurikulum Merdeka Belajar</li>";
    echo "<li>🧠 <strong>Kurikulum Deep Learning</strong> - Program pembelajaran mendalam</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Migration Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
