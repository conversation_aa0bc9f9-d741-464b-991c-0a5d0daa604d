<?php
// Halaman manajemen user untuk admin
require_once __DIR__ . '/../../models/User.php';
$userModel = new User();
$stats = $userModel->getUserStats();
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body bg-primary text-white rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-people-fill" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1 fw-bold">Manajemen User</h4>
                                    <p class="mb-0 opacity-75">
                                        <i class="bi bi-shield-check me-1"></i>
                                        Kelola akun pengguna sistem
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="/siswa-app/public/admin/users/create" class="btn btn-light btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>
                                Tambah User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="mb-1"><?= $stats['total_users'] ?></h5>
                    <small class="text-muted">Total User</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="bi bi-person-check" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="mb-1"><?= $stats['active_users'] ?></h5>
                    <small class="text-muted">User Aktif</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="bi bi-shield-fill" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="mb-1"><?= $stats['admin_count'] ?></h5>
                    <small class="text-muted">Admin</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="bi bi-mortarboard" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="mb-1"><?= $stats['pamong_count'] ?></h5>
                    <small class="text-muted">Pamong</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-secondary mb-2">
                        <i class="bi bi-person-workspace" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="mb-1"><?= $stats['wali_kelas_count'] ?></h5>
                    <small class="text-muted">Wali Kelas</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-dark mb-2">
                        <i class="bi bi-person-badge" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="mb-1"><?= $stats['staff_count'] ?></h5>
                    <small class="text-muted">Staff</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-dark">
                        <i class="bi bi-table me-2"></i>
                        Daftar User
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="px-4 py-3">User</th>
                                    <th class="py-3">Role</th>
                                    <th class="py-3">Kelas yang Dikelola</th>
                                    <th class="py-3">Status</th>
                                    <th class="py-3">Last Login</th>
                                    <th class="py-3">Created</th>
                                    <th class="py-3 text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td class="px-4 py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold"><?= htmlspecialchars($user['nama_lengkap']) ?></div>
                                                <small class="text-muted">
                                                    <i class="bi bi-at"></i> <?= htmlspecialchars($user['username']) ?>
                                                    <span class="mx-1">•</span>
                                                    <i class="bi bi-envelope"></i> <?= htmlspecialchars($user['email']) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-3">
                                        <?php
                                        $roleClass = '';
                                        $roleIcon = '';
                                        switch (true) {
                                            case $user['role'] === 'admin':
                                                $roleClass = 'bg-warning text-dark';
                                                $roleIcon = 'bi-shield-fill';
                                                break;
                                            case in_array($user['role'], ['pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu']):
                                                $roleClass = 'bg-info text-white';
                                                $roleIcon = 'bi-mortarboard';
                                                break;
                                            case strpos($user['role'], 'wali_kelas') === 0:
                                                $roleClass = 'bg-success text-white';
                                                $roleIcon = 'bi-person-workspace';
                                                break;
                                            case $user['role'] === 'staff':
                                                $roleClass = 'bg-dark text-white';
                                                $roleIcon = 'bi-person-badge';
                                                break;
                                            default:
                                                $roleClass = 'bg-light text-dark';
                                                $roleIcon = 'bi-person';
                                        }
                                        ?>
                                        <span class="badge <?= $roleClass ?> px-3 py-2">
                                            <i class="<?= $roleIcon ?> me-1"></i>
                                            <?= $userModel->getRoleDisplayName($user['role']) ?>
                                        </span>
                                    </td>
                                    <td class="py-3">
                                        <?php
                                        // Get kelas yang dikelola untuk wali kelas
                                        if (strpos($user['role'], 'wali_kelas') === 0) {
                                            require_once __DIR__ . '/../../config/Database.php';
                                            $db = new Database();
                                            $pdo = $db->getConnection();

                                            $stmt = $pdo->prepare("
                                                SELECT nama_kelas
                                                FROM kelas
                                                WHERE wali_kelas_id = ? AND is_active = 1
                                                ORDER BY nama_kelas
                                            ");
                                            $stmt->execute([$user['id']]);
                                            $kelasYangDikelola = $stmt->fetchAll(PDO::FETCH_COLUMN);

                                            if (!empty($kelasYangDikelola)) {
                                                foreach ($kelasYangDikelola as $kelas) {
                                                    echo "<span class='badge bg-primary me-1 mb-1'>{$kelas}</span>";
                                                }
                                            } else {
                                                echo "<span class='text-muted'><em>Belum ada kelas</em></span>";
                                            }
                                        } else {
                                            echo "<span class='text-muted'>-</span>";
                                        }
                                        ?>
                                    </td>
                                    <td class="py-3">
                                        <?php if ($user['is_active']): ?>
                                            <span class="badge bg-success px-3 py-2">
                                                <i class="bi bi-check-circle me-1"></i>
                                                Aktif
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger px-3 py-2">
                                                <i class="bi bi-x-circle me-1"></i>
                                                Nonaktif
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3">
                                        <?php if ($user['last_login']): ?>
                                            <small class="text-muted">
                                                <i class="bi bi-clock me-1"></i>
                                                <?= date('d/m/Y H:i', strtotime($user['last_login'])) ?>
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted">
                                                <i class="bi bi-dash-circle me-1"></i>
                                                Belum login
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>
                                            <?= date('d/m/Y', strtotime($user['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td class="py-3 text-center">
                                        <div class="btn-group" role="group">
                                            <a href="/siswa-app/public/admin/users/edit/<?= $user['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit User">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-warning" 
                                                    title="Reset Password"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#resetPasswordModal"
                                                    data-user-id="<?= $user['id'] ?>"
                                                    data-user-name="<?= htmlspecialchars($user['nama_lengkap']) ?>">
                                                <i class="bi bi-key"></i>
                                            </button>
                                            
                                            <?php if ($user['username'] !== 'admin'): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Delete User"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteUserModal"
                                                    data-user-id="<?= $user['id'] ?>"
                                                    data-user-name="<?= htmlspecialchars($user['nama_lengkap']) ?>">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    Reset Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="resetPasswordForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <p>Reset password untuk user: <strong id="resetUserName"></strong></p>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Password Baru</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                        <div class="form-text">Minimal 6 karakter</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-key me-1"></i>
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Hapus User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="deleteUserForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <p>Apakah Anda yakin ingin menghapus user: <strong id="deleteUserName"></strong>?</p>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>
                        Hapus User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
}

.table th {
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 2px;
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.card {
    border-radius: 12px;
}

.table-responsive {
    border-radius: 0 0 12px 12px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reset Password Modal
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    resetPasswordModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');
        
        document.getElementById('resetUserName').textContent = userName;
        document.getElementById('resetPasswordForm').action = '/siswa-app/public/admin/users/reset-password/' + userId;
    });
    
    // Delete User Modal
    const deleteUserModal = document.getElementById('deleteUserModal');
    deleteUserModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');
        
        document.getElementById('deleteUserName').textContent = userName;
        document.getElementById('deleteUserForm').action = '/siswa-app/public/admin/users/delete/' + userId;
    });
});
</script>
