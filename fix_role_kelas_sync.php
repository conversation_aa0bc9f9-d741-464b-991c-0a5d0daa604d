<?php
/**
 * Script Perbaikan Sinkronisasi Role dan <PERSON>
 * 
 * Memperbaiki sinkronisasi antara role user dan assignment kelas
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';

echo "<h1>🔧 Perbaikan Sinkronisasi Role dan <PERSON></h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Tujuan Perbaikan</h3>";
    echo "<ul>";
    echo "<li>Sinkronisasi role user dengan kelas yang dikelola</li>";
    echo "<li>Update assignment wali kelas yang belum tepat</li>";
    echo "<li>Pastikan setiap kelas memiliki wali kelas yang sesuai</li>";
    echo "<li>Update kolom wali_kelas (text) agar sesuai dengan user yang assigned</li>";
    echo "</ul>";
    echo "</div>";
    
    // Step 1: Analisis data saat ini
    echo "<h2>📊 Step 1: Analisis Data Saat Ini</h2>";
    
    $stmt = $pdo->query("
        SELECT k.id, k.nama_kelas, k.tingkat, k.wali_kelas, k.wali_kelas_id,
               u.username, u.nama_lengkap, u.role
        FROM kelas k
        LEFT JOIN users u ON k.wali_kelas_id = u.id
        WHERE k.is_active = 1
        ORDER BY k.tingkat, k.nama_kelas
    ");
    $kelasData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Kelas</th><th>Wali Kelas (Text)</th><th>User Assigned</th><th>Role User</th><th>Status</th></tr>";
    echo "</thead><tbody>";
    
    $needsSync = [];
    $needsAssignment = [];
    $needsRoleUpdate = [];
    
    foreach ($kelasData as $kelas) {
        $status = '✅ OK';
        $statusColor = 'green';
        
        if (!$kelas['wali_kelas_id']) {
            $status = '❌ Belum ada wali kelas';
            $statusColor = 'red';
            $needsAssignment[] = $kelas;
        } elseif (!$kelas['username']) {
            $status = '❌ User tidak ditemukan';
            $statusColor = 'red';
            $needsSync[] = $kelas;
        } else {
            // Cek apakah role sesuai dengan kelas
            $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $kelas['nama_kelas']));
            if ($kelas['role'] !== $expectedRole) {
                $status = '⚠️ Role tidak sesuai';
                $statusColor = 'orange';
                $needsRoleUpdate[] = $kelas;
            }
            
            // Cek apakah wali_kelas (text) sesuai dengan nama user
            if ($kelas['wali_kelas'] !== $kelas['nama_lengkap']) {
                $needsSync[] = $kelas;
            }
        }
        
        echo "<tr>";
        echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
        echo "<td>{$kelas['wali_kelas']}</td>";
        echo "<td>{$kelas['nama_lengkap']} ({$kelas['username']})</td>";
        echo "<td><code>{$kelas['role']}</code></td>";
        echo "<td style='color: {$statusColor};'>{$status}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    echo "<p><strong>Ringkasan masalah:</strong></p>";
    echo "<ul>";
    echo "<li>Perlu assignment: " . count($needsAssignment) . " kelas</li>";
    echo "<li>Perlu sinkronisasi: " . count($needsSync) . " kelas</li>";
    echo "<li>Perlu update role: " . count($needsRoleUpdate) . " kelas</li>";
    echo "</ul>";
    
    // Step 2: Update role user agar sesuai dengan kelas
    echo "<h2>🔄 Step 2: Update Role User</h2>";
    
    if (!empty($needsRoleUpdate)) {
        echo "<p>🔄 Mengupdate role user agar sesuai dengan kelas yang dikelola...</p>";
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>User</th><th>Kelas</th><th>Role Lama</th><th>Role Baru</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($needsRoleUpdate as $kelas) {
            $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $kelas['nama_kelas']));
            
            try {
                $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
                $stmt->execute([$expectedRole, $kelas['wali_kelas_id']]);
                
                echo "<tr style='background: #d4edda;'>";
                echo "<td>{$kelas['nama_lengkap']} ({$kelas['username']})</td>";
                echo "<td>{$kelas['nama_kelas']}</td>";
                echo "<td><code>{$kelas['role']}</code></td>";
                echo "<td><code>{$expectedRole}</code></td>";
                echo "<td style='color: green;'>✅ Berhasil</td>";
                echo "</tr>";
            } catch (Exception $e) {
                echo "<tr style='background: #f8d7da;'>";
                echo "<td>{$kelas['nama_lengkap']} ({$kelas['username']})</td>";
                echo "<td>{$kelas['nama_kelas']}</td>";
                echo "<td><code>{$kelas['role']}</code></td>";
                echo "<td><code>{$expectedRole}</code></td>";
                echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                echo "</tr>";
            }
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ Semua role user sudah sesuai</p>";
    }
    
    // Step 3: Update kolom wali_kelas (text) agar sesuai dengan user
    echo "<h2>📝 Step 3: Update Kolom Wali Kelas (Text)</h2>";
    
    if (!empty($needsSync)) {
        echo "<p>🔄 Mengupdate kolom wali_kelas agar sesuai dengan nama user...</p>";
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Kelas</th><th>Wali Kelas Lama</th><th>Wali Kelas Baru</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($needsSync as $kelas) {
            if ($kelas['nama_lengkap']) {
                try {
                    $stmt = $pdo->prepare("UPDATE kelas SET wali_kelas = ? WHERE id = ?");
                    $stmt->execute([$kelas['nama_lengkap'], $kelas['id']]);
                    
                    echo "<tr style='background: #d4edda;'>";
                    echo "<td>{$kelas['nama_kelas']}</td>";
                    echo "<td>{$kelas['wali_kelas']}</td>";
                    echo "<td>{$kelas['nama_lengkap']}</td>";
                    echo "<td style='color: green;'>✅ Berhasil</td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr style='background: #f8d7da;'>";
                    echo "<td>{$kelas['nama_kelas']}</td>";
                    echo "<td>{$kelas['wali_kelas']}</td>";
                    echo "<td>{$kelas['nama_lengkap']}</td>";
                    echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                    echo "</tr>";
                }
            }
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ Semua kolom wali_kelas sudah sesuai</p>";
    }
    
    // Step 4: Assign wali kelas untuk kelas yang belum memiliki
    echo "<h2>👨‍🏫 Step 4: Assignment Wali Kelas</h2>";
    
    if (!empty($needsAssignment)) {
        echo "<p>🔄 Mencari atau membuat wali kelas untuk kelas yang belum memiliki...</p>";
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Kelas</th><th>Action</th><th>User Assigned</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($needsAssignment as $kelas) {
            $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $kelas['nama_kelas']));
            
            // Cek apakah sudah ada user dengan role yang sesuai
            $stmt = $pdo->prepare("SELECT id, username, nama_lengkap FROM users WHERE role = ? AND is_active = 1");
            $stmt->execute([$expectedRole]);
            $existingUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existingUser) {
                // Assign user yang sudah ada
                try {
                    $stmt = $pdo->prepare("UPDATE kelas SET wali_kelas_id = ?, wali_kelas = ? WHERE id = ?");
                    $stmt->execute([$existingUser['id'], $existingUser['nama_lengkap'], $kelas['id']]);
                    
                    echo "<tr style='background: #d4edda;'>";
                    echo "<td>{$kelas['nama_kelas']}</td>";
                    echo "<td>Assign existing user</td>";
                    echo "<td>{$existingUser['nama_lengkap']} ({$existingUser['username']})</td>";
                    echo "<td><code>{$expectedRole}</code></td>";
                    echo "<td style='color: green;'>✅ Berhasil</td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr style='background: #f8d7da;'>";
                    echo "<td>{$kelas['nama_kelas']}</td>";
                    echo "<td>Assign existing user</td>";
                    echo "<td>{$existingUser['nama_lengkap']} ({$existingUser['username']})</td>";
                    echo "<td><code>{$expectedRole}</code></td>";
                    echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                    echo "</tr>";
                }
            } else {
                // Buat user baru
                $username = str_replace('wali_kelas_', 'wali_', $expectedRole);
                $namaLengkap = "Pak " . ucwords(str_replace('_', ' ', str_replace('wali_kelas_', '', $expectedRole)));
                $email = $username . "@sekolah.com";
                $password = password_hash('wali123', PASSWORD_DEFAULT);
                
                try {
                    // Insert user baru
                    $stmt = $pdo->prepare("
                        INSERT INTO users (username, email, password, role, nama_lengkap, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, 1, NOW())
                    ");
                    $stmt->execute([$username, $email, $password, $expectedRole, $namaLengkap]);
                    $newUserId = $pdo->lastInsertId();
                    
                    // Update kelas dengan wali baru
                    $stmt = $pdo->prepare("UPDATE kelas SET wali_kelas_id = ?, wali_kelas = ? WHERE id = ?");
                    $stmt->execute([$newUserId, $namaLengkap, $kelas['id']]);
                    
                    echo "<tr style='background: #d1ecf1;'>";
                    echo "<td>{$kelas['nama_kelas']}</td>";
                    echo "<td>Create new user</td>";
                    echo "<td>{$namaLengkap} ({$username})</td>";
                    echo "<td><code>{$expectedRole}</code></td>";
                    echo "<td style='color: green;'>✅ Berhasil dibuat</td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr style='background: #f8d7da;'>";
                    echo "<td>{$kelas['nama_kelas']}</td>";
                    echo "<td>Create new user</td>";
                    echo "<td>{$namaLengkap} ({$username})</td>";
                    echo "<td><code>{$expectedRole}</code></td>";
                    echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                    echo "</tr>";
                }
            }
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ Semua kelas sudah memiliki wali kelas</p>";
    }
    
    // Step 5: Verifikasi hasil
    echo "<h2>✅ Step 5: Verifikasi Hasil</h2>";
    
    $stmt = $pdo->query("
        SELECT k.id, k.nama_kelas, k.tingkat, k.wali_kelas, k.wali_kelas_id,
               u.username, u.nama_lengkap, u.role
        FROM kelas k
        LEFT JOIN users u ON k.wali_kelas_id = u.id
        WHERE k.is_active = 1
        ORDER BY k.tingkat, k.nama_kelas
    ");
    $kelasDataAfter = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Kelas</th><th>Wali Kelas</th><th>User</th><th>Role</th><th>Status</th></tr>";
    echo "</thead><tbody>";
    
    $allSynced = true;
    
    foreach ($kelasDataAfter as $kelas) {
        $status = '✅ Sinkron';
        $statusColor = 'green';
        
        if (!$kelas['wali_kelas_id'] || !$kelas['username']) {
            $status = '❌ Belum sinkron';
            $statusColor = 'red';
            $allSynced = false;
        } else {
            $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $kelas['nama_kelas']));
            if ($kelas['role'] !== $expectedRole || $kelas['wali_kelas'] !== $kelas['nama_lengkap']) {
                $status = '⚠️ Perlu perbaikan';
                $statusColor = 'orange';
                $allSynced = false;
            }
        }
        
        echo "<tr>";
        echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
        echo "<td>{$kelas['wali_kelas']}</td>";
        echo "<td>{$kelas['nama_lengkap']} ({$kelas['username']})</td>";
        echo "<td><code>{$kelas['role']}</code></td>";
        echo "<td style='color: {$statusColor};'>{$status}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    if ($allSynced) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 SINKRONISASI BERHASIL!</h3>";
        echo "<p>Semua kelas sudah memiliki wali kelas yang sesuai dengan role mereka.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ MASIH ADA MASALAH</h3>";
        echo "<p>Beberapa kelas masih belum sinkron. Silakan jalankan script ini lagi atau perbaiki secara manual.</p>";
        echo "</div>";
    }
    
    // Step 6: Update form kelas
    echo "<h2>📝 Step 6: Informasi untuk Update Form</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 Langkah Selanjutnya:</h4>";
    echo "<ol>";
    echo "<li><strong>Update form create kelas:</strong> Pastikan dropdown wali kelas menampilkan user yang sesuai</li>";
    echo "<li><strong>Update form edit kelas:</strong> Pastikan bisa mengubah assignment wali kelas</li>";
    echo "<li><strong>Validasi data:</strong> Tambah validasi untuk memastikan konsistensi</li>";
    echo "<li><strong>Test manual:</strong> Test create dan edit kelas dengan wali kelas baru</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Perbaikan selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
