<?php
/**
 * Fix Berkas <PERSON> - Specifically for Rapor detection
 */

require_once 'app/models/Database.php';

echo "<h2>🔧 Fix <PERSON>rka<PERSON> - Rapor Detection</h2>";

try {
    $db = new Database();
    
    // Fix specific berkas records
    $fixes = [
        16 => 'rapor_kelas_x',   // Rapor_Kelas_X_Siswa_57_20250603155445_cee95f.pdf
        17 => 'rapor_kelas_xi',  // Rapor_Kelas_XI_Siswa_57_20250603155807_522292.pdf
        18 => 'rapor_kelas_xii'  // Rapor_Kelas_XII_Siswa_57_20250603155826_6975fd.pdf
    ];
    
    foreach ($fixes as $berkasId => $correctJenis) {
        // Get current data
        $current = $db->fetch("SELECT * FROM berkas WHERE id = ?", [$berkasId]);
        
        if ($current) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
            echo "<strong>Berkas ID:</strong> $berkasId<br>";
            echo "<strong>File:</strong> " . htmlspecialchars($current['nama_file_sistem']) . "<br>";
            echo "<strong>Current Jenis:</strong> <code>" . ($current['jenis_berkas'] ?: 'EMPTY') . "</code><br>";
            echo "<strong>Correct Jenis:</strong> <code>$correctJenis</code><br>";
            
            // Update jenis_berkas
            $sql = "UPDATE berkas SET jenis_berkas = ? WHERE id = ?";
            $db->query($sql, [$correctJenis, $berkasId]);
            
            echo "<span style='color: green;'>✅ Updated to $correctJenis</span><br>";
            echo "</div>";
        }
    }
    
    echo "<hr>";
    echo "<h3>📊 Verification</h3>";
    
    // Verify the changes
    $allBerkas = $db->fetchAll("SELECT id, jenis_berkas, nama_file_sistem FROM berkas WHERE siswa_id = 57 ORDER BY id");
    
    foreach ($allBerkas as $berkas) {
        echo "<p>";
        echo "<strong>ID " . $berkas['id'] . ":</strong> ";
        echo "<code>" . ($berkas['jenis_berkas'] ?: 'EMPTY') . "</code> - ";
        echo htmlspecialchars($berkas['nama_file_sistem']);
        echo "</p>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Jenis Berkas Fixed!</h4>";
    echo "<p>Semua berkas rapor sekarang memiliki jenis yang benar:</p>";
    echo "<ul>";
    echo "<li>✅ <strong>rapor_kelas_x</strong> - Rapor Kelas X</li>";
    echo "<li>✅ <strong>rapor_kelas_xi</strong> - Rapor Kelas XI</li>";
    echo "<li>✅ <strong>rapor_kelas_xii</strong> - Rapor Kelas XII</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='test_berkas_grouped.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Grouped Display</a> ";
echo "<a href='public/siswa/detail/57' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👁️ Detail Siswa</a>";
echo "</p>";
?>
