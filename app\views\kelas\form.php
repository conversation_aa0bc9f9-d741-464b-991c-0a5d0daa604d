<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-building"></i>
                        <?= isset($kelas) ? 'Edit Kelas' : 'Tambah Kelas' ?>
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle"></i> <?= htmlspecialchars($success) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?= isset($kelas) ? '/siswa-app/public/kelas/edit/' . $kelas['id_kelas'] : '/siswa-app/public/kelas/create' ?>" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nama_kelas" class="form-label">
                                        <i class="bi bi-tag"></i> Nama Kelas <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="nama_kelas"
                                           name="nama_kelas"
                                           value="<?= htmlspecialchars($kelas['nama_kelas'] ?? '') ?>"
                                           placeholder="Contoh: X-IPA-1"
                                           required>
                                    <div class="invalid-feedback">
                                        Nama kelas wajib diisi.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tingkat" class="form-label">
                                        <i class="bi bi-layers"></i> Tingkat <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="tingkat" name="tingkat" required>
                                        <option value="">Pilih Tingkat</option>
                                        <option value="KPP" <?= (isset($kelas) && $kelas['tingkat'] == 'KPP') ? 'selected' : '' ?>>KPP (Kelas Persiapan Pertama)</option>
                                        <option value="X" <?= (isset($kelas) && $kelas['tingkat'] == 'X') ? 'selected' : '' ?>>X (Kelas 10)</option>
                                        <option value="XI" <?= (isset($kelas) && $kelas['tingkat'] == 'XI') ? 'selected' : '' ?>>XI (Kelas 11)</option>
                                        <option value="XII" <?= (isset($kelas) && $kelas['tingkat'] == 'XII') ? 'selected' : '' ?>>XII (Kelas 12)</option>
                                        <option value="KPA" <?= (isset($kelas) && $kelas['tingkat'] == 'KPA') ? 'selected' : '' ?>>KPA (Kelas Persiapan Atas)</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Tingkat kelas wajib dipilih.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="kurikulum" class="form-label">
                                        <i class="bi bi-book"></i> Kurikulum
                                    </label>
                                    <select class="form-select" id="kurikulum" name="kurikulum">
                                        <option value="">Pilih Kurikulum</option>
                                        <option value="Kurikulum Seminari" <?= (isset($kelas) && $kelas['kurikulum'] == 'Kurikulum Seminari') ? 'selected' : '' ?>>Kurikulum Seminari</option>
                                        <option value="Kurikulum K13" <?= (isset($kelas) && $kelas['kurikulum'] == 'Kurikulum K13') ? 'selected' : '' ?>>Kurikulum K13</option>
                                        <option value="Kurikulum Merdeka" <?= (isset($kelas) && $kelas['kurikulum'] == 'Kurikulum Merdeka') ? 'selected' : '' ?>>Kurikulum Merdeka</option>
                                        <option value="Kurikulum Deep Learning" <?= (isset($kelas) && $kelas['kurikulum'] == 'Kurikulum Deep Learning') ? 'selected' : '' ?>>Kurikulum Deep Learning</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tahun_pelajaran" class="form-label">
                                        <i class="bi bi-calendar"></i> Tahun Pelajaran <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="tahun_pelajaran"
                                           name="tahun_pelajaran"
                                           value="<?= htmlspecialchars($kelas['tahun_pelajaran'] ?? $default_academic_year ?? '2024/2025') ?>"
                                           placeholder="2024/2025"
                                           pattern="[0-9]{4}/[0-9]{4}"
                                           required>
                                    <div class="invalid-feedback">
                                        Tahun pelajaran wajib diisi dengan format YYYY/YYYY.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="wali_kelas_id" class="form-label">
                                        <i class="bi bi-person"></i> Wali Kelas <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="wali_kelas_id" name="wali_kelas_id" required>
                                        <option value="">Pilih Wali Kelas</option>
                                        <?php if (isset($wali_kelas_list) && is_array($wali_kelas_list)): ?>
                                            <?php foreach ($wali_kelas_list as $wali): ?>
                                                <option value="<?= $wali['id'] ?>"
                                                        <?= (isset($kelas['wali_kelas_id']) && $kelas['wali_kelas_id'] == $wali['id']) ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($wali['nama_lengkap']) ?> (<?= htmlspecialchars($wali['username']) ?>)
                                                    <?php if (strpos($wali['role'], 'wali_kelas_') === 0): ?>
                                                        - <?= strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $wali['role'])) ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">Wali kelas wajib dipilih.</div>
                                    <!-- Hidden field untuk backward compatibility -->
                                    <input type="hidden" id="wali_kelas" name="wali_kelas" value="<?= htmlspecialchars($kelas['wali_kelas'] ?? '') ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="kapasitas" class="form-label">
                                        <i class="bi bi-people"></i> Kapasitas
                                    </label>
                                    <input type="number"
                                           class="form-control"
                                           id="kapasitas"
                                           name="kapasitas"
                                           value="<?= htmlspecialchars($kelas['kapasitas'] ?? '30') ?>"
                                           min="1"
                                           max="50"
                                           placeholder="30">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/siswa-app/public/kelas" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Kembali
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i>
                                <?= isset($kelas) ? 'Update Kelas' : 'Simpan Kelas' ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update hidden wali_kelas field when dropdown changes
document.getElementById('wali_kelas_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const waliKelasName = selectedOption.text.split(' (')[0]; // Get name without username
    document.getElementById('wali_kelas').value = waliKelasName;
});

// Set initial value if editing
document.addEventListener('DOMContentLoaded', function() {
    const waliKelasSelect = document.getElementById('wali_kelas_id');
    if (waliKelasSelect.value) {
        const selectedOption = waliKelasSelect.options[waliKelasSelect.selectedIndex];
        const waliKelasName = selectedOption.text.split(' (')[0];
        document.getElementById('wali_kelas').value = waliKelasName;
    }
});
</script>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-format tahun pelajaran
document.getElementById('tahun_pelajaran').addEventListener('input', function(e) {
    let value = e.target.value.replace(/[^0-9]/g, '');
    if (value.length >= 4) {
        let year1 = value.substring(0, 4);
        let year2 = value.substring(4, 8);
        if (year2.length > 0) {
            e.target.value = year1 + '/' + year2;
        } else {
            e.target.value = year1;
        }
    }
});
</script>