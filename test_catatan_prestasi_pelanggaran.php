<?php
/**
 * Script untuk menambahkan data catatan prestasi dan pelanggaran contoh
 * untuk testing fitur statistik catatan
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/CatatanSiswa.php';

try {
    echo "<h2>Testing Catatan Prestasi dan Pelanggaran</h2>";
    
    $db = new Database();
    $catatanModel = new CatatanSiswa();
    
    // Cek apakah tabel catatan_siswa dan kategori_catatan sudah ada
    echo "<p>1. Memeriksa tabel catatan...</p>";
    
    $tables = $db->fetchAll("SHOW TABLES LIKE 'catatan_siswa'");
    if (empty($tables)) {
        echo "<p style='color: red;'>❌ Tabel catatan_siswa belum ada. Jalankan script create_catatan_tables.sql terlebih dahulu.</p>";
        exit;
    }
    
    $tables = $db->fetchAll("SHOW TABLES LIKE 'kategori_catatan'");
    if (empty($tables)) {
        echo "<p style='color: red;'>❌ Tabel kategori_catatan belum ada. Jalankan script create_catatan_tables.sql terlebih dahulu.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Tabel catatan sudah ada</p>";
    
    // Cek apakah ada siswa untuk testing
    echo "<p>2. Mencari siswa untuk testing...</p>";
    $siswa = $db->fetchAll("SELECT id_siswa, nama_lengkap FROM siswa LIMIT 3");
    
    if (empty($siswa)) {
        echo "<p style='color: red;'>❌ Tidak ada data siswa. Tambahkan siswa terlebih dahulu.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Ditemukan " . count($siswa) . " siswa untuk testing</p>";
    
    // Cek apakah kategori catatan sudah ada
    echo "<p>3. Memeriksa kategori catatan...</p>";
    $categories = $db->fetchAll("SELECT * FROM kategori_catatan WHERE kode_kategori IN ('bk_prestasi', 'bk_pelanggaran', 'bk_konseling')");
    
    if (count($categories) < 3) {
        echo "<p style='color: orange;'>⚠️ Kategori BK belum lengkap. Menambahkan kategori...</p>";
        
        // Insert kategori BK jika belum ada
        $insertCategories = [
            ['bk_prestasi', 'BK Prestasi', 'Catatan prestasi siswa dari BK', '#28a745', 'bi-award'],
            ['bk_pelanggaran', 'BK Pelanggaran', 'Catatan pelanggaran siswa dari BK', '#dc3545', 'bi-exclamation-triangle'],
            ['bk_konseling', 'BK Konseling', 'Catatan konseling siswa dari BK', '#17a2b8', 'bi-heart']
        ];
        
        foreach ($insertCategories as $cat) {
            try {
                $db->query("INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES (?, ?, ?, ?, ?)", $cat);
                echo "<p style='color: green;'>✅ Kategori {$cat[1]} ditambahkan</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Kategori {$cat[1]} sudah ada</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ Kategori BK sudah lengkap</p>";
    }
    
    // Tambahkan data catatan contoh
    echo "<p>4. Menambahkan data catatan contoh...</p>";
    
    $contohCatatan = [
        // Prestasi
        [
            'jenis_catatan' => 'bk_prestasi',
            'judul_catatan' => 'Juara 1 Lomba Matematika',
            'isi_catatan' => 'Siswa berhasil meraih juara 1 dalam lomba matematika tingkat kabupaten. Menunjukkan kemampuan analisis yang sangat baik.',
            'tingkat_prioritas' => 'tinggi'
        ],
        [
            'jenis_catatan' => 'bk_prestasi',
            'judul_catatan' => 'Prestasi Olahraga Basket',
            'isi_catatan' => 'Terpilih menjadi kapten tim basket sekolah dan berhasil membawa tim ke final kompetisi antar sekolah.',
            'tingkat_prioritas' => 'sedang'
        ],
        
        // Pelanggaran
        [
            'jenis_catatan' => 'bk_pelanggaran',
            'judul_catatan' => 'Terlambat Masuk Kelas',
            'isi_catatan' => 'Siswa terlambat masuk kelas sebanyak 3 kali dalam seminggu. Perlu pembinaan tentang kedisiplinan waktu.',
            'tingkat_prioritas' => 'sedang',
            'tindak_lanjut' => 'Diberikan teguran lisan dan diminta membuat surat pernyataan tidak akan mengulangi.'
        ],
        [
            'jenis_catatan' => 'bk_pelanggaran',
            'judul_catatan' => 'Tidak Mengerjakan Tugas',
            'isi_catatan' => 'Siswa tidak mengerjakan tugas matematika dan bahasa Indonesia. Sudah diberikan peringatan oleh guru mata pelajaran.',
            'tingkat_prioritas' => 'rendah',
            'tindak_lanjut' => 'Diminta mengerjakan tugas pengganti dan konseling dengan guru BK.'
        ],
        
        // Konseling
        [
            'jenis_catatan' => 'bk_konseling',
            'judul_catatan' => 'Konseling Masalah Keluarga',
            'isi_catatan' => 'Siswa mengalami masalah di rumah yang mempengaruhi konsentrasi belajar. Perlu pendampingan khusus.',
            'tingkat_prioritas' => 'tinggi',
            'tindak_lanjut' => 'Dijadwalkan sesi konseling rutin setiap minggu dan koordinasi dengan orang tua.'
        ]
    ];
    
    $berhasilDitambahkan = 0;
    
    foreach ($siswa as $s) {
        $siswaId = $s['id_siswa'];
        $namaSiswa = $s['nama_lengkap'];
        
        echo "<p><strong>Menambahkan catatan untuk: {$namaSiswa}</strong></p>";
        
        // Tambahkan beberapa catatan untuk setiap siswa
        $catatanUntukSiswa = array_slice($contohCatatan, 0, rand(2, 5)); // Random 2-5 catatan per siswa
        
        foreach ($catatanUntukSiswa as $catatan) {
            try {
                $data = array_merge($catatan, [
                    'siswa_id' => $siswaId,
                    'tanggal_catatan' => date('Y-m-d', strtotime('-' . rand(1, 30) . ' days')), // Random tanggal dalam 30 hari terakhir
                    'status_catatan' => 'aktif',
                    'created_by' => 1 // Assuming admin user ID is 1
                ]);
                
                $result = $catatanModel->create($data);
                
                if ($result) {
                    echo "<p style='color: green; margin-left: 20px;'>✅ {$catatan['judul_catatan']}</p>";
                    $berhasilDitambahkan++;
                } else {
                    echo "<p style='color: red; margin-left: 20px;'>❌ Gagal: {$catatan['judul_catatan']}</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red; margin-left: 20px;'>❌ Error: {$catatan['judul_catatan']} - {$e->getMessage()}</p>";
            }
        }
    }
    
    echo "<p>5. Menampilkan statistik catatan...</p>";
    
    // Tampilkan statistik untuk setiap siswa
    foreach ($siswa as $s) {
        $siswaId = $s['id_siswa'];
        $namaSiswa = $s['nama_lengkap'];
        
        $stats = $catatanModel->getDetailedStatistics($siswaId);
        $grouped = $catatanModel->getGroupedBySiswaId($siswaId);
        
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>{$namaSiswa}</h4>";
        echo "<ul>";
        echo "<li>Total Catatan: <strong>{$stats['total_catatan']}</strong></li>";
        echo "<li>Prestasi: <strong style='color: green;'>{$stats['total_prestasi']}</strong></li>";
        echo "<li>Pelanggaran: <strong style='color: red;'>{$stats['total_pelanggaran']}</strong></li>";
        echo "<li>Konseling: <strong style='color: blue;'>{$stats['total_konseling']}</strong></li>";
        echo "<li>Total Bimbingan: <strong>{$stats['total_bimbingan']}</strong></li>";
        echo "</ul>";
        
        echo "<p><strong>Detail per kategori:</strong></p>";
        echo "<ul>";
        foreach ($stats['by_category'] as $category => $count) {
            if ($count > 0) {
                echo "<li>{$category}: {$count}</li>";
            }
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3 style='color: green;'>✅ Testing Selesai!</h3>";
    echo "<p><strong>Total catatan berhasil ditambahkan: {$berhasilDitambahkan}</strong></p>";
    echo "<p>Sekarang Anda dapat mengakses halaman detail siswa untuk melihat statistik prestasi dan pelanggaran yang sudah berfungsi.</p>";
    
    echo "<h4>Langkah selanjutnya:</h4>";
    echo "<ol>";
    echo "<li>Akses halaman detail siswa</li>";
    echo "<li>Klik tab 'Catatan Siswa'</li>";
    echo "<li>Lihat statistik prestasi dan pelanggaran yang sudah muncul</li>";
    echo "<li>Cek accordion untuk melihat detail catatan per kategori</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Pastikan database sudah terhubung dan tabel catatan sudah dibuat.</p>";
}
?>
