<?php
require_once __DIR__ . '/Database.php';

class Berkas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Get files by student ID
     */
    public function getBySiswaId($siswa_id) {
        try {
            return $this->db->fetchAll("
                SELECT * FROM berkas
                WHERE siswa_id = ?
                ORDER BY jenis_berkas, created_at DESC
            ", [$siswa_id]);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Create new file record
     */
    public function create($data) {
        $sql = "INSERT INTO berkas (
            siswa_id, jenis_berkas, nama_berkas, nama_file_asli,
            nama_file_sistem, ukuran_file, mime_type, file_path,
            file_hash, keterangan, uploaded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $data['siswa_id'],
            $data['jenis_berkas'],
            $data['nama_berkas'],
            $data['nama_file_asli'],
            $data['nama_file_sistem'],
            $data['ukuran_file'],
            $data['mime_type'],
            $data['file_path'],
            $data['file_hash'],
            $data['keterangan'] ?? null,
            $data['uploaded_by'] ?? 1
        ];

        $this->db->query($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * Delete file record
     */
    public function delete($id) {
        $this->db->query("DELETE FROM berkas WHERE id = ?", [$id]);
        return true;
    }

    /**
     * Get file by ID
     */
    public function getById($id) {
        return $this->db->fetch("SELECT * FROM berkas WHERE id = ?", [$id]);
    }

    /**
     * Get allowed file types with categories
     */
    public function getFileCategories() {
        return [
            'Dokumen Identitas' => [
                'kartu_keluarga' => 'Kartu Keluarga',
                'akta_lahir' => 'Akta Kelahiran'
            ],
            'Rapor' => [
                'rapor_kelas_x' => 'Rapor Kelas X',
                'rapor_kelas_xi' => 'Rapor Kelas XI',
                'rapor_kelas_xii' => 'Rapor Kelas XII',
                'rapor_kpp' => 'Rapor KPP',
                'rapor_kpa' => 'Rapor KPA'
            ],
            'Ijazah' => [
                'ijazah_sd' => 'Ijazah SD/MI',
                'ijazah_smp' => 'Ijazah SMP/MTs',
                'ijazah_sma' => 'Ijazah SMA/SMK/MA'
            ],
            'Foto' => [
                'foto_siswa' => 'Foto Siswa'
            ],
            'Surat' => [
                'surat_keterangan_sehat' => 'Surat Keterangan Sehat',
                'surat_kelakuan_baik' => 'Surat Kelakuan Baik',
                'surat_peringatan_1' => 'Surat Peringatan 1',
                'surat_peringatan_2' => 'Surat Peringatan 2',
                'surat_peringatan_3' => 'Surat Peringatan 3',
                'surat_panggilan_ortu' => 'Surat Panggilan Orang Tua'
            ],
            'Prestasi' => [
                'piagam_prestasi' => 'Piagam Prestasi',
                'sertifikat_lomba' => 'Sertifikat Lomba',
                'penghargaan_akademik' => 'Penghargaan Akademik'
            ],
            'Lainnya' => [
                'lainnya' => 'Dokumen Lainnya'
            ]
        ];
    }

    /**
     * Get flat list of allowed file types
     */
    public function getAllowedTypes() {
        $types = [];
        foreach ($this->getFileCategories() as $category => $items) {
            $types = array_merge($types, $items);
        }
        return $types;
    }

    /**
     * Get allowed MIME types
     */
    public function getAllowedMimeTypes() {
        return [
            'application/pdf',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
    }

    /**
     * Get max file size (in bytes)
     */
    public function getMaxFileSize() {
        return 5 * 1024 * 1024; // 5MB
    }

    /**
     * Validate file upload
     */
    public function validateFile($file, $jenis_berkas) {
        $errors = [];

        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $errors[] = 'File tidak ditemukan';
            return $errors;
        }

        // Check file size
        if ($file['size'] > $this->getMaxFileSize()) {
            $errors[] = 'Ukuran file terlalu besar (maksimal 5MB)';
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $this->getAllowedMimeTypes())) {
            $errors[] = 'Tipe file tidak diizinkan. Gunakan PDF, JPG, PNG, atau DOC';
        }

        // Check file extension
        $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'Ekstensi file tidak diizinkan';
        }

        return $errors;
    }

    /**
     * Generate descriptive filename based on document type
     */
    public function generateFileName($originalName, $siswaId, $jenisberkas) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid()), 0, 6);

        // Get descriptive name for document type
        $documentNames = $this->getDocumentNames();
        $docName = $documentNames[$jenisberkas] ?? $jenisberkas;

        // Clean document name for filename (remove spaces, special chars)
        $cleanDocName = $this->cleanForFilename($docName);

        return "{$cleanDocName}_Siswa_{$siswaId}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Get descriptive document names for filename
     */
    public function getDocumentNames() {
        return [
            // Dokumen Identitas
            'kartu_keluarga' => 'Kartu_Keluarga',
            'akta_lahir' => 'Akta_Kelahiran',

            // Rapor
            'rapor_kelas_x' => 'Rapor_Kelas_X',
            'rapor_kelas_xi' => 'Rapor_Kelas_XI',
            'rapor_kelas_xii' => 'Rapor_Kelas_XII',
            'rapor_kpp' => 'Rapor_KPP',
            'rapor_kpa' => 'Rapor_KPA',

            // Ijazah
            'ijazah_sd' => 'Ijazah_SD',
            'ijazah_smp' => 'Ijazah_SMP',
            'ijazah_sma' => 'Ijazah_SMA',

            // Foto
            'foto_siswa' => 'Foto_Siswa',

            // Surat
            'surat_keterangan_sehat' => 'Surat_Keterangan_Sehat',
            'surat_kelakuan_baik' => 'Surat_Kelakuan_Baik',
            'surat_peringatan_1' => 'Surat_Peringatan_1',
            'surat_peringatan_2' => 'Surat_Peringatan_2',
            'surat_peringatan_3' => 'Surat_Peringatan_3',
            'surat_panggilan_ortu' => 'Surat_Panggilan_Orang_Tua',

            // Prestasi
            'piagam_prestasi' => 'Piagam_Prestasi',
            'sertifikat_lomba' => 'Sertifikat_Lomba',
            'penghargaan_akademik' => 'Penghargaan_Akademik',

            // Lainnya
            'lainnya' => 'Dokumen_Lainnya'
        ];
    }

    /**
     * Clean string for use in filename
     */
    private function cleanForFilename($string) {
        // Remove or replace special characters
        $string = str_replace([' ', '-', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $string);
        // Remove multiple underscores
        $string = preg_replace('/_+/', '_', $string);
        // Remove leading/trailing underscores
        $string = trim($string, '_');
        return $string;
    }

    /**
     * Get upload directory with subfolder based on document type
     */
    public function getUploadDir($jenisberkas = null) {
        $baseDir = __DIR__ . '/../../public/uploads/berkas/';

        if ($jenisberkas) {
            // Create subfolder based on document category
            $category = $this->getDocumentCategory($jenisberkas);
            $uploadDir = $baseDir . $category . '/';
        } else {
            $uploadDir = $baseDir;
        }

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        return $uploadDir;
    }

    /**
     * Get document category for folder organization
     */
    public function getDocumentCategory($jenisberkas) {
        $categories = [
            // Dokumen Identitas
            'kartu_keluarga' => 'identitas',
            'akta_lahir' => 'identitas',

            // Rapor
            'rapor_kelas_x' => 'rapor',
            'rapor_kelas_xi' => 'rapor',
            'rapor_kelas_xii' => 'rapor',
            'rapor_kpp' => 'rapor',
            'rapor_kpa' => 'rapor',

            // Ijazah
            'ijazah_sd' => 'ijazah',
            'ijazah_smp' => 'ijazah',
            'ijazah_sma' => 'ijazah',

            // Foto
            'foto_siswa' => 'foto',

            // Surat
            'surat_keterangan_sehat' => 'surat',
            'surat_kelakuan_baik' => 'surat',
            'surat_peringatan_1' => 'surat',
            'surat_peringatan_2' => 'surat',
            'surat_peringatan_3' => 'surat',
            'surat_panggilan_ortu' => 'surat',

            // Prestasi
            'piagam_prestasi' => 'prestasi',
            'sertifikat_lomba' => 'prestasi',
            'penghargaan_akademik' => 'prestasi',

            // Lainnya
            'lainnya' => 'lainnya'
        ];

        return $categories[$jenisberkas] ?? 'lainnya';
    }

    /**
     * Get full file path with category folder
     */
    public function getFullFilePath($jenisberkas, $filename) {
        $category = $this->getDocumentCategory($jenisberkas);
        return "uploads/berkas/{$category}/{$filename}";
    }

    /**
     * Get berkas by siswa ID and category
     */
    public function getBySiswaIdAndCategory($siswaId, $category) {
        $categoryTypes = [];
        $fileCategories = $this->getFileCategories();

        // Find all jenis_berkas for this category
        foreach ($fileCategories as $catName => $types) {
            if (strtolower($catName) === strtolower($category)) {
                $categoryTypes = array_keys($types);
                break;
            }
        }

        if (empty($categoryTypes)) {
            return [];
        }

        $placeholders = str_repeat('?,', count($categoryTypes) - 1) . '?';
        $sql = "SELECT * FROM berkas WHERE siswa_id = ? AND jenis_berkas IN ($placeholders) ORDER BY created_at DESC";

        $params = array_merge([$siswaId], $categoryTypes);
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get berkas statistics by category for a student
     */
    public function getStatsByCategory($siswaId) {
        $sql = "SELECT jenis_berkas, COUNT(*) as count, SUM(ukuran_file) as total_size
                FROM berkas WHERE siswa_id = ? GROUP BY jenis_berkas";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$siswaId]);

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stats = [];

        foreach ($results as $result) {
            $category = $this->getDocumentCategory($result['jenis_berkas']);
            if (!isset($stats[$category])) {
                $stats[$category] = ['count' => 0, 'total_size' => 0];
            }
            $stats[$category]['count'] += $result['count'];
            $stats[$category]['total_size'] += $result['total_size'];
        }

        return $stats;
    }
}
?>