<?php
/**
 * Fix Student Data
 * Script ini akan memperbaiki data siswa yang tidak tampil karena masalah tahun ajaran
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/helpers/AcademicYear.php';

echo "<h1>🔧 Fix Student Data</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Current System Status</h2>";
    
    // Check current academic year
    $currentYear = AcademicYear::getCurrentAcademicYear();
    echo "<p style='color: green;'>✅ Current Academic Year: <strong>{$currentYear}</strong></p>";
    
    // Check students without proper class assignment
    $studentsWithoutProperClass = $db->fetchAll("
        SELECT s.*, k.nama_kelas, k.tahun_pelajaran
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        WHERE s.status_siswa = 'aktif'
        ORDER BY s.id_siswa
    ");
    
    echo "<p style='color: blue;'>ℹ️ Total Active Students: " . count($studentsWithoutProperClass) . "</p>";
    
    // Analyze the data
    $studentsWithNullYear = 0;
    $studentsWithWrongYear = 0;
    $studentsWithCorrectYear = 0;
    $studentsWithoutClass = 0;
    
    foreach ($studentsWithoutProperClass as $student) {
        if (empty($student['kelas_id'])) {
            $studentsWithoutClass++;
        } elseif (empty($student['tahun_pelajaran'])) {
            $studentsWithNullYear++;
        } elseif ($student['tahun_pelajaran'] !== $currentYear) {
            $studentsWithWrongYear++;
        } else {
            $studentsWithCorrectYear++;
        }
    }
    
    echo "<h3>Data Analysis:</h3>";
    echo "<p>👥 Students with correct year ({$currentYear}): {$studentsWithCorrectYear}</p>";
    echo "<p>⚠️ Students with wrong year: {$studentsWithWrongYear}</p>";
    echo "<p>❌ Students with NULL year: {$studentsWithNullYear}</p>";
    echo "<p>🚫 Students without class: {$studentsWithoutClass}</p>";
    
    // Get available classes for current year
    $availableClasses = $db->fetchAll("
        SELECT id, nama_kelas, tingkat 
        FROM kelas 
        WHERE tahun_pelajaran = ? AND is_active = 1 AND tingkat > 0
        ORDER BY tingkat, nama_kelas
    ", [$currentYear]);
    
    echo "<h3>Available Classes for {$currentYear}:</h3>";
    foreach ($availableClasses as $kelas) {
        echo "<p style='margin-left: 20px;'>🏫 {$kelas['nama_kelas']} (ID: {$kelas['id']}, Tingkat: {$kelas['tingkat']})</p>";
    }
    
    if (empty($availableClasses)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ No Classes Available</h4>";
        echo "<p>No active classes found for current year {$currentYear}. Creating some classes first...</p>";
        echo "</div>";
        
        // Create some basic classes for current year
        $basicClasses = [
            ['X-IPA-1', 10],
            ['X-IPA-2', 10],
            ['X-IPS-1', 10],
            ['XI-IPA-1', 11],
            ['XI-IPA-2', 11],
            ['XI-IPS-1', 11],
            ['XII-IPA-1', 12],
            ['XII-IPA-2', 12],
            ['XII-IPS-1', 12]
        ];
        
        foreach ($basicClasses as $classData) {
            try {
                $db->query("
                    INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ", [
                    $classData[0],
                    $classData[1],
                    $currentYear,
                    'Wali Kelas ' . $classData[0],
                    30,
                    1,
                    1
                ]);
                
                echo "<p style='color: green;'>✅ Created class: {$classData[0]}</p>";
                
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Class {$classData[0]} might already exist</p>";
            }
        }
        
        // Refresh available classes
        $availableClasses = $db->fetchAll("
            SELECT id, nama_kelas, tingkat 
            FROM kelas 
            WHERE tahun_pelajaran = ? AND is_active = 1 AND tingkat > 0
            ORDER BY tingkat, nama_kelas
        ", [$currentYear]);
        
        echo "<p style='color: green;'>✅ Created " . count($availableClasses) . " classes for {$currentYear}</p>";
    }
    
    echo "<h2>🔧 Fixing Student Data</h2>";
    
    $fixedStudents = 0;
    $skippedStudents = 0;
    
    // Fix students without proper class assignment
    foreach ($studentsWithoutProperClass as $student) {
        $needsFix = false;
        $reason = '';
        
        if (empty($student['kelas_id'])) {
            $needsFix = true;
            $reason = 'No class assigned';
        } elseif (empty($student['tahun_pelajaran'])) {
            $needsFix = true;
            $reason = 'Class has NULL year';
        } elseif ($student['tahun_pelajaran'] !== $currentYear) {
            $needsFix = true;
            $reason = "Wrong year ({$student['tahun_pelajaran']})";
        }
        
        if ($needsFix && !empty($availableClasses)) {
            // Assign to a random class from current year
            $randomClass = $availableClasses[array_rand($availableClasses)];
            
            try {
                $db->query("
                    UPDATE siswa 
                    SET kelas_id = ?, updated_at = NOW() 
                    WHERE id_siswa = ?
                ", [$randomClass['id'], $student['id_siswa']]);
                
                $fixedStudents++;
                echo "<p style='color: green;'>✅ Fixed: {$student['nama_lengkap']} (NIS: {$student['nis']}) → {$randomClass['nama_kelas']} ({$reason})</p>";
                
            } catch (Exception $e) {
                $skippedStudents++;
                echo "<p style='color: red;'>❌ Failed to fix {$student['nama_lengkap']}: " . $e->getMessage() . "</p>";
            }
        } elseif ($needsFix) {
            $skippedStudents++;
            echo "<p style='color: orange;'>⚠️ Skipped {$student['nama_lengkap']}: No available classes</p>";
        }
    }
    
    echo "<h2>📊 Final Results</h2>";
    
    // Get updated statistics
    $finalStats = $db->fetch("
        SELECT COUNT(*) as total 
        FROM siswa s 
        JOIN kelas k ON s.kelas_id = k.id 
        WHERE s.status_siswa = 'aktif' AND k.tahun_pelajaran = ?
    ", [$currentYear]);
    
    $studentsPerClass = $db->fetchAll("
        SELECT k.nama_kelas, COUNT(s.id_siswa) as jumlah_siswa
        FROM kelas k
        LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
        WHERE k.tahun_pelajaran = ? AND k.is_active = 1 AND k.tingkat > 0
        GROUP BY k.id, k.nama_kelas
        ORDER BY k.tingkat, k.nama_kelas
    ", [$currentYear]);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Fix Operation Complete</h4>";
    echo "<ul>";
    echo "<li><strong>Students Fixed:</strong> {$fixedStudents}</li>";
    echo "<li><strong>Students Skipped:</strong> {$skippedStudents}</li>";
    echo "<li><strong>Total Students in {$currentYear}:</strong> " . ($finalStats['total'] ?? 0) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Updated Students per Class:</h3>";
    foreach ($studentsPerClass as $classData) {
        echo "<p style='margin-left: 20px;'>🏫 {$classData['nama_kelas']}: {$classData['jumlah_siswa']} students</p>";
    }
    
    // Test the query that's used in the application
    echo "<h2>🧪 Testing Application Query</h2>";
    
    $testQuery = "
        SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        WHERE s.status_siswa = 'aktif' AND k.tahun_pelajaran = ?
        ORDER BY k.tingkat, s.nama_lengkap
        LIMIT 10
    ";
    
    $testResults = $db->fetchAll($testQuery, [$currentYear]);
    
    echo "<p><strong>Query Results:</strong> " . count($testResults) . " students found</p>";
    
    if (!empty($testResults)) {
        echo "<h4>Sample Results:</h4>";
        foreach (array_slice($testResults, 0, 5) as $student) {
            echo "<p>👤 {$student['nama_lengkap']} (NIS: {$student['nis']}) - Class: {$student['nama_kelas']} - Year: {$student['tahun_pelajaran']}</p>";
        }
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue; text-decoration: underline;'>🔗 Test Student List Page</a></p>";
    echo "<p><a href='/siswa-app/debug_siswa_data.php' target='_blank' style='color: blue; text-decoration: underline;'>🔗 Run Debug Script Again</a></p>";
    
    if (($finalStats['total'] ?? 0) > 0) {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🎉 Success!</h4>";
        echo "<p>Student data has been fixed. You should now see " . ($finalStats['total'] ?? 0) . " students in the application.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Still No Data</h4>";
        echo "<p>No students found even after fixing. You may need to add some students first.</p>";
        echo "<p><a href='/siswa-app/setup_sample_students.php' target='_blank'>Add Sample Students</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
