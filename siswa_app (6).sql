-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- <PERSON><PERSON><PERSON> pembuatan: 04 Jun 2025 pada 15.32
-- Versi server: 10.4.27-MariaDB
-- Versi PHP: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `siswa_app`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON> dari tabel `absensi`
--

CREATE TABLE `absensi` (
  `id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `tanggal` date NOT NULL,
  `jenis_ketidakhadiran` enum('sakit','ijin','alpha') NOT NULL,
  `keterangan` text DEFAULT NULL,
  `surat_keterangan` varchar(255) DEFAULT NULL,
  `jam_masuk` time DEFAULT NULL,
  `jam_keluar` time DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `absensi`
--

INSERT INTO `absensi` (`id`, `siswa_id`, `tanggal`, `jenis_ketidakhadiran`, `keterangan`, `surat_keterangan`, `jam_masuk`, `jam_keluar`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, '2024-12-01', 'sakit', 'Demam tinggi, istirahat di rumah', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(2, 1, '2024-12-02', 'sakit', 'Masih demam, kontrol ke dokter', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(3, 2, '2024-12-03', 'ijin', 'Acara keluarga di luar kota', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(4, 5, '2024-12-04', 'alpha', 'Tidak ada keterangan', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(5, 6, '2024-12-05', 'sakit', 'Flu dan batuk', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(6, 7, '2024-12-06', 'ijin', 'Mengurus dokumen penting', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(7, 1, '2024-12-09', 'alpha', 'Terlambat bangun, tidak masuk sekolah', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(8, 2, '2024-12-10', 'sakit', 'Sakit perut, ke puskesmas', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(9, 13, '2024-12-11', 'ijin', 'Menjenguk nenek yang sakit', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02'),
(10, 14, '2024-12-12', 'sakit', 'Pusing dan mual', NULL, NULL, NULL, 1, '2025-06-02 14:04:02', '2025-06-02 14:04:02');

-- --------------------------------------------------------

--
-- Struktur dari tabel `activity_log`
--

CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL,
  `table_name` varchar(50) NOT NULL,
  `record_id` int(11) NOT NULL,
  `action_type` enum('INSERT','UPDATE','DELETE') NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `audit_logs`
--

CREATE TABLE `audit_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(50) NOT NULL,
  `table_name` varchar(50) NOT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `berkas`
--

CREATE TABLE `berkas` (
  `id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `jenis_berkas` enum('kartu_keluarga','akta_lahir','rapor_kelas_x','rapor_kelas_xi','rapor_kelas_xii','rapor_kpp','rapor_kpa','ijazah_sd','ijazah_smp','ijazah_sma','foto_siswa','surat_keterangan_sehat','surat_kelakuan_baik','surat_peringatan_1','surat_peringatan_2','surat_peringatan_3','surat_panggilan_ortu','piagam_prestasi','sertifikat_lomba','penghargaan_akademik','lainnya') NOT NULL,
  `nama_berkas` varchar(255) NOT NULL,
  `nama_file_asli` varchar(255) NOT NULL,
  `nama_file_sistem` varchar(255) NOT NULL,
  `ukuran_file` int(11) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_hash` varchar(64) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data untuk tabel `berkas`
--

INSERT INTO `berkas` (`id`, `siswa_id`, `jenis_berkas`, `nama_berkas`, `nama_file_asli`, `nama_file_sistem`, `ukuran_file`, `mime_type`, `file_path`, `file_hash`, `keterangan`, `uploaded_by`, `created_at`, `updated_at`) VALUES
(8, 15, 'lainnya', 'Akta Kelahiran Ahmad Fauzi', 'akta_ahmad_fauzi.pdf', 'akta_15_001.pdf', 0, '', 'uploads/berkas/lainnya/akta_15_001.pdf', '', 'Akta kelahiran asli', 1, '2025-06-02 14:46:50', '2025-06-03 13:59:06'),
(9, 15, 'lainnya', 'Kartu Keluarga Ahmad Fauzi', 'kk_ahmad_fauzi.pdf', 'kk_15_001.pdf', 0, '', 'uploads/berkas/lainnya/kk_15_001.pdf', '', 'Kartu keluarga terbaru', 1, '2025-06-02 14:46:50', '2025-06-03 13:59:06'),
(10, 15, 'lainnya', 'Ijazah SMP Ahmad Fauzi', 'ijazah_smp_ahmad.pdf', 'ijazah_15_001.pdf', 0, '', 'uploads/berkas/lainnya/ijazah_15_001.pdf', '', 'Ijazah SMP tahun 2023', 1, '2025-06-02 14:46:50', '2025-06-03 13:59:06'),
(11, 15, '', 'Rapor Semester 1', 'rapor_sem1_ahmad.pdf', 'rapor_15_001.pdf', 0, '', 'uploads/berkas/', '', 'Rapor semester 1 kelas X', 1, '2025-06-02 14:46:50', '2025-06-02 14:46:50'),
(14, 1, '', '1175817-533696-59083-149846641-1389269391.pdf', '1175817-533696-59083-149846641-1389269391.pdf', 'Rapor_Siswa_1_20250602165505_aaa745.pdf', 1478843, 'application/pdf', 'uploads/berkas/lainnya/Rapor_Siswa_1_20250602165505_aaa745.pdf', '3f81384dd661963abd3dd0749581170e236e6b5eac7ef36cbbd79b6604262e36', '', 5, '2025-06-02 14:55:05', '2025-06-02 14:55:05'),
(15, 57, 'kartu_keluarga', '1175817-533696-59083-149846641-1389269391.pdf', '1175817-533696-59083-149846641-1389269391.pdf', 'Kartu_Keluarga_Siswa_57_20250603154851_f3b2a0.pdf', 1478843, 'application/pdf', 'uploads/berkas/identitas/Kartu_Keluarga_Siswa_57_20250603154851_f3b2a0.pdf', '3f81384dd661963abd3dd0749581170e236e6b5eac7ef36cbbd79b6604262e36', '', 1, '2025-06-03 13:48:51', '2025-06-03 13:48:51'),
(16, 57, 'rapor_kelas_x', '4_ DATA ISIAN PESERTA DIDIK TAHUN 2020-2.pdf', '4_ DATA ISIAN PESERTA DIDIK TAHUN 2020-2.pdf', 'Rapor_Kelas_X_Siswa_57_20250603155445_cee95f.pdf', 8310, 'application/pdf', 'uploads/berkas/rapor/Rapor_Kelas_X_Siswa_57_20250603155445_cee95f.pdf', '02a9b3b3e38e15f87e7047eb6d340937108430319e5758797d1fe33c53b62504', '', 1, '2025-06-03 13:54:45', '2025-06-04 13:20:48'),
(17, 57, 'rapor_kelas_xi', 'download.pdf', 'download.pdf', 'Rapor_Kelas_XI_Siswa_57_20250603155807_522292.pdf', 131283, 'application/pdf', 'uploads/berkas/rapor/Rapor_Kelas_XI_Siswa_57_20250603155807_522292.pdf', '3a05cb638a719dee1d64069d440f98f0f4d91773c4a8c67814b1af7a3f04a234', '', 1, '2025-06-03 13:58:07', '2025-06-04 13:20:48'),
(18, 57, 'rapor_kelas_xii', 'Keuskupan Agung Semarang.pdf', 'Keuskupan Agung Semarang.pdf', 'Rapor_Kelas_XII_Siswa_57_20250603155826_6975fd.pdf', 294898, 'application/pdf', 'uploads/berkas/rapor/Rapor_Kelas_XII_Siswa_57_20250603155826_6975fd.pdf', '2bd24289d8d734c3a272b037ded81c6d65fc8bc5e7bff567be3c7f8801849588', '', 1, '2025-06-03 13:58:26', '2025-06-04 13:20:48');

-- --------------------------------------------------------

--
-- Struktur dari tabel `catatan_siswa`
--

CREATE TABLE `catatan_siswa` (
  `id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `jenis_catatan` enum('pamong_mp','pamong_mt','pamong_mm','pamong_mu','wali_kpp','wali_x','wali_xi','wali_xii','wali_kpa','bk_konseling','bk_pelanggaran','bk_prestasi','bk_lainnya') NOT NULL,
  `judul_catatan` varchar(255) NOT NULL,
  `isi_catatan` text NOT NULL,
  `tanggal_catatan` date NOT NULL,
  `tingkat_prioritas` enum('rendah','sedang','tinggi','urgent') DEFAULT 'sedang',
  `status_catatan` enum('draft','aktif','selesai','ditunda') DEFAULT 'aktif',
  `tindak_lanjut` text DEFAULT NULL,
  `tanggal_tindak_lanjut` date DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data untuk tabel `catatan_siswa`
--

INSERT INTO `catatan_siswa` (`id`, `siswa_id`, `jenis_catatan`, `judul_catatan`, `isi_catatan`, `tanggal_catatan`, `tingkat_prioritas`, `status_catatan`, `tindak_lanjut`, `tanggal_tindak_lanjut`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 1, 'pamong_mp', 'Evaluasi Awal Masa Persiapan', 'Siswa menunjukkan antusiasme yang baik dalam mengikuti program masa persiapan. Perlu bimbingan lebih dalam hal kedisiplinan waktu.', '2024-01-15', 'sedang', 'aktif', 'Jadwalkan sesi konseling individual untuk membahas manajemen waktu', '2024-01-22', 1, '2025-06-02 06:35:10', NULL, '2025-06-02 06:35:10'),
(2, 1, 'wali_x', 'Perkembangan Akademik Semester 1', 'Prestasi akademik siswa cukup baik dengan rata-rata nilai 8.2. Namun perlu peningkatan di mata pelajaran Matematika.', '2024-01-10', 'tinggi', 'aktif', 'Koordinasi dengan guru Matematika untuk program remedial', '2024-01-17', 1, '2025-06-02 06:35:10', NULL, '2025-06-02 06:35:10'),
(3, 1, 'bk_konseling', 'Konseling Adaptasi Lingkungan Sekolah', 'Siswa mengalami kesulitan adaptasi dengan lingkungan baru. Sudah dilakukan 2 sesi konseling dengan progress yang positif.', '2024-01-08', 'sedang', 'selesai', NULL, NULL, 1, '2025-06-02 06:35:10', NULL, '2025-06-02 06:35:10'),
(4, 1, 'pamong_mm', 'Siswa memerlukan pendampingan', 'Pada semester ini siswa tersebut memerlukan pendampingan dikarenakan kurangnya minat menjadi romio', '2025-06-02', 'tinggi', 'aktif', '', '0000-00-00', 1, '2025-06-02 12:21:08', NULL, '2025-06-02 12:21:08'),
(5, 15, '', 'Prestasi Akademik', 'Ahmad menunjukkan prestasi yang baik dalam mata pelajaran Matematika dengan nilai rata-rata 85.', '2024-01-15', 'sedang', 'aktif', 'Pertahankan prestasi dan tingkatkan di mata pelajaran lain', NULL, 1, '2025-06-02 14:46:50', NULL, '2025-06-02 14:46:50'),
(6, 15, '', 'Kedisiplinan', 'Ahmad selalu hadir tepat waktu dan mengikuti kegiatan sekolah dengan baik.', '2024-01-20', 'rendah', 'aktif', 'Terus pertahankan kedisiplinan', NULL, 1, '2025-06-02 14:46:50', NULL, '2025-06-02 14:46:50'),
(7, 15, '', 'Konseling Karir', 'Diskusi mengenai minat dan bakat Ahmad untuk melanjutkan ke perguruan tinggi.', '2024-02-01', 'tinggi', 'aktif', 'Lakukan tes minat bakat dan konseling lanjutan', NULL, 1, '2025-06-02 14:46:50', NULL, '2025-06-02 14:46:50'),
(8, 15, '', 'Tugas Proyek', 'Ahmad berhasil menyelesaikan proyek akhir semester dengan baik.', '2024-02-10', 'sedang', 'selesai', NULL, NULL, 1, '2025-06-02 14:46:50', NULL, '2025-06-02 14:46:50'),
(9, 15, '', 'Juara Olimpiade', 'Ahmad meraih juara 2 dalam Olimpiade Matematika tingkat kabupaten.', '2024-02-15', 'tinggi', 'aktif', 'Persiapkan untuk olimpiade tingkat provinsi', NULL, 1, '2025-06-02 14:46:50', NULL, '2025-06-02 14:46:50'),
(10, 15, '', 'Terlambat', 'Ahmad terlambat masuk kelas sebanyak 2 kali dalam seminggu.', '2024-02-20', 'sedang', 'aktif', 'Beri peringatan dan pantau kedisiplinan', NULL, 1, '2025-06-02 14:46:50', NULL, '2025-06-02 14:46:50'),
(11, 57, 'bk_prestasi', 'Siswa memerlukan pendampingan', 'jhjvbhh', '2025-06-03', 'sedang', 'aktif', NULL, '0000-00-00', 1, '2025-06-03 13:56:24', NULL, '2025-06-03 13:56:24');

-- --------------------------------------------------------

--
-- Struktur dari tabel `csrf_tokens`
--

CREATE TABLE `csrf_tokens` (
  `id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `kategori_catatan`
--

CREATE TABLE `kategori_catatan` (
  `id` int(11) NOT NULL,
  `kode_kategori` varchar(20) NOT NULL,
  `nama_kategori` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `warna_badge` varchar(7) DEFAULT '#6c757d',
  `icon_class` varchar(50) DEFAULT 'bi-note-text',
  `is_active` tinyint(1) DEFAULT 1,
  `allowed_roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`allowed_roles`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data untuk tabel `kategori_catatan`
--

INSERT INTO `kategori_catatan` (`id`, `kode_kategori`, `nama_kategori`, `deskripsi`, `warna_badge`, `icon_class`, `is_active`, `allowed_roles`, `created_at`) VALUES
(14, 'pamong_mp', 'Catatan Pamong MP (KPP)', 'Catatan dari Pamong untuk siswa tingkat KPP', '#17a2b8', 'bi-person-badge', 1, '[\"admin\",\"pamong\"]', '2025-06-02 17:05:38'),
(15, 'pamong_mt', 'Catatan Pamong MT (X)', 'Catatan dari Pamong untuk siswa tingkat X', '#28a745', 'bi-person-badge', 1, '[\"admin\",\"pamong\"]', '2025-06-02 17:05:38'),
(16, 'pamong_mm', 'Catatan Pamong MM (XI)', 'Catatan dari Pamong untuk siswa tingkat XI', '#ffc107', 'bi-person-badge', 1, '[\"admin\",\"pamong\"]', '2025-06-02 17:05:38'),
(17, 'pamong_mu', 'Catatan Pamong MU (XII & KPA)', 'Catatan dari Pamong untuk siswa tingkat XII dan KPA', '#dc3545', 'bi-person-badge', 1, '[\"admin\",\"pamong\"]', '2025-06-02 17:05:38'),
(18, 'wali_kpp', 'Catatan Wali Kelas KPP', 'Catatan dari Wali Kelas untuk siswa KPP', '#6f42c1', 'bi-person-check', 1, '[\"admin\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(19, 'wali_x', 'Catatan Wali Kelas X', 'Catatan dari Wali Kelas untuk siswa kelas X', '#6f42c1', 'bi-person-check', 1, '[\"admin\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(20, 'wali_xi', 'Catatan Wali Kelas XI', 'Catatan dari Wali Kelas untuk siswa kelas XI', '#6f42c1', 'bi-person-check', 1, '[\"admin\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(21, 'wali_xii', 'Catatan Wali Kelas XII', 'Catatan dari Wali Kelas untuk siswa kelas XII', '#6f42c1', 'bi-person-check', 1, '[\"admin\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(22, 'wali_kpa', 'Catatan Wali Kelas KPA', 'Catatan dari Wali Kelas untuk siswa KPA', '#6f42c1', 'bi-person-check', 1, '[\"admin\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(23, 'bk_konseling', 'Konseling BK', 'Catatan konseling dari Bimbingan Konseling', '#fd7e14', 'bi-chat-heart', 1, '[\"admin\",\"bk\"]', '2025-06-02 17:05:38'),
(24, 'bk_pelanggaran', 'Pelanggaran (BK)', 'Catatan pelanggaran yang ditangani BK', '#dc3545', 'bi-exclamation-triangle', 1, '[\"admin\",\"bk\"]', '2025-06-02 17:05:38'),
(25, 'bk_prestasi', 'Prestasi (BK)', 'Catatan prestasi yang dicatat BK', '#28a745', 'bi-trophy', 1, '[\"admin\",\"bk\"]', '2025-06-02 17:05:38'),
(26, 'bk_lainnya', 'Catatan BK Lainnya', 'Catatan lainnya dari Bimbingan Konseling', '#6c757d', 'bi-journal-text', 1, '[\"admin\",\"bk\"]', '2025-06-02 17:05:38'),
(27, 'akademik', 'Akademik', 'Catatan terkait prestasi dan perkembangan akademik', '#007bff', 'bi-book', 1, '[\"admin\",\"pamong\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(28, 'prestasi', 'Prestasi', 'Catatan prestasi dan pencapaian siswa', '#28a745', 'bi-award', 1, '[\"admin\",\"pamong\",\"wali_kelas\",\"bk\"]', '2025-06-02 17:05:38'),
(29, 'pelanggaran', 'Pelanggaran', 'Catatan pelanggaran dan tindakan disiplin', '#dc3545', 'bi-shield-exclamation', 1, '[\"admin\",\"pamong\",\"wali_kelas\",\"bk\"]', '2025-06-02 17:05:38'),
(30, 'kesehatan', 'Kesehatan', 'Catatan terkait kesehatan dan kondisi medis', '#e83e8c', 'bi-heart-pulse', 1, '[\"admin\",\"pamong\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(31, 'kehadiran', 'Kehadiran', 'Catatan terkait kehadiran dan absensi', '#20c997', 'bi-calendar-check', 1, '[\"admin\",\"pamong\",\"wali_kelas\"]', '2025-06-02 17:05:38'),
(32, 'umum', 'Umum', 'Catatan umum lainnya', '#6c757d', 'bi-chat-dots', 1, '[\"admin\",\"pamong\",\"wali_kelas\",\"bk\"]', '2025-06-02 17:05:38');

-- --------------------------------------------------------

--
-- Struktur dari tabel `kelas`
--

CREATE TABLE `kelas` (
  `id` int(11) NOT NULL,
  `nama_kelas` varchar(20) NOT NULL,
  `tingkat` varchar(10) NOT NULL,
  `kurikulum_id` int(11) DEFAULT NULL,
  `jurusan` varchar(50) DEFAULT NULL,
  `tahun_pelajaran` varchar(9) NOT NULL,
  `wali_kelas` varchar(100) DEFAULT NULL,
  `wali_kelas_id` int(11) DEFAULT NULL,
  `kapasitas` int(11) DEFAULT 30,
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data untuk tabel `kelas`
--

INSERT INTO `kelas` (`id`, `nama_kelas`, `tingkat`, `kurikulum_id`, `jurusan`, `tahun_pelajaran`, `wali_kelas`, `wali_kelas_id`, `kapasitas`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'KPP A', 'KPP', 2, 'Umum', '2024/2025', 'Budi Santoso', NULL, 30, 1, 1, 1, '2025-06-02 03:55:22', '2025-06-02 16:14:26'),
(3, 'XI-2', 'XI', 1, 'Umum', '2024/2025', 'Ahmad Rahman', NULL, 30, 1, 1, 1, '2025-06-02 03:55:22', '2025-06-02 16:02:57'),
(4, 'X-2', 'X', 1, 'Umum', '2024/2025', 'Budi Santoso', NULL, 30, 1, 1, 1, '2025-06-02 03:59:25', '2025-06-02 16:02:57'),
(9, 'XII-1', 'XII', 1, 'Umum', '2024/2025', 'Rina Marlina, S.Pd', NULL, 30, 1, 1, 1, '2025-06-02 12:06:39', '2025-06-02 16:02:57'),
(10, 'KPP B', 'KPP', 2, 'Umum', '2024/2025', 'Dra. Siti Nurhaliza', NULL, 30, 1, 1, 1, '2025-06-02 12:12:45', '2025-06-02 16:14:53'),
(14, 'X-1', 'X', 1, 'Umum', '2024/2025', 'Dewi Sartika, S.S', NULL, 30, 1, 1, 1, '2025-06-02 12:12:45', '2025-06-02 16:02:57'),
(16, 'XI-1', 'XI', 1, 'Umum', '2024/2025', 'Maya Sari, S.S', NULL, 30, 1, 1, 1, '2025-06-02 12:12:45', '2025-06-02 16:02:57'),
(19, 'XII-2', 'XII', 1, 'Umum', '2024/2025', 'Hendra Gunawan, S.Pd', NULL, 30, 1, 1, 1, '2025-06-02 12:12:45', '2025-06-02 16:02:57'),
(20, 'KPA', 'KPA', 1, 'Umum', '2024/2025', 'Dr. Lestari Indah, M.Sc', NULL, 30, 1, 1, 1, '2025-06-02 12:12:45', '2025-06-02 16:02:57'),
(22, 'KPP C', 'KPP', 2, 'Umum', '2024/2025', '', NULL, 30, 1, 1, 1, '2025-06-02 13:18:44', '2025-06-02 16:15:06'),
(23, 'KPP-B', 'KPP', 1, 'Umum', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(24, 'X-IPA-2', 'X', 1, 'IPA', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(25, 'X-IPS-2', 'X', 1, 'IPS', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(26, 'XI-IPA-2', 'XI', 1, 'IPA', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(27, 'XI-IPS-1', 'XI', 1, 'IPS', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(28, 'XII-IPA-1', 'XII', 1, 'IPA', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(29, 'XII-IPS-1', 'XII', 1, 'IPS', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(30, 'KPA-A', 'KPA', 1, 'Umum', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57'),
(31, 'KPA-B', 'KPA', 1, 'Umum', '2024/2025', NULL, NULL, 30, 1, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 16:02:57');

-- --------------------------------------------------------

--
-- Struktur dari tabel `kurikulum`
--

CREATE TABLE `kurikulum` (
  `id_kurikulum` int(11) NOT NULL,
  `nama_kurikulum` varchar(100) NOT NULL,
  `kode_kurikulum` varchar(20) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `kurikulum`
--

INSERT INTO `kurikulum` (`id_kurikulum`, `nama_kurikulum`, `kode_kurikulum`, `deskripsi`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Kurikulum Merdeka', 'MERDEKA', 'Kurikulum yang memberikan kebebasan belajar kepada siswa untuk mengeksplorasi minat dan bakat mereka', 1, '2025-06-02 16:02:57', '2025-06-02 16:02:57'),
(2, 'Kurikulum Seminari', 'SEMINARI', 'Kurikulum khusus untuk pendidikan seminari dengan fokus pada pembentukan karakter dan spiritualitas', 1, '2025-06-02 16:02:57', '2025-06-02 16:02:57');

-- --------------------------------------------------------

--
-- Struktur dari tabel `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` text NOT NULL,
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `siswa`
--

CREATE TABLE `siswa` (
  `id_siswa` int(11) NOT NULL,
  `nis` varchar(20) NOT NULL,
  `nisn` varchar(20) DEFAULT NULL,
  `nama_lengkap` varchar(100) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `tempat_lahir` varchar(50) DEFAULT NULL,
  `tanggal_lahir` date DEFAULT NULL,
  `alamat` text DEFAULT NULL,
  `no_telepon` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_ayah` varchar(100) DEFAULT NULL,
  `nama_ibu` varchar(100) DEFAULT NULL,
  `pekerjaan_ayah` varchar(50) DEFAULT NULL,
  `pekerjaan_ibu` varchar(50) DEFAULT NULL,
  `kelas_id` int(11) DEFAULT NULL,
  `tahun_masuk` year(4) DEFAULT NULL,
  `status_siswa` enum('aktif','lulus','mutasi','dropout') DEFAULT 'aktif',
  `foto` varchar(255) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data untuk tabel `siswa`
--

INSERT INTO `siswa` (`id_siswa`, `nis`, `nisn`, `nama_lengkap`, `jenis_kelamin`, `tempat_lahir`, `tanggal_lahir`, `alamat`, `no_telepon`, `email`, `nama_ayah`, `nama_ibu`, `pekerjaan_ayah`, `pekerjaan_ibu`, `kelas_id`, `tahun_masuk`, `status_siswa`, `foto`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, '2024001', '1234567890', 'Ahmad Fauzi', 'L', 'Jakarta', '2007-05-15', 'Jl. Merdeka No. 123, Jakarta', NULL, NULL, 'Bapak Ahmad Yusuf', 'Ibu Siti Aminah', 'Guru', 'Ibu Rumah Tangga', 1, 2024, 'aktif', 'siswa_1_1748867442.jpg', 1, NULL, '2025-06-02 03:55:22', '2025-06-02 13:55:28'),
(2, '2024002', '1234567891', 'Siti Nurhaliza', 'P', 'Bandung', '2007-08-20', 'Jl. Sudirman No. 456, Bandung', NULL, NULL, 'Bapak Hendra Gunawan', 'Ibu Dewi Sartika', 'Pegawai Swasta', 'Pedagang', 1, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 03:55:22', '2025-06-02 13:55:28'),
(5, '2024003', '1234567892', 'Muhammad Fajar Sidiq', 'L', 'Surabaya', '2008-11-08', 'Jl. Pahlawan No. 789, Surabaya', '081234567892', '<EMAIL>', 'Bapak Bambang Sutrisno', 'Ibu Rina Marlina', 'Petani', 'Petani', 4, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 12:29:09', '2025-06-02 13:55:28'),
(6, '2024004', '1234567893', 'Putri Ayu Ningrum', 'P', 'Yogyakarta', '2008-05-12', 'Jl. Malioboro No. 321, Yogyakarta', '081234567893', '<EMAIL>', 'Bapak Agus Salim', 'Ibu Maya Sari', 'Buruh', 'Buruh', 20, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 12:29:09', '2025-06-02 13:55:28'),
(7, '2024005', '1234567894', 'Dimas Arya Wijaya', 'L', 'Medan', '2008-09-30', 'Jl. Gatot Subroto No. 654, Medan', '081234567894', '<EMAIL>', 'Bapak Doni Prasetyo', 'Ibu Lina Handayani', 'Wiraswasta', 'Wiraswasta', 4, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 12:29:09', '2025-06-02 13:55:28'),
(13, '2024101', NULL, 'Andi Pratama', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 22, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(14, '2024102', NULL, 'Siti Aisyah', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 22, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(15, '2024103', NULL, 'Budi Santoso', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 23, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(16, '2024201', NULL, 'Dewi Lestari', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 24, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(17, '2024202', NULL, 'Rizki Maulana', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 25, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(18, '2024301', NULL, 'Fitri Handayani', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 26, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(19, '2024302', NULL, 'Agus Setiawan', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 27, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(20, '2024401', NULL, 'Maya Sari', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 28, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(21, '2024402', NULL, 'Doni Prasetyo', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 29, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(24, '2024021', NULL, 'Arif Hidayat', 'L', NULL, NULL, NULL, NULL, NULL, 'Bapak Arief Hidayat Sr', 'Ibu Bella Safitri Sr', 'PNS', 'Guru', 22, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(25, '2024022', NULL, 'Bella Safitri', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 22, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(26, '2024023', NULL, 'Candra Wijaya', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 22, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(27, '2024024', NULL, 'Dina Marlina', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 22, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(28, '2024025', NULL, 'Eko Susanto', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 23, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(29, '2024026', NULL, 'Fira Amelia', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 23, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(30, '2024027', NULL, 'Gilang Ramadhan', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 23, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(31, '2024028', NULL, 'Hani Putri', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 23, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(32, '2024033', NULL, 'Muhammad Rizki', 'L', NULL, NULL, NULL, NULL, NULL, 'Bapak Muhammad Rizki Sr', 'Ibu Nadia Putri Sr', 'Dokter', 'Perawat', 14, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(33, '2024034', NULL, 'Nadia Putri', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 14, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(34, '2024035', NULL, 'Omar Faruq', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 14, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(35, '2024036', NULL, 'Putri Ayu', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 14, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(36, '2024037', NULL, 'Qori Ananda', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 4, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(37, '2024038', NULL, 'Rani Oktavia', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 4, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(38, '2024039', NULL, 'Satria Budi', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 4, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(39, '2024040', NULL, 'Tari Wulandari', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 4, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(40, '2024041', NULL, 'Umar Fadhil', 'L', NULL, NULL, NULL, NULL, NULL, 'Bapak Umar Fadhil Sr', 'Ibu Vina Safira Sr', 'Polisi', 'Ibu Rumah Tangga', 16, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(41, '2024042', NULL, 'Vina Safira', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 16, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(42, '2024043', NULL, 'Wahyu Pratama', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 16, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(43, '2024044', NULL, 'Xenia Putri', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 16, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(44, '2024045', NULL, 'Yoga Aditya', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(45, '2024046', NULL, 'Zahra Amalia', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(46, '2024047', NULL, 'Alvin Kurniawan', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(47, '2024048', NULL, 'Bunga Citra', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(48, '2024049', NULL, 'Cahyo Nugroho', 'L', NULL, NULL, NULL, NULL, NULL, 'Bapak Cahyo Nugroho Sr', 'Ibu Devi Anggraini Sr', 'Pengusaha', 'Akuntan', 9, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(49, '2024050', NULL, 'Devi Anggraini', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 9, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(50, '2024051', NULL, 'Erick Setiawan', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 9, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(51, '2024052', NULL, 'Fanny Rahayu', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 9, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(52, '2024053', NULL, 'Galih Pratama', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 19, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(53, '2024054', NULL, 'Hesti Wulandari', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 19, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(54, '2024055', NULL, 'Ivan Gunawan', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 19, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(55, '2024056', NULL, 'Jessi Permata', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 19, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(57, '2024058', NULL, 'Luna Maharani', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(58, '2024059', NULL, 'Mario Fernandes', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(59, '2024060', NULL, 'Nina Sartika', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(60, '2024061', NULL, 'Oscar Ramadhan', 'L', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28'),
(61, '2024062', NULL, 'Priska Dewi', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20, 2024, 'aktif', NULL, 1, NULL, '2025-06-02 13:55:28', '2025-06-02 13:55:28');

-- --------------------------------------------------------

--
-- Struktur dari tabel `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','pamong_mp','pamong_mt','pamong_mm','pamong_mu','wali_kelas','staff') DEFAULT 'staff',
  `pamong_type` enum('mp','mt','mm','mu') DEFAULT NULL,
  `tingkat_akses` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tingkat_akses`)),
  `nama_lengkap` varchar(100) NOT NULL,
  `foto_profil` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `failed_login_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data untuk tabel `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `role`, `pamong_type`, `tingkat_akses`, `nama_lengkap`, `foto_profil`, `is_active`, `last_login`, `failed_login_attempts`, `locked_until`, `password_reset_token`, `password_reset_expires`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$KNeTbIxnpLWjpud/b9NoYufnvSzKd2Dh9.nyMD./0r4AoyUhP7iJ6', 'admin', NULL, NULL, 'Administrator', NULL, 1, '2025-06-04 13:29:22', 0, NULL, NULL, NULL, '2025-05-31 03:29:10', '2025-06-04 13:29:22'),
(2, 'wali_x_ipa1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', NULL, NULL, 'Budi Santoso', NULL, 1, '2025-06-02 13:09:43', 0, NULL, NULL, NULL, '2025-06-02 13:03:26', '2025-06-02 13:09:43'),
(3, 'wali_x_ips1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', NULL, NULL, 'Siti Aminah', NULL, 1, NULL, 0, NULL, NULL, NULL, '2025-06-02 13:03:26', '2025-06-02 13:03:26'),
(4, 'wali_xi_ipa1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', NULL, NULL, 'Ahmad Rahman', NULL, 1, NULL, 0, NULL, NULL, NULL, '2025-06-02 13:03:26', '2025-06-02 13:03:26'),
(5, 'pamong_mp', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mp', 'mp', '[\"KPP\"]', 'Agustinus Kartono Pr', NULL, 1, '2025-06-02 17:02:29', 0, NULL, NULL, NULL, '2025-06-02 13:18:44', '2025-06-02 17:02:29'),
(6, 'pamong_mt', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mt', 'mt', '[\"X\"]', 'Pamong MT - Sari Dewi', NULL, 1, NULL, 0, NULL, NULL, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(7, 'pamong_mm', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mm', 'mm', '[\"XI\"]', 'Pamong MM - Ahmad Yusuf', NULL, 1, NULL, 0, NULL, NULL, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(8, 'pamong_mu', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mu', 'mu', '[\"XII\", \"KPA\"]', 'Pamong MU - Rina Sari', NULL, 1, NULL, 0, NULL, NULL, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44'),
(9, 'staff_tata_usaha', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'staff', NULL, NULL, 'Staff Tata Usaha - Indah Permata', NULL, 1, NULL, 0, NULL, NULL, NULL, '2025-06-02 13:18:44', '2025-06-02 13:18:44');

-- --------------------------------------------------------

--
-- Struktur dari tabel `user_kelas_mapping`
--

CREATE TABLE `user_kelas_mapping` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `kelas_id` int(11) NOT NULL,
  `role_type` enum('wali_kelas','guru_mapel','guru_piket') DEFAULT 'wali_kelas',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `user_management_log`
--

CREATE TABLE `user_management_log` (
  `id` int(11) NOT NULL,
  `admin_user_id` int(11) NOT NULL,
  `target_user_id` int(11) NOT NULL,
  `action_type` enum('create','update','delete','password_reset') NOT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `user_management_log`
--

INSERT INTO `user_management_log` (`id`, `admin_user_id`, `target_user_id`, `action_type`, `old_values`, `new_values`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 5, 'update', '{\"id\":\"5\",\"username\":\"pamong_mp\",\"email\":\"<EMAIL>\",\"password\":\"$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC\\/.og\\/at2.uheWG\\/igi\",\"role\":\"pamong_mp\",\"pamong_type\":\"mp\",\"tingkat_akses\":\"[\\\"KPP\\\"]\",\"nama_lengkap\":\"Pamong MP - Budi Hartono\",\"foto_profil\":null,\"is_active\":\"1\",\"last_login\":\"2025-06-02 22:57:15\",\"failed_login_attempts\":\"0\",\"locked_until\":null,\"password_reset_token\":null,\"password_reset_expires\":null,\"created_at\":\"2025-06-02 20:18:44\",\"updated_at\":\"2025-06-02 22:57:15\"}', '{\"username\":\"pamong_mp\",\"email\":\"<EMAIL>\",\"role\":\"pamong_mp\",\"nama_lengkap\":\"Agustinus Kartono Pr\",\"pamong_type\":null,\"is_active\":1}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 16:05:33');

-- --------------------------------------------------------

--
-- Stand-in struktur untuk tampilan `view_absensi_summary`
-- (Lihat di bawah untuk tampilan aktual)
--
CREATE TABLE `view_absensi_summary` (
`id_siswa` int(11)
,`nis` varchar(20)
,`nama_lengkap` varchar(100)
,`nama_kelas` varchar(20)
,`tingkat` varchar(10)
,`total_ketidakhadiran` bigint(21)
,`total_sakit` decimal(22,0)
,`total_ijin` decimal(22,0)
,`total_alpha` decimal(22,0)
,`ketidakhadiran_terakhir` date
);

-- --------------------------------------------------------

--
-- Struktur untuk view `view_absensi_summary`
--
DROP TABLE IF EXISTS `view_absensi_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `view_absensi_summary`  AS SELECT `s`.`id_siswa` AS `id_siswa`, `s`.`nis` AS `nis`, `s`.`nama_lengkap` AS `nama_lengkap`, `k`.`nama_kelas` AS `nama_kelas`, `k`.`tingkat` AS `tingkat`, count(`a`.`id`) AS `total_ketidakhadiran`, sum(case when `a`.`jenis_ketidakhadiran` = 'sakit' then 1 else 0 end) AS `total_sakit`, sum(case when `a`.`jenis_ketidakhadiran` = 'ijin' then 1 else 0 end) AS `total_ijin`, sum(case when `a`.`jenis_ketidakhadiran` = 'alpha' then 1 else 0 end) AS `total_alpha`, max(`a`.`tanggal`) AS `ketidakhadiran_terakhir` FROM ((`siswa` `s` left join `kelas` `k` on(`s`.`kelas_id` = `k`.`id`)) left join `absensi` `a` on(`s`.`id_siswa` = `a`.`siswa_id`)) WHERE `s`.`status_siswa` = 'aktif' GROUP BY `s`.`id_siswa`, `s`.`nis`, `s`.`nama_lengkap`, `k`.`nama_kelas`, `k`.`tingkat``tingkat`  ;

--
-- Indexes for dumped tables
--

--
-- Indeks untuk tabel `absensi`
--
ALTER TABLE `absensi`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_siswa_tanggal` (`siswa_id`,`tanggal`),
  ADD KEY `idx_siswa_tanggal` (`siswa_id`,`tanggal`),
  ADD KEY `idx_tanggal` (`tanggal`),
  ADD KEY `idx_jenis` (`jenis_ketidakhadiran`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_absensi_jenis_tanggal` (`jenis_ketidakhadiran`,`tanggal`);

--
-- Indeks untuk tabel `activity_log`
--
ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_table_record` (`table_name`,`record_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indeks untuk tabel `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_table` (`table_name`),
  ADD KEY `idx_created` (`created_at`);

--
-- Indeks untuk tabel `berkas`
--
ALTER TABLE `berkas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uploaded_by` (`uploaded_by`),
  ADD KEY `idx_siswa` (`siswa_id`),
  ADD KEY `idx_jenis` (`jenis_berkas`),
  ADD KEY `idx_hash` (`file_hash`);

--
-- Indeks untuk tabel `catatan_siswa`
--
ALTER TABLE `catatan_siswa`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_siswa_jenis` (`siswa_id`,`jenis_catatan`),
  ADD KEY `idx_tanggal` (`tanggal_catatan`),
  ADD KEY `idx_status` (`status_catatan`),
  ADD KEY `idx_prioritas` (`tingkat_prioritas`);

--
-- Indeks untuk tabel `csrf_tokens`
--
ALTER TABLE `csrf_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indeks untuk tabel `kategori_catatan`
--
ALTER TABLE `kategori_catatan`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `kode_kategori` (`kode_kategori`);

--
-- Indeks untuk tabel `kelas`
--
ALTER TABLE `kelas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_tahun` (`tahun_pelajaran`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_tingkat` (`tingkat`),
  ADD KEY `idx_wali_kelas_id` (`wali_kelas_id`),
  ADD KEY `idx_kelas_tingkat_active` (`tingkat`,`is_active`),
  ADD KEY `idx_kelas_kurikulum` (`kurikulum_id`);

--
-- Indeks untuk tabel `kurikulum`
--
ALTER TABLE `kurikulum`
  ADD PRIMARY KEY (`id_kurikulum`),
  ADD UNIQUE KEY `kode_kurikulum` (`kode_kurikulum`),
  ADD KEY `idx_kurikulum_active` (`is_active`);

--
-- Indeks untuk tabel `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- Indeks untuk tabel `siswa`
--
ALTER TABLE `siswa`
  ADD PRIMARY KEY (`id_siswa`),
  ADD UNIQUE KEY `nis` (`nis`),
  ADD UNIQUE KEY `nisn` (`nisn`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_nis` (`nis`),
  ADD KEY `idx_nama` (`nama_lengkap`),
  ADD KEY `idx_status` (`status_siswa`),
  ADD KEY `idx_kelas` (`kelas_id`),
  ADD KEY `idx_siswa_status_kelas` (`status_siswa`,`kelas_id`);

--
-- Indeks untuk tabel `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_users_role_pamong` (`role`,`pamong_type`);

--
-- Indeks untuk tabel `user_kelas_mapping`
--
ALTER TABLE `user_kelas_mapping`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_kelas_role` (`user_id`,`kelas_id`,`role_type`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_kelas_id` (`kelas_id`),
  ADD KEY `idx_role_type` (`role_type`);

--
-- Indeks untuk tabel `user_management_log`
--
ALTER TABLE `user_management_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_user` (`admin_user_id`),
  ADD KEY `idx_target_user` (`target_user_id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- AUTO_INCREMENT untuk tabel yang dibuang
--

--
-- AUTO_INCREMENT untuk tabel `absensi`
--
ALTER TABLE `absensi`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT untuk tabel `activity_log`
--
ALTER TABLE `activity_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT untuk tabel `audit_logs`
--
ALTER TABLE `audit_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT untuk tabel `berkas`
--
ALTER TABLE `berkas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT untuk tabel `catatan_siswa`
--
ALTER TABLE `catatan_siswa`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT untuk tabel `csrf_tokens`
--
ALTER TABLE `csrf_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT untuk tabel `kategori_catatan`
--
ALTER TABLE `kategori_catatan`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT untuk tabel `kelas`
--
ALTER TABLE `kelas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT untuk tabel `kurikulum`
--
ALTER TABLE `kurikulum`
  MODIFY `id_kurikulum` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT untuk tabel `siswa`
--
ALTER TABLE `siswa`
  MODIFY `id_siswa` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT untuk tabel `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT untuk tabel `user_kelas_mapping`
--
ALTER TABLE `user_kelas_mapping`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT untuk tabel `user_management_log`
--
ALTER TABLE `user_management_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Ketidakleluasaan untuk tabel pelimpahan (Dumped Tables)
--

--
-- Ketidakleluasaan untuk tabel `absensi`
--
ALTER TABLE `absensi`
  ADD CONSTRAINT `absensi_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id_siswa`) ON DELETE CASCADE,
  ADD CONSTRAINT `absensi_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Ketidakleluasaan untuk tabel `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD CONSTRAINT `audit_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Ketidakleluasaan untuk tabel `berkas`
--
ALTER TABLE `berkas`
  ADD CONSTRAINT `berkas_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id_siswa`) ON DELETE CASCADE,
  ADD CONSTRAINT `berkas_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`);

--
-- Ketidakleluasaan untuk tabel `csrf_tokens`
--
ALTER TABLE `csrf_tokens`
  ADD CONSTRAINT `csrf_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Ketidakleluasaan untuk tabel `kelas`
--
ALTER TABLE `kelas`
  ADD CONSTRAINT `fk_kelas_kurikulum` FOREIGN KEY (`kurikulum_id`) REFERENCES `kurikulum` (`id_kurikulum`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_kelas_wali_kelas` FOREIGN KEY (`wali_kelas_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `kelas_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `kelas_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`);

--
-- Ketidakleluasaan untuk tabel `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Ketidakleluasaan untuk tabel `siswa`
--
ALTER TABLE `siswa`
  ADD CONSTRAINT `siswa_ibfk_1` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`),
  ADD CONSTRAINT `siswa_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `siswa_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`);

--
-- Ketidakleluasaan untuk tabel `user_kelas_mapping`
--
ALTER TABLE `user_kelas_mapping`
  ADD CONSTRAINT `user_kelas_mapping_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_kelas_mapping_ibfk_2` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE;

--
-- Ketidakleluasaan untuk tabel `user_management_log`
--
ALTER TABLE `user_management_log`
  ADD CONSTRAINT `user_management_log_ibfk_1` FOREIGN KEY (`admin_user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `user_management_log_ibfk_2` FOREIGN KEY (`target_user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
