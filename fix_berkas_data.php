<?php
/**
 * Fix Berkas Data - Repair jenis_berkas and file paths
 */

require_once 'app/models/Database.php';
require_once 'app/models/Berkas.php';

echo "<h2>🔧 Fix Berkas Data</h2>";

try {
    $db = new Database();
    $berkasModel = new Berkas();
    
    // Get all berkas records
    $allBerkas = $db->fetchAll("SELECT * FROM berkas ORDER BY id");
    
    echo "<p>📁 Found " . count($allBerkas) . " berkas records to fix</p>";
    
    $fixed = 0;
    
    foreach ($allBerkas as $berkas) {
        $berkasId = $berkas['id'];
        $currentJenis = $berkas['jenis_berkas'];
        $namaFileSistem = $berkas['nama_file_sistem'];
        $currentPath = $berkas['file_path'];
        
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<strong>Berkas ID:</strong> $berkasId<br>";
        echo "<strong>File:</strong> $namaFileSistem<br>";
        echo "<strong>Current Jenis:</strong> <code>" . ($currentJenis ?: 'EMPTY') . "</code><br>";
        echo "<strong>Current Path:</strong> <code>$currentPath</code><br>";
        
        $needsUpdate = false;
        $newJenis = $currentJenis;
        $newPath = $currentPath;
        
        // Fix jenis_berkas based on filename
        if (empty($currentJenis)) {
            if (strpos($namaFileSistem, 'Kartu_Keluarga') !== false) {
                $newJenis = 'kartu_keluarga';
                $needsUpdate = true;
            } elseif (strpos($namaFileSistem, 'Akta') !== false) {
                $newJenis = 'akta_lahir';
                $needsUpdate = true;
            } elseif (strpos($namaFileSistem, 'Rapor_Kelas_X') !== false) {
                $newJenis = 'rapor_kelas_x';
                $needsUpdate = true;
            } elseif (strpos($namaFileSistem, 'Rapor_Kelas_XI') !== false) {
                $newJenis = 'rapor_kelas_xi';
                $needsUpdate = true;
            } elseif (strpos($namaFileSistem, 'Rapor_Kelas_XII') !== false) {
                $newJenis = 'rapor_kelas_xii';
                $needsUpdate = true;
            } elseif (strpos($namaFileSistem, 'Rapor') !== false) {
                $newJenis = 'rapor_kelas_x'; // default
                $needsUpdate = true;
            } elseif (strpos($namaFileSistem, 'Ijazah') !== false) {
                $newJenis = 'ijazah_sma';
                $needsUpdate = true;
            } else {
                $newJenis = 'lainnya';
                $needsUpdate = true;
            }
            
            echo "<strong>Detected Jenis:</strong> <code>$newJenis</code><br>";
        }
        
        // Fix file path
        if ($needsUpdate || strpos($currentPath, 'uploads/berkas/') === 0) {
            $newPath = $berkasModel->getFullFilePath($newJenis, $namaFileSistem);
            echo "<strong>New Path:</strong> <code>$newPath</code><br>";
            
            // Check if physical file exists
            $physicalPath = __DIR__ . '/public/' . $newPath;
            
            if (file_exists($physicalPath)) {
                echo "<span style='color: green;'>✅ Physical file exists</span><br>";
            } else {
                // Try to find file in different locations
                $possiblePaths = [
                    __DIR__ . '/public/uploads/berkas/rapor/' . $namaFileSistem,
                    __DIR__ . '/public/uploads/berkas/identitas/' . $namaFileSistem,
                    __DIR__ . '/public/uploads/berkas/ijazah/' . $namaFileSistem,
                    __DIR__ . '/public/uploads/berkas/lainnya/' . $namaFileSistem,
                    __DIR__ . '/uploads/berkas/rapor/' . $namaFileSistem,
                    __DIR__ . '/uploads/berkas/identitas/' . $namaFileSistem,
                ];
                
                $foundFile = false;
                foreach ($possiblePaths as $possiblePath) {
                    if (file_exists($possiblePath)) {
                        echo "<span style='color: orange;'>📁 Found file at: $possiblePath</span><br>";
                        
                        // Create target directory if not exists
                        $targetDir = dirname($physicalPath);
                        if (!is_dir($targetDir)) {
                            mkdir($targetDir, 0755, true);
                            echo "<span style='color: blue;'>📁 Created directory: $targetDir</span><br>";
                        }
                        
                        // Copy file to correct location
                        if (copy($possiblePath, $physicalPath)) {
                            echo "<span style='color: green;'>✅ File copied to correct location</span><br>";
                            $foundFile = true;
                        } else {
                            echo "<span style='color: red;'>❌ Failed to copy file</span><br>";
                        }
                        break;
                    }
                }
                
                if (!$foundFile) {
                    echo "<span style='color: red;'>❌ Physical file not found anywhere</span><br>";
                }
            }
            
            // Update database
            if ($needsUpdate) {
                $sql = "UPDATE berkas SET jenis_berkas = ?, file_path = ? WHERE id = ?";
                $db->query($sql, [$newJenis, $newPath, $berkasId]);
                echo "<span style='color: green;'>✅ Database updated</span><br>";
                $fixed++;
            }
        } else {
            echo "<span style='color: blue;'>ℹ️ No changes needed</span><br>";
        }
        
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<h3>📊 Fix Summary</h3>";
    echo "<p><strong>Total Records:</strong> " . count($allBerkas) . "</p>";
    echo "<p><strong>Fixed:</strong> <span style='color: green;'>$fixed</span></p>";
    
    if ($fixed > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Data Fix Completed!</h4>";
        echo "<p>Berkas data has been repaired and files moved to correct locations.</p>";
        echo "</div>";
    }
    
    // Test file access again
    echo "<h3>🔍 Test File Access After Fix</h3>";
    $testBerkas = $db->fetchAll("SELECT * FROM berkas WHERE file_path LIKE 'uploads/berkas/%' LIMIT 3");
    
    foreach ($testBerkas as $berkas) {
        $testUrl = "http://localhost/siswa-app/public/" . $berkas['file_path'];
        echo "<p><strong>Test URL:</strong> <a href='$testUrl' target='_blank'>" . basename($berkas['file_path']) . "</a></p>";
        
        // Test with curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            echo "<span style='color: green;'>✅ File accessible (HTTP 200)</span><br>";
        } else {
            echo "<span style='color: red;'>❌ File not accessible (HTTP $httpCode)</span><br>";
        }
        echo "<br>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='public/siswa/detail/57' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Detail Siswa</a> ";
echo "<a href='public/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Dashboard</a>";
echo "</p>";
?>
