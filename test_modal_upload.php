<?php
/**
 * Test Modal Upload Berkas
 */

require_once 'app/models/Database.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/User.php';
require_once 'app/models/Siswa.php';
require_once 'app/models/Berkas.php';

// Initialize session manager
$sessionManager = new SimpleSessionManager();
$userModel = new User();
$siswaModel = new Siswa();
$berkasModel = new Berkas();

echo "<h2>🔄 Test Modal Upload Berkas</h2>";

// Login sebagai admin
$result = $userModel->authenticate('admin', 'admin123');

if ($result['success']) {
    $sessionManager->loginUser($result['user']);
    echo "<p style='color: green;'>✅ Login berhasil sebagai: " . htmlspecialchars($result['user']['nama_lengkap']) . "</p>";
    
    // Get sample siswa
    $siswaList = $siswaModel->getAll();
    if (!empty($siswaList)) {
        $siswa = $siswaList[0];
        $siswaId = $siswa['id_siswa'] ?? $siswa['id'];
        
        echo "<p>📚 Testing dengan siswa: <strong>" . htmlspecialchars($siswa['nama_lengkap']) . "</strong> (ID: $siswaId)</p>";
        
        // Test 1: Check file categories
        echo "<h3>Test 1: File Categories</h3>";
        $fileCategories = $berkasModel->getFileCategories();
        echo "<p>✅ File categories loaded: " . count($fileCategories) . " categories</p>";
        
        foreach ($fileCategories as $categoryName => $types) {
            echo "<p><strong>$categoryName:</strong></p>";
            echo "<ul>";
            foreach ($types as $typeKey => $typeName) {
                echo "<li><code>$typeKey</code> → $typeName</li>";
            }
            echo "</ul>";
        }
        
        // Test 2: Check modal form structure
        echo "<h3>Test 2: Modal Form Structure</h3>";
        echo "<p>✅ Modal form should have:</p>";
        echo "<ul>";
        echo "<li>✅ Field name: <code>jenis_berkas</code> (select with proper options)</li>";
        echo "<li>✅ Field name: <code>file</code> (file input)</li>";
        echo "<li>✅ Field name: <code>keterangan</code> (textarea)</li>";
        echo "<li>✅ Field name: <code>csrf_token</code> (hidden)</li>";
        echo "</ul>";
        
        // Test 3: Check URLs
        echo "<h3>Test 3: URLs</h3>";
        $detailUrl = "http://localhost/siswa-app/public/siswa/detail/$siswaId";
        $uploadUrl = "http://localhost/siswa-app/public/upload/berkas/$siswaId";
        
        echo "<p><strong>Detail Page:</strong> <a href='$detailUrl' target='_blank'>$detailUrl</a></p>";
        echo "<p><strong>Upload Endpoint:</strong> $uploadUrl</p>";
        
        // Test 4: Check existing berkas
        echo "<h3>Test 4: Existing Berkas</h3>";
        $existingBerkas = $berkasModel->getBySiswaId($siswaId);
        echo "<p>📁 Existing files: " . count($existingBerkas) . " files</p>";
        
        if (!empty($existingBerkas)) {
            echo "<ul>";
            foreach (array_slice($existingBerkas, 0, 5) as $berkas) {
                echo "<li>";
                echo "<strong>" . htmlspecialchars($berkas['nama_berkas']) . "</strong> ";
                echo "(" . htmlspecialchars($berkas['jenis_berkas']) . ") ";
                echo "- " . number_format($berkas['ukuran_file'] / 1024, 1) . " KB";
                echo "</li>";
            }
            echo "</ul>";
            if (count($existingBerkas) > 5) {
                echo "<p><em>... dan " . (count($existingBerkas) - 5) . " file lainnya</em></p>";
            }
        }
        
        // Test 5: Simulate form data
        echo "<h3>Test 5: Simulate Form Data</h3>";
        $sampleFormData = [
            'jenis_berkas' => 'kartu_keluarga',
            'keterangan' => 'Test upload dari modal',
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        echo "<p>✅ Sample form data:</p>";
        echo "<pre>" . print_r($sampleFormData, true) . "</pre>";
        
        // Test 6: Check upload directory
        echo "<h3>Test 6: Upload Directory</h3>";
        $uploadDir = $berkasModel->getUploadDir('kartu_keluarga');
        echo "<p>📁 Upload directory: <code>$uploadDir</code></p>";
        
        if (is_dir($uploadDir)) {
            echo "<p style='color: green;'>✅ Directory exists and is writable: " . (is_writable($uploadDir) ? 'Yes' : 'No') . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Directory will be created on first upload</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Tidak ada data siswa untuk testing</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Login gagal: " . htmlspecialchars($result['message']) . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Kesimpulan</h3>";
echo "<p>Modal upload berkas sudah diperbaiki dengan:</p>";
echo "<ul>";
echo "<li>✅ Field name <code>file</code> (sesuai UploadController)</li>";
echo "<li>✅ Jenis berkas menggunakan kode yang benar dari Berkas model</li>";
echo "<li>✅ Redirect yang smart (kembali ke detail jika dari modal)</li>";
echo "<li>✅ Preview file yang responsif</li>";
echo "</ul>";

echo "<p><strong>Untuk testing manual:</strong></p>";
echo "<ol>";
echo "<li>Buka halaman detail siswa</li>";
echo "<li>Klik tombol 'Upload Berkas'</li>";
echo "<li>Pilih jenis berkas dari dropdown</li>";
echo "<li>Pilih file (PDF, JPG, PNG, DOC, DOCX max 5MB)</li>";
echo "<li>Tambahkan keterangan (opsional)</li>";
echo "<li>Klik 'Upload Berkas'</li>";
echo "<li>Halaman akan refresh dan menampilkan berkas baru</li>";
echo "</ol>";

echo "<hr>";
echo "<p><a href='public/'>Kembali ke Aplikasi</a></p>";
echo "<p><a href='simple_login.php'>Simple Login</a></p>";
?>
