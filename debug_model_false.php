<?php
/**
 * Debug Model False Return
 * Script ini akan debug mengapa model men<PERSON><PERSON><PERSON>an false
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/helpers/Security.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set up session if not exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user'] = ['id' => 1, 'username' => 'admin', 'role' => 'admin'];
}

echo "<h1>🔍 Debug Model False Return</h1>";

try {
    $db = new Database();
    $kelasModel = new Kelas();
    
    echo "<h2>📝 Test Data from Form</h2>";
    
    // Simulate the exact data from the form
    $formData = [
        'nama_kelas' => 'X-IPA-1',
        'tingkat' => 'X',
        'kurikulum' => '',  // Empty kurikulum
        'tahun_pelajaran' => '2024/2025',
        'wali_kelas' => 'Nama Wali Kelas',
        'kapasitas' => 30,
        'created_by' => 1
    ];
    
    echo "<h3>Form Data:</h3>";
    echo "<pre>" . print_r($formData, true) . "</pre>";
    
    echo "<h2>🔍 Step-by-Step Debug</h2>";
    
    // Step 1: Check for existing class
    echo "<h3>Step 1: Check for Existing Class</h3>";
    $existingClass = $db->fetch("
        SELECT id FROM kelas 
        WHERE nama_kelas = ? AND tahun_pelajaran = ? AND is_active = 1
    ", [$formData['nama_kelas'], $formData['tahun_pelajaran']]);
    
    if ($existingClass) {
        echo "<p style='color: red;'>❌ Class already exists with ID: {$existingClass['id']}</p>";
        echo "<p>This would cause the model to throw an exception, not return false.</p>";
    } else {
        echo "<p style='color: green;'>✅ No existing class found</p>";
    }
    
    // Step 2: Test SQL preparation
    echo "<h3>Step 2: Test SQL Preparation</h3>";
    
    $sql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $params = [
        $formData['nama_kelas'],
        $formData['tingkat'],
        $formData['kurikulum'] ?: null,  // Convert empty string to null
        $formData['tahun_pelajaran'],
        $formData['wali_kelas'] ?: null,
        (int)($formData['kapasitas']),
        $formData['created_by']
    ];
    
    echo "<p><strong>SQL:</strong></p>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    echo "<p><strong>Parameters:</strong></p>";
    echo "<pre>" . print_r($params, true) . "</pre>";
    
    // Step 3: Test direct database insert
    echo "<h3>Step 3: Test Direct Database Insert</h3>";
    
    try {
        $db->query($sql, $params);
        $insertId = $db->lastInsertId();
        
        echo "<p style='color: green;'>✅ Direct insert successful! Insert ID: {$insertId}</p>";
        
        // Verify the inserted data
        $insertedData = $db->fetch("SELECT * FROM kelas WHERE id = ?", [$insertId]);
        echo "<h4>Inserted Data:</h4>";
        echo "<pre>" . print_r($insertedData, true) . "</pre>";
        
        // Clean up
        $db->query("DELETE FROM kelas WHERE id = ?", [$insertId]);
        echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Direct insert failed: " . $e->getMessage() . "</p>";
        echo "<p>Error Code: " . $e->getCode() . "</p>";
    }
    
    // Step 4: Test model method with debugging
    echo "<h3>Step 4: Test Model Method with Debugging</h3>";
    
    // Let's look at the actual model code to see what might return false
    echo "<h4>Model Method Analysis:</h4>";
    
    // Test the model method
    try {
        echo "<p>Calling kelasModel->createKelas() with form data...</p>";
        
        $result = $kelasModel->createKelas($formData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Model method successful! Result: {$result}</p>";
            
            // Clean up
            $db->query("DELETE FROM kelas WHERE id = ?", [$result]);
            echo "<p style='color: blue;'>🧹 Model test data cleaned up</p>";
            
        } else {
            echo "<p style='color: red;'>❌ Model method returned false</p>";
            echo "<p>This indicates the model method completed without throwing an exception but returned false.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Model method threw exception: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>🔍 Model Code Analysis</h2>";
    
    // Let's examine what could cause the model to return false
    echo "<h3>Potential Causes for Model Returning False:</h3>";
    echo "<ol>";
    echo "<li><strong>Database query fails silently</strong> - PDO might not throw exception</li>";
    echo "<li><strong>lastInsertId() returns 0</strong> - Insert might succeed but ID not returned</li>";
    echo "<li><strong>Transaction rollback</strong> - If using transactions</li>";
    echo "<li><strong>Conditional logic</strong> - Some condition in model returns false</li>";
    echo "</ol>";
    
    // Test lastInsertId behavior
    echo "<h3>Test lastInsertId() Behavior:</h3>";
    
    try {
        // Insert a test record
        $testSql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $testParams = [
            'TEST-LASTID-' . date('His'),
            'X',
            'Kurikulum K13',
            '2024/2025',
            'Test Teacher',
            30,
            1
        ];
        
        $db->query($testSql, $testParams);
        $lastId = $db->lastInsertId();
        
        echo "<p><strong>lastInsertId() result:</strong> {$lastId}</p>";
        
        if ($lastId > 0) {
            echo "<p style='color: green;'>✅ lastInsertId() working correctly</p>";
            
            // Clean up
            $db->query("DELETE FROM kelas WHERE id = ?", [$lastId]);
            echo "<p style='color: blue;'>🧹 lastInsertId test data cleaned up</p>";
        } else {
            echo "<p style='color: red;'>❌ lastInsertId() returned 0 - this could be the issue!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ lastInsertId test failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🔧 Debugging the Actual Model</h2>";
    
    // Let's create a debug version of the model method
    echo "<h3>Debug Model Method:</h3>";
    
    try {
        echo "<p>Testing with debug logging...</p>";
        
        // Check for duplicate (same as model)
        $existingClass = $db->fetch("
            SELECT id FROM kelas 
            WHERE nama_kelas = ? AND tahun_pelajaran = ? AND is_active = 1
        ", [$formData['nama_kelas'], $formData['tahun_pelajaran']]);
        
        if ($existingClass) {
            echo "<p style='color: red;'>❌ Duplicate check failed - class exists</p>";
        } else {
            echo "<p style='color: green;'>✅ Duplicate check passed</p>";
            
            // Prepare SQL (same as model)
            $sql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $formData['nama_kelas'],
                $formData['tingkat'],
                $formData['kurikulum'] ?: null,
                $formData['tahun_pelajaran'],
                $formData['wali_kelas'] ?: null,
                (int)($formData['kapasitas'] ?? 30),
                $formData['created_by'] ?? 1
            ];
            
            echo "<p>Executing query...</p>";
            $queryResult = $db->query($sql, $params);
            echo "<p><strong>Query result:</strong> " . ($queryResult ? 'true' : 'false') . "</p>";
            
            $kelasId = $db->lastInsertId();
            echo "<p><strong>Last Insert ID:</strong> {$kelasId}</p>";
            
            if ($kelasId) {
                echo "<p style='color: green;'>✅ Insert successful, ID: {$kelasId}</p>";
                
                // Clean up
                $db->query("DELETE FROM kelas WHERE id = ?", [$kelasId]);
                echo "<p style='color: blue;'>🧹 Debug test data cleaned up</p>";
                
                echo "<p><strong>Conclusion:</strong> The model should work. There might be an issue with the actual form data or session.</p>";
            } else {
                echo "<p style='color: red;'>❌ lastInsertId() returned 0 - this is the problem!</p>";
                echo "<p>The insert might be succeeding but the ID is not being returned properly.</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Debug model test failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>💡 Recommendations</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔧 Possible Solutions:</h4>";
    echo "<ol>";
    echo "<li><strong>Check Auto Increment:</strong> Ensure the 'id' column has AUTO_INCREMENT</li>";
    echo "<li><strong>Check Table Engine:</strong> Ensure table uses InnoDB or MyISAM</li>";
    echo "<li><strong>Check Permissions:</strong> Ensure database user has INSERT privileges</li>";
    echo "<li><strong>Add Debug Logging:</strong> Add more logging to the model method</li>";
    echo "<li><strong>Check Form Data:</strong> Ensure all required fields are being sent</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔗 Try Form Again</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Debug Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
