<?php
/**
 * Test file untuk memverifikasi fitur "Tambah Siswa" di halaman Data Kelas
 */

// Include necessary files
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/models/Siswa.php';

echo "<h1>🧪 Test Fitur Tambah Siswa di Halaman Data Kelas</h1>";

try {
    // Initialize models
    $kelasModel = new Kelas();
    $siswaModel = new Siswa();
    
    // Get current academic year
    require_once __DIR__ . '/app/helpers/AcademicYear.php';
    $selectedYear = AcademicYear::getSelectedAcademicYear();
    
    echo "<h2>📋 Informasi Sistem</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Tahun Ajaran Aktif:</strong> {$selectedYear}</p>";
    echo "</div>";
    
    // Get all classes
    $allClasses = $kelasModel->getAll($selectedYear);
    
    if (empty($allClasses)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>⚠️ Peringatan:</strong> Tidak ada kelas yang ditemukan untuk tahun ajaran {$selectedYear}</p>";
        echo "<p>Silakan tambah kelas terlebih dahulu sebelum menguji fitur ini.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>🏫 Daftar Kelas yang Tersedia</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Total Kelas:</strong> " . count($allClasses) . "</p>";
    echo "</div>";
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
    echo "<thead style='background: #343a40; color: white;'>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>Nama Kelas</th>";
    echo "<th>Tingkat</th>";
    echo "<th>Tahun Pelajaran</th>";
    echo "<th>Jumlah Siswa</th>";
    echo "<th>Test Links</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($allClasses as $kelas) {
        // Get student count for this class
        $siswaList = $siswaModel->getByKelasId($kelas['id_kelas']);
        $jumlahSiswa = count($siswaList);
        
        echo "<tr>";
        echo "<td>{$kelas['id_kelas']}</td>";
        echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
        echo "<td>{$kelas['tingkat']}</td>";
        echo "<td>{$kelas['tahun_pelajaran']}</td>";
        echo "<td><span style='background: #007bff; color: white; padding: 2px 8px; border-radius: 3px;'>{$jumlahSiswa} siswa</span></td>";
        echo "<td>";
        
        // Test links
        $kelasId = $kelas['id_kelas'];
        echo "<a href='/siswa-app/public/siswa/create?kelas_id={$kelasId}' target='_blank' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px;'>+ Tambah Siswa</a>";
        echo "<a href='/siswa-app/public/kelas/detail/{$kelasId}' target='_blank' style='background: #17a2b8; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px;'>Detail Kelas</a>";
        echo "<a href='/siswa-app/public/kelas' target='_blank' style='background: #6c757d; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px;'>List Kelas</a>";
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    echo "<h2>✅ Fitur yang Telah Ditambahkan</h2>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>1. Tombol 'Tambah Siswa' di Header Halaman List Kelas</h3>";
    echo "<p>• Lokasi: <code>app/views/kelas/list.php</code> baris 17-19</p>";
    echo "<p>• Fungsi: Mengarahkan ke form tambah siswa umum</p>";
    echo "<p>• URL: <code>/siswa-app/public/siswa/create</code></p>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>2. Tombol 'Tambah Siswa' di Setiap Baris Kelas</h3>";
    echo "<p>• Lokasi: <code>app/views/kelas/list.php</code> baris 98-101</p>";
    echo "<p>• Fungsi: Mengarahkan ke form tambah siswa dengan kelas sudah terpilih</p>";
    echo "<p>• URL: <code>/siswa-app/public/siswa/create?kelas_id={id_kelas}</code></p>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>3. Auto-Select Kelas di Form Tambah Siswa</h3>";
    echo "<p>• Lokasi: <code>app/controllers/SiswaController.php</code> baris 126-127</p>";
    echo "<p>• Fungsi: Membaca parameter kelas_id dari URL dan meneruskan ke view</p>";
    echo "<p>• Lokasi View: <code>app/views/siswa/create.php</code> baris 263-264</p>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>4. Notifikasi Kelas Terpilih</h3>";
    echo "<p>• Lokasi: <code>app/views/siswa/create.php</code> baris 25-45</p>";
    echo "<p>• Fungsi: Menampilkan alert info tentang kelas yang akan ditambahi siswa</p>";
    echo "</div>";
    
    echo "<h2>🔗 Test Manual</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Langkah-langkah test:</strong></p>";
    echo "<ol>";
    echo "<li>Klik link 'List Kelas' di atas untuk membuka halaman daftar kelas</li>";
    echo "<li>Perhatikan ada tombol 'Tambah Siswa' di header (warna hijau)</li>";
    echo "<li>Perhatikan ada tombol 'Tambah Siswa' (ikon person-plus) di setiap baris kelas</li>";
    echo "<li>Klik tombol 'Tambah Siswa' di salah satu baris kelas</li>";
    echo "<li>Verifikasi bahwa form terbuka dengan kelas sudah terpilih</li>";
    echo "<li>Verifikasi ada notifikasi biru yang menunjukkan kelas yang dipilih</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
