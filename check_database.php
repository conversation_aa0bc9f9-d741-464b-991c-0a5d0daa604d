<?php
/**
 * Check Database Direct
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=siswa_app', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔍 Check Database Direct</h2>";
    
    // Check current data
    $stmt = $pdo->prepare("SELECT id, jenis_berkas, nama_file_sistem FROM berkas WHERE siswa_id = 57 ORDER BY id");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Data:</h3>";
    foreach ($results as $row) {
        echo "<p><strong>ID " . $row['id'] . ":</strong> ";
        echo "jenis_berkas = '" . ($row['jenis_berkas'] ?: 'NULL/EMPTY') . "' - ";
        echo htmlspecialchars($row['nama_file_sistem']);
        echo "</p>";
    }
    
    echo "<hr>";
    echo "<h3>Manual Update:</h3>";
    
    // Manual updates
    $updates = [
        16 => 'rapor_kelas_x',
        17 => 'rapor_kelas_xi', 
        18 => 'rapor_kelas_xii'
    ];
    
    foreach ($updates as $id => $jenis) {
        $stmt = $pdo->prepare("UPDATE berkas SET jenis_berkas = ? WHERE id = ?");
        $result = $stmt->execute([$jenis, $id]);
        echo "<p>✅ Updated ID $id to '$jenis' - Result: " . ($result ? 'SUCCESS' : 'FAILED') . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>After Update:</h3>";
    
    // Check after update
    $stmt = $pdo->prepare("SELECT id, jenis_berkas, nama_file_sistem FROM berkas WHERE siswa_id = 57 ORDER BY id");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($results as $row) {
        echo "<p><strong>ID " . $row['id'] . ":</strong> ";
        echo "jenis_berkas = '" . ($row['jenis_berkas'] ?: 'NULL/EMPTY') . "' - ";
        echo htmlspecialchars($row['nama_file_sistem']);
        echo "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='test_berkas_grouped.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Grouped Display</a>";
echo "</p>";
?>
