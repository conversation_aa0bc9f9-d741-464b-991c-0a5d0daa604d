<?php
/**
 * Script Test Final Sinkronisasi Role dan <PERSON>
 * 
 * Verifikasi bahwa sinkronisasi role dan kelas sudah berfungsi dengan benar
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/User.php';
require_once __DIR__ . '/app/models/Kelas.php';

echo "<h1>🧪 Test Final Sinkronisasi Role dan <PERSON></h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    $userModel = new User();
    $kelasModel = new Kelas();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Test yang Akan Di<PERSON>ukan</h3>";
    echo "<ul>";
    echo "<li>Verifikasi sinkronisasi role user dengan kelas yang dikelola</li>";
    echo "<li>Test method getUsersByRole() untuk wali kelas</li>";
    echo "<li>Test form kelas dengan dropdown wali kelas yang benar</li>";
    echo "<li>Verifikasi data konsisten antara role dan assignment</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 1: Verifikasi sinkronisasi role dan kelas
    echo "<h2>🔄 Test 1: Sinkronisasi Role dan Kelas</h2>";
    
    $stmt = $pdo->query("
        SELECT k.id, k.nama_kelas, k.tingkat, k.wali_kelas, k.wali_kelas_id,
               u.username, u.nama_lengkap, u.role
        FROM kelas k
        LEFT JOIN users u ON k.wali_kelas_id = u.id
        WHERE k.is_active = 1
        ORDER BY k.tingkat, k.nama_kelas
    ");
    $kelasData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Kelas</th><th>Wali Kelas (Text)</th><th>User Assigned</th><th>Role User</th><th>Expected Role</th><th>Status</th></tr>";
    echo "</thead><tbody>";
    
    $totalSinkron = 0;
    $totalKelas = count($kelasData);
    
    foreach ($kelasData as $kelas) {
        $expectedRole = 'wali_kelas_' . strtolower(str_replace('-', '_', $kelas['nama_kelas']));
        $status = '✅ Sinkron';
        $statusColor = 'green';
        
        if (!$kelas['wali_kelas_id'] || !$kelas['username']) {
            $status = '❌ Belum ada wali kelas';
            $statusColor = 'red';
        } elseif ($kelas['role'] !== $expectedRole) {
            $status = '⚠️ Role tidak sesuai';
            $statusColor = 'orange';
        } elseif ($kelas['wali_kelas'] !== $kelas['nama_lengkap']) {
            $status = '⚠️ Nama tidak sinkron';
            $statusColor = 'orange';
        } else {
            $totalSinkron++;
        }
        
        echo "<tr>";
        echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
        echo "<td>{$kelas['wali_kelas']}</td>";
        echo "<td>{$kelas['nama_lengkap']} ({$kelas['username']})</td>";
        echo "<td><code>{$kelas['role']}</code></td>";
        echo "<td><code>{$expectedRole}</code></td>";
        echo "<td style='color: {$statusColor};'>{$status}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    $sinkronPercentage = $totalKelas > 0 ? ($totalSinkron / $totalKelas) * 100 : 0;
    
    echo "<p><strong>Ringkasan:</strong></p>";
    echo "<ul>";
    echo "<li>Total kelas: {$totalKelas}</li>";
    echo "<li>Kelas sinkron: {$totalSinkron}</li>";
    echo "<li>Persentase sinkronisasi: " . round($sinkronPercentage, 1) . "%</li>";
    echo "</ul>";
    
    // Test 2: Test method getUsersByRole()
    echo "<h2>👥 Test 2: Method getUsersByRole()</h2>";
    
    $waliKelasList = $userModel->getUsersByRole('wali_kelas');
    
    echo "<p><strong>Total user wali kelas:</strong> " . count($waliKelasList) . "</p>";
    
    if (!empty($waliKelasList)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Kelas yang Dikelola</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasList as $user) {
            // Cek kelas yang dikelola
            $stmt = $pdo->prepare("SELECT nama_kelas FROM kelas WHERE wali_kelas_id = ? AND is_active = 1");
            $stmt->execute([$user['id']]);
            $kelasYangDikelola = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $status = '✅ OK';
            $statusColor = 'green';
            
            if (empty($kelasYangDikelola)) {
                $status = '⚠️ Tidak ada kelas';
                $statusColor = 'orange';
            } elseif (strpos($user['role'], 'wali_kelas_') === 0) {
                // Cek apakah role sesuai dengan kelas
                $expectedClass = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $user['role']));
                if (!in_array($expectedClass, $kelasYangDikelola)) {
                    $status = '⚠️ Kelas tidak sesuai role';
                    $statusColor = 'orange';
                }
            }
            
            echo "<tr>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><code>{$user['role']}</code></td>";
            echo "<td>" . (empty($kelasYangDikelola) ? '<em>Tidak ada</em>' : implode(', ', $kelasYangDikelola)) . "</td>";
            echo "<td style='color: {$statusColor};'>{$status}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Tidak ada user wali kelas ditemukan</p>";
    }
    
    // Test 3: Test dropdown form kelas
    echo "<h2>📝 Test 3: Dropdown Form Kelas</h2>";
    
    echo "<h4>Simulasi Dropdown Wali Kelas:</h4>";
    echo "<select class='form-select' style='width: 100%; padding: 8px; margin: 10px 0;'>";
    echo "<option value=''>Pilih Wali Kelas</option>";
    
    foreach ($waliKelasList as $wali) {
        $roleInfo = '';
        if (strpos($wali['role'], 'wali_kelas_') === 0) {
            $roleInfo = ' - ' . strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $wali['role']));
        }
        
        echo "<option value='{$wali['id']}'>";
        echo htmlspecialchars($wali['nama_lengkap']) . " (" . htmlspecialchars($wali['username']) . ")" . $roleInfo;
        echo "</option>";
    }
    echo "</select>";
    
    echo "<p><strong>Analisis dropdown:</strong></p>";
    echo "<ul>";
    echo "<li>Total options: " . count($waliKelasList) . "</li>";
    echo "<li>Role spesifik ditampilkan: " . (strpos(json_encode($waliKelasList), 'wali_kelas_') !== false ? 'Ya' : 'Tidak') . "</li>";
    echo "<li>Urutan: Role umum dulu, kemudian role spesifik</li>";
    echo "</ul>";
    
    // Test 4: Test konsistensi data
    echo "<h2>🔍 Test 4: Konsistensi Data</h2>";
    
    $issues = [];
    
    // Cek kelas tanpa wali kelas
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM kelas WHERE wali_kelas_id IS NULL AND is_active = 1");
    $kelasWithoutWali = $stmt->fetchColumn();
    
    if ($kelasWithoutWali > 0) {
        $issues[] = "{$kelasWithoutWali} kelas belum memiliki wali kelas";
    }
    
    // Cek user wali kelas tanpa kelas
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u
        WHERE u.role LIKE 'wali_kelas%'
        AND u.id NOT IN (SELECT wali_kelas_id FROM kelas WHERE wali_kelas_id IS NOT NULL AND is_active = 1)
    ");
    $userWithoutKelas = $stmt->fetchColumn();
    
    if ($userWithoutKelas > 0) {
        $issues[] = "{$userWithoutKelas} user wali kelas tidak mengelola kelas apapun";
    }
    
    // Cek role yang tidak sesuai dengan kelas
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u
        JOIN kelas k ON u.id = k.wali_kelas_id
        WHERE u.role LIKE 'wali_kelas_%' 
        AND u.role != CONCAT('wali_kelas_', LOWER(REPLACE(k.nama_kelas, '-', '_')))
    ");
    $roleMismatch = $stmt->fetchColumn();
    
    if ($roleMismatch > 0) {
        $issues[] = "{$roleMismatch} user memiliki role yang tidak sesuai dengan kelas yang dikelola";
    }
    
    // Cek nama wali kelas yang tidak sinkron
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM kelas k
        JOIN users u ON k.wali_kelas_id = u.id
        WHERE k.wali_kelas != u.nama_lengkap
    ");
    $nameMismatch = $stmt->fetchColumn();
    
    if ($nameMismatch > 0) {
        $issues[] = "{$nameMismatch} kelas memiliki nama wali kelas yang tidak sinkron dengan user";
    }
    
    if (empty($issues)) {
        echo "<p style='color: green; font-size: 1.2em;'><strong>✅ SEMUA DATA KONSISTEN!</strong></p>";
        echo "<p>Tidak ada masalah konsistensi yang ditemukan.</p>";
    } else {
        echo "<p style='color: red; font-size: 1.2em;'><strong>❌ DITEMUKAN MASALAH KONSISTENSI</strong></p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li style='color: red;'>{$issue}</li>";
        }
        echo "</ul>";
    }
    
    // Test 5: Ringkasan dan rekomendasi
    echo "<h2>📋 Test 5: Ringkasan dan Rekomendasi</h2>";
    
    $overallScore = 0;
    $maxScore = 4;
    
    // Scoring
    if ($sinkronPercentage >= 90) $overallScore++;
    if (count($waliKelasList) > 0) $overallScore++;
    if (empty($issues)) $overallScore += 2;
    
    $overallPercentage = ($overallScore / $maxScore) * 100;
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Skor Keseluruhan:</h4>";
    echo "<ul>";
    echo "<li><strong>Sinkronisasi Role-Kelas:</strong> " . round($sinkronPercentage, 1) . "%</li>";
    echo "<li><strong>User Wali Kelas:</strong> " . count($waliKelasList) . " user</li>";
    echo "<li><strong>Konsistensi Data:</strong> " . (empty($issues) ? 'Baik' : count($issues) . ' masalah') . "</li>";
    echo "<li><strong>Skor Keseluruhan:</strong> {$overallScore}/{$maxScore} (" . round($overallPercentage, 1) . "%)</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($overallPercentage >= 80) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 SINKRONISASI BERHASIL!</h3>";
        echo "<p>Sistem role dan kelas sudah berfungsi dengan baik.</p>";
        echo "<ul>";
        echo "<li>✅ Role user sudah sesuai dengan kelas yang dikelola</li>";
        echo "<li>✅ Form kelas sudah menampilkan dropdown wali kelas yang benar</li>";
        echo "<li>✅ Data konsisten antara role dan assignment</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ MASIH ADA MASALAH</h3>";
        echo "<p>Sistem masih memerlukan perbaikan.</p>";
        echo "<p><strong>Langkah yang disarankan:</strong></p>";
        echo "<ol>";
        echo "<li>Jalankan script fix_role_kelas_sync.php</li>";
        echo "<li>Verifikasi assignment wali kelas secara manual</li>";
        echo "<li>Test form create/edit kelas</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    // Test manual links
    echo "<h2>🔗 Test Manual</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Link untuk Test Manual:</h4>";
    echo "<ul>";
    echo "<li><a href='/siswa-app/public/admin/users' target='_blank'>Manajemen User</a> - Cek role user wali kelas</li>";
    echo "<li><a href='/siswa-app/public/kelas' target='_blank'>Manajemen Kelas</a> - Cek assignment wali kelas</li>";
    echo "<li><a href='/siswa-app/public/kelas/create' target='_blank'>Create Kelas</a> - Test dropdown wali kelas</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test final selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
