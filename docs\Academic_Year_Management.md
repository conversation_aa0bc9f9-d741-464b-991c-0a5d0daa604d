# Manajemen Tahun Ajaran - Dokumentasi Lengkap

## 📋 Deskripsi
Fitur Manajemen Tahun Ajaran memungkinkan administrator dan staff untuk mengelola tahun ajaran dalam sistem, termasuk menambahkan tahun-tahun sebelumnya, tahun mendata<PERSON>, dan mengelola data historis.

## 🎯 Fitur Utama

### 1. **Tambah Tahun Ajaran Manual**
- Menambahkan tahun ajaran satu per satu
- Format: YYYY/YYYY (contoh: 2023/2024)
- Validasi format otomatis
- Pencegahan duplikasi

### 2. **Generate Tahun Ajaran Bulk**
- Membuat multiple tahun ajaran sekaligus
- Tentukan tahun mulai dan tahun akhir
- Maksimal 20 tahun ajaran per operasi
- Otomatis skip tahun yang sudah ada

### 3. **Statistik Tahun Ajaran**
- Jumlah siswa per tahun ajaran
- Jumlah kelas aktif dan total kelas
- Status tahun ajaran (Berjalan/Berisi Data/Kosong)
- Indikator visual yang jelas

### 4. **<PERSON>pus Tahun Ajaran**
- <PERSON><PERSON> tahun ajaran yang kosong
- Proteksi untuk tahun berjalan
- Proteksi untuk tahun yang berisi data
- Konfirmasi sebelum penghapusan

### 5. **Navigasi Cepat**
- Link langsung ke daftar siswa per tahun
- Link langsung ke daftar kelas per tahun
- Integrasi dengan Academic Year Selector

## 🔐 Hak Akses

### **Admin**
- ✅ Lihat semua tahun ajaran
- ✅ Tambah tahun ajaran manual
- ✅ Generate tahun ajaran bulk
- ✅ Hapus tahun ajaran kosong
- ✅ Akses semua statistik

### **Staff**
- ✅ Lihat semua tahun ajaran
- ✅ Tambah tahun ajaran manual
- ❌ Generate tahun ajaran bulk
- ❌ Hapus tahun ajaran
- ✅ Akses semua statistik

### **Guru/User Biasa**
- ❌ Tidak ada akses ke manajemen tahun ajaran
- ✅ Dapat menggunakan Academic Year Selector

## 🛠️ Implementasi Teknis

### **1. Controller**
**File:** `app/controllers/AcademicYearManagementController.php`

**Methods:**
- `index()` - Halaman utama manajemen
- `add()` - Tambah tahun ajaran manual
- `generateYears()` - Generate bulk tahun ajaran
- `delete()` - Hapus tahun ajaran
- `getYearStatistics()` - Ambil statistik tahun ajaran

### **2. View**
**File:** `app/views/academic_year/management.php`

**Komponen:**
- Tabel daftar tahun ajaran dengan statistik
- Modal form tambah tahun ajaran
- Modal form generate bulk
- Modal konfirmasi hapus
- Alert messages untuk feedback

### **3. Routing**
**Base URL:** `/academic-year-management`

**Endpoints:**
- `GET /academic-year-management` - Halaman utama
- `POST /academic-year-management/add` - Tambah tahun ajaran
- `POST /academic-year-management/generate` - Generate bulk
- `POST /academic-year-management/delete` - Hapus tahun ajaran

### **4. Database Integration**
Sistem menggunakan tabel `kelas` untuk menyimpan informasi tahun ajaran:
- Tahun ajaran diambil dari field `tahun_pelajaran`
- Placeholder entries dibuat untuk tahun kosong
- Statistik dihitung dari relasi siswa-kelas

## 📊 Cara Kerja Sistem

### **1. Penyimpanan Tahun Ajaran**
```sql
-- Placeholder entry untuk tahun ajaran kosong
INSERT INTO kelas (
    nama_kelas, 
    tingkat, 
    tahun_pelajaran, 
    wali_kelas, 
    kapasitas, 
    is_active, 
    created_by
) VALUES (
    'PLACEHOLDER_2023_2024',  -- Nama unik
    0,                        -- Tingkat khusus untuk placeholder
    '2023/2024',             -- Tahun ajaran
    'System Generated',       -- Wali kelas placeholder
    0,                       -- Kapasitas 0
    0,                       -- Tidak aktif
    user_id                  -- ID user yang membuat
);
```

### **2. Pengambilan Tahun Tersedia**
```php
// Dari AcademicYear helper
$availableYears = AcademicYear::getAvailableAcademicYears();
// Query: SELECT DISTINCT tahun_pelajaran FROM kelas WHERE tahun_pelajaran IS NOT NULL
```

### **3. Perhitungan Statistik**
```php
// Siswa per tahun ajaran
SELECT COUNT(*) FROM siswa s 
JOIN kelas k ON s.kelas_id = k.id 
WHERE k.tahun_pelajaran = ?

// Kelas aktif per tahun ajaran
SELECT COUNT(*) FROM kelas 
WHERE tahun_pelajaran = ? AND is_active = 1 AND tingkat > 0
```

## 🎨 Interface Pengguna

### **1. Halaman Utama**
```
┌─────────────────────────────────────────────────────────────┐
│ 📅 Manajemen Tahun Ajaran                    [+ Tambah] [⚙️ Generate] │
├─────────────────────────────────────────────────────────────┤
│ 📊 Tahun Ajaran Saat Ini: 2024/2025                        │
├─────────────────────────────────────────────────────────────┤
│ Tahun Ajaran │ Status    │ Siswa │ Kelas Aktif │ Total │ Aksi │
│ 2024/2025   │ 🟢 Berjalan │  150  │     8      │   8   │ 👁️📚  │
│ 2023/2024   │ 🔵 Berisi   │  145  │     0      │   8   │ 👁️📚  │
│ 2022/2023   │ ⚪ Kosong   │   0   │     0      │   0   │ 👁️📚🗑️ │
└─────────────────────────────────────────────────────────────┘
```

### **2. Modal Tambah Tahun Ajaran**
```
┌─────────────────────────────────────┐
│ ➕ Tambah Tahun Ajaran              │
├─────────────────────────────────────┤
│ Tahun Ajaran: [2023/2024____]      │
│ Format: YYYY/YYYY (contoh: 2023/2024) │
│                                     │
│ ℹ️ Tahun ajaran akan tersedia untuk │
│   pembuatan kelas dan siswa         │
│                                     │
│           [Batal] [➕ Tambah]       │
└─────────────────────────────────────┘
```

### **3. Modal Generate Bulk**
```
┌─────────────────────────────────────┐
│ ⚙️ Generate Tahun Ajaran Bulk       │
├─────────────────────────────────────┤
│ Tahun Mulai:  [2020____]           │
│ Tahun Akhir:  [2024____]           │
│                                     │
│ ⚠️ Akan membuat:                    │
│ • 2020/2021                        │
│ • 2021/2022                        │
│ • 2022/2023                        │
│ • 2023/2024                        │
│ • 2024/2025                        │
│                                     │
│           [Batal] [⚙️ Generate]     │
└─────────────────────────────────────┘
```

## 📝 Panduan Penggunaan

### **1. Menambah Tahun Ajaran Manual**
1. Login sebagai Admin atau Staff
2. Buka menu **Manajemen → Manajemen Tahun Ajaran**
3. Klik tombol **"Tambah Tahun Ajaran"**
4. Masukkan tahun ajaran dengan format YYYY/YYYY
5. Klik **"Tambah"**

### **2. Generate Tahun Ajaran Bulk (Admin Only)**
1. Login sebagai Admin
2. Buka halaman Manajemen Tahun Ajaran
3. Klik tombol **"Generate Bulk"**
4. Tentukan tahun mulai dan tahun akhir
5. Klik **"Generate"**

### **3. Melihat Statistik**
- Statistik ditampilkan otomatis di tabel utama
- Klik ikon mata (👁️) untuk melihat daftar siswa
- Klik ikon buku (📚) untuk melihat daftar kelas

### **4. Menghapus Tahun Ajaran (Admin Only)**
1. Pastikan tahun ajaran kosong (tidak ada siswa/kelas aktif)
2. Klik ikon hapus (🗑️) pada tahun yang ingin dihapus
3. Konfirmasi penghapusan

## ⚠️ Batasan dan Keamanan

### **Batasan Sistem**
- Maksimal 20 tahun ajaran per generate bulk
- Tidak dapat menghapus tahun ajaran yang sedang berjalan
- Tidak dapat menghapus tahun ajaran yang berisi data
- Format tahun ajaran harus YYYY/YYYY

### **Keamanan**
- CSRF token protection untuk semua operasi
- Role-based access control
- Validasi input yang ketat
- Logging untuk audit trail

## 🔧 Troubleshooting

### **Problem: Tahun ajaran tidak muncul di dropdown**
**Solusi:** Pastikan ada entry di tabel `kelas` dengan `tahun_pelajaran` yang sesuai

### **Problem: Tidak bisa menghapus tahun ajaran**
**Solusi:** Periksa apakah tahun tersebut memiliki data siswa atau kelas aktif

### **Problem: Generate bulk tidak berfungsi**
**Solusi:** Pastikan login sebagai Admin dan tahun mulai ≤ tahun akhir

### **Problem: Statistik tidak akurat**
**Solusi:** Periksa relasi antara tabel `siswa` dan `kelas`

## 🚀 Pengembangan Lanjutan

### **Fitur yang Bisa Ditambahkan**
1. **Export/Import Tahun Ajaran** - Backup dan restore data
2. **Template Kelas** - Copy struktur kelas dari tahun sebelumnya
3. **Migrasi Siswa** - Pindahkan siswa antar tahun ajaran
4. **Laporan Historis** - Analisis tren multi-tahun
5. **Notifikasi Otomatis** - Reminder pergantian tahun ajaran

### **Optimisasi Performance**
1. **Caching** - Cache statistik tahun ajaran
2. **Indexing** - Index pada field `tahun_pelajaran`
3. **Pagination** - Untuk sistem dengan banyak tahun ajaran
4. **Background Jobs** - Generate bulk di background

## ✅ Kesimpulan

Fitur Manajemen Tahun Ajaran memberikan kontrol penuh kepada administrator untuk mengelola data historis dan perencanaan tahun ajaran. Dengan interface yang intuitif dan keamanan yang ketat, fitur ini memungkinkan sekolah untuk:

- ✅ Mengelola data multi-tahun dengan mudah
- ✅ Melihat statistik dan tren historis
- ✅ Merencanakan tahun ajaran mendatang
- ✅ Menjaga integritas data dengan validasi ketat
- ✅ Memberikan akses yang sesuai berdasarkan role pengguna

Sistem ini siap digunakan dan dapat dikembangkan lebih lanjut sesuai kebutuhan sekolah.
