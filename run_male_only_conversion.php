<?php
/**
 * Script untuk mengkonversi sistem ke sekolah khusus laki-laki
 * 
 * PERINGATAN: Script ini akan mengubah struktur database dan data siswa!
 * Pastikan sudah backup database sebelum menjalankan script ini.
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';

echo "<h1>🔧 Konversi ke Sekolah Khusus Laki-laki</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ PERINGATAN PENTING</h3>";
    echo "<p>Script ini akan:</p>";
    echo "<ul>";
    echo "<li>Mengubah struktur tabel siswa</li>";
    echo "<li>Mengubah field jenis_kelamin menjadi ENUM('L') saja</li>";
    echo "<li>Mengupdate semua siswa perempuan menjadi laki-laki (atau menghapusnya)</li>";
    echo "</ul>";
    echo "<p><strong>Pastikan Anda sudah backup database!</strong></p>";
    echo "</div>";
    
    // Step 1: Check current data
    echo "<h2>📊 Langkah 1: Cek Data Saat Ini</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM siswa");
    $totalSiswa = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM siswa WHERE jenis_kelamin = 'L'");
    $totalLaki = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM siswa WHERE jenis_kelamin = 'P'");
    $totalPerempuan = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Total Siswa:</strong> {$totalSiswa}</p>";
    echo "<p><strong>Siswa Laki-laki:</strong> {$totalLaki}</p>";
    echo "<p><strong>Siswa Perempuan:</strong> {$totalPerempuan}</p>";
    echo "</div>";
    
    if ($totalPerempuan > 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🚨 Ditemukan {$totalPerempuan} siswa perempuan!</h4>";
        echo "<p>Pilih salah satu opsi:</p>";
        echo "<ol>";
        echo "<li><strong>Ubah menjadi laki-laki</strong> - Data siswa tetap ada tapi jenis kelamin diubah</li>";
        echo "<li><strong>Hapus data siswa perempuan</strong> - Data siswa perempuan akan dihapus permanen</li>";
        echo "</ol>";
        echo "</div>";
        
        // Get list of female students
        $stmt = $pdo->query("SELECT nama_lengkap, nis FROM siswa WHERE jenis_kelamin = 'P' LIMIT 10");
        $siswaPerempuan = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($siswaPerempuan)) {
            echo "<h4>Contoh siswa perempuan yang akan terpengaruh:</h4>";
            echo "<ul>";
            foreach ($siswaPerempuan as $siswa) {
                echo "<li>{$siswa['nama_lengkap']} (NIS: {$siswa['nis']})</li>";
            }
            if ($totalPerempuan > 10) {
                echo "<li><em>... dan " . ($totalPerempuan - 10) . " siswa lainnya</em></li>";
            }
            echo "</ul>";
        }
    }
    
    // Step 2: User confirmation (in real scenario, this would be interactive)
    echo "<h2>🔄 Langkah 2: Eksekusi Konversi</h2>";
    
    // For demo purposes, we'll convert (not delete) female students
    $conversionMode = 'convert'; // 'convert' or 'delete'
    
    if ($totalPerempuan > 0) {
        if ($conversionMode === 'convert') {
            echo "<p>🔄 Mengubah semua siswa perempuan menjadi laki-laki...</p>";
            $stmt = $pdo->prepare("UPDATE siswa SET jenis_kelamin = 'L' WHERE jenis_kelamin = 'P'");
            $stmt->execute();
            echo "<p style='color: green;'>✅ Berhasil mengubah {$totalPerempuan} siswa perempuan menjadi laki-laki</p>";
        } else {
            echo "<p>🗑️ Menghapus semua siswa perempuan...</p>";
            $stmt = $pdo->prepare("DELETE FROM siswa WHERE jenis_kelamin = 'P'");
            $stmt->execute();
            echo "<p style='color: green;'>✅ Berhasil menghapus {$totalPerempuan} siswa perempuan</p>";
        }
    }
    
    // Step 3: Modify table structure
    echo "<h2>🔧 Langkah 3: Modifikasi Struktur Tabel</h2>";
    
    echo "<p>🔄 Mengubah field jenis_kelamin menjadi ENUM('L') saja...</p>";
    $stmt = $pdo->prepare("ALTER TABLE siswa MODIFY COLUMN jenis_kelamin ENUM('L') NOT NULL DEFAULT 'L'");
    $stmt->execute();
    echo "<p style='color: green;'>✅ Berhasil mengubah struktur tabel siswa</p>";
    
    // Step 4: Verify changes
    echo "<h2>✅ Langkah 4: Verifikasi Perubahan</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM siswa");
    $newTotal = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM siswa WHERE jenis_kelamin = 'L'");
    $newTotalLaki = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Check if there are any 'P' records left (should be 0)
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM siswa WHERE jenis_kelamin = 'P'");
    $remainingPerempuan = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Hasil Akhir:</h4>";
    echo "<p><strong>Total Siswa:</strong> {$newTotal}</p>";
    echo "<p><strong>Siswa Laki-laki:</strong> {$newTotalLaki}</p>";
    echo "<p><strong>Siswa Perempuan:</strong> {$remainingPerempuan}</p>";
    echo "</div>";
    
    if ($remainingPerempuan == 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 KONVERSI BERHASIL!</h3>";
        echo "<p>Sistem telah berhasil dikonversi menjadi sekolah khusus laki-laki.</p>";
        echo "<p>Semua siswa sekarang memiliki jenis kelamin 'L' (Laki-laki).</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ MASIH ADA MASALAH!</h3>";
        echo "<p>Masih ditemukan {$remainingPerempuan} siswa dengan jenis kelamin 'P'.</p>";
        echo "<p>Silakan periksa kembali atau jalankan script lagi.</p>";
        echo "</div>";
    }
    
    // Step 5: Show table structure
    echo "<h2>🔍 Langkah 5: Struktur Tabel Baru</h2>";
    
    $stmt = $pdo->query("DESCRIBE siswa");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>" . ($column['Field'] === 'jenis_kelamin' ? "<strong style='color: green;'>{$column['Type']}</strong>" : $column['Type']) . "</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    // Step 6: Next steps
    echo "<h2>📝 Langkah Selanjutnya</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ol>";
    echo "<li>✅ <strong>Database sudah dikonversi</strong></li>";
    echo "<li>✅ <strong>Aplikasi PHP sudah diupdate</strong> (form, view, controller)</li>";
    echo "<li>🔄 <strong>Test semua fungsi CRUD siswa</strong></li>";
    echo "<li>📚 <strong>Update dokumentasi sistem</strong></li>";
    echo "<li>👥 <strong>Informasikan perubahan ke pengguna</strong></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test yang Perlu Dilakukan:</h4>";
    echo "<ul>";
    echo "<li>Tambah siswa baru - pastikan otomatis laki-laki</li>";
    echo "<li>Edit siswa - pastikan tidak ada pilihan perempuan</li>";
    echo "<li>Lihat daftar siswa - pastikan tidak ada kolom jenis kelamin</li>";
    echo "<li>Lihat detail siswa - pastikan selalu tampil laki-laki</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Silakan periksa kembali atau restore dari backup jika diperlukan.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Script selesai dijalankan pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
