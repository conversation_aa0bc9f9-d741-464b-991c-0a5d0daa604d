<?php
/**
 * Script untuk Memperbaiki Database Relasi Wali Kelas
 * 
 * Script ini akan:
 * 1. Memastikan kolom wali_kelas_id ada di tabel kelas
 * 2. Membuat user dengan role wali_kelas jika belum ada
 * 3. Mengassign wali kelas ke kelas-kelas yang ada
 * 4. Memverifikasi perbaikan
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';

echo "<h1>🔧 Perbaikan Database Relasi Wali Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Tujuan Perbaikan</h3>";
    echo "<p>Script ini akan memperbaiki sistem RBAC wali kelas dengan:</p>";
    echo "<ul>";
    echo "<li>Memastikan struktur database yang benar</li>";
    echo "<li>Membuat user wali kelas jika diperlukan</li>";
    echo "<li>Mengassign wali kelas ke kelas-kelas</li>";
    echo "<li>Memverifikasi perbaikan</li>";
    echo "</ul>";
    echo "</div>";
    
    // Step 1: Cek dan tambah kolom wali_kelas_id jika belum ada
    echo "<h2>🔧 Step 1: Memastikan Struktur Database</h2>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM kelas LIKE 'wali_kelas_id'");
    $hasWaliKelasId = $stmt->rowCount() > 0;
    
    if (!$hasWaliKelasId) {
        echo "<p>🔄 Menambah kolom wali_kelas_id ke tabel kelas...</p>";
        
        $pdo->exec("ALTER TABLE kelas ADD COLUMN wali_kelas_id INT NULL AFTER wali_kelas");
        echo "<p style='color: green;'>✅ Kolom wali_kelas_id berhasil ditambahkan</p>";
        
        $pdo->exec("ALTER TABLE kelas ADD CONSTRAINT fk_kelas_wali_kelas FOREIGN KEY (wali_kelas_id) REFERENCES users(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✅ Foreign key constraint berhasil ditambahkan</p>";
        
        $pdo->exec("ALTER TABLE kelas ADD INDEX idx_wali_kelas_id (wali_kelas_id)");
        echo "<p style='color: green;'>✅ Index berhasil ditambahkan</p>";
    } else {
        echo "<p style='color: green;'>✅ Kolom wali_kelas_id sudah ada</p>";
    }
    
    // Step 2: Cek dan update enum role untuk menambah wali_kelas
    echo "<h2>👥 Step 2: Memastikan Role Wali Kelas</h2>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
    $roleColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($roleColumn && strpos($roleColumn['Type'], 'wali_kelas') === false) {
        echo "<p>🔄 Menambah 'wali_kelas' ke enum role...</p>";
        $pdo->exec("ALTER TABLE users MODIFY COLUMN role ENUM('admin','pamong_mp','pamong_mt','pamong_mm','pamong_mu','wali_kelas','staff') DEFAULT 'staff'");
        echo "<p style='color: green;'>✅ Role 'wali_kelas' berhasil ditambahkan</p>";
    } else {
        echo "<p style='color: green;'>✅ Role 'wali_kelas' sudah tersedia</p>";
    }
    
    // Step 3: Cek dan buat user wali kelas jika belum ada
    echo "<h2>👨‍🏫 Step 3: Memastikan User Wali Kelas</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'wali_kelas'");
    $waliKelasCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($waliKelasCount == 0) {
        echo "<p>🔄 Membuat user wali kelas sample...</p>";
        
        // Buat beberapa user wali kelas sample
        $waliKelasSample = [
            ['username' => 'wali_kpa', 'nama' => 'Pak Budi Santoso', 'email' => '<EMAIL>'],
            ['username' => 'wali_x1', 'nama' => 'Pak Ahmad Wijaya', 'email' => '<EMAIL>'],
            ['username' => 'wali_x2', 'nama' => 'Pak Rizki Pratama', 'email' => '<EMAIL>'],
            ['username' => 'wali_xi1', 'nama' => 'Pak Dani Kurniawan', 'email' => '<EMAIL>'],
            ['username' => 'wali_xii1', 'nama' => 'Pak Fajar Nugroho', 'email' => '<EMAIL>']
        ];
        
        $defaultPassword = password_hash('wali123', PASSWORD_DEFAULT);
        
        foreach ($waliKelasSample as $wali) {
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, role, nama_lengkap, is_active, created_at)
                VALUES (?, ?, ?, 'wali_kelas', ?, 1, NOW())
            ");
            $stmt->execute([
                $wali['username'],
                $wali['email'],
                $defaultPassword,
                $wali['nama']
            ]);
            
            echo "<p style='color: green;'>✅ User wali kelas '{$wali['username']}' berhasil dibuat</p>";
        }
        
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📝 Catatan:</strong> Password default untuk semua wali kelas adalah: <code>wali123</code></p>";
        echo "<p>Silakan ubah password setelah login pertama kali.</p>";
        echo "</div>";
    } else {
        echo "<p style='color: green;'>✅ Sudah ada {$waliKelasCount} user dengan role wali_kelas</p>";
    }
    
    // Step 4: Assign wali kelas ke kelas-kelas yang ada
    echo "<h2>🏫 Step 4: Assign Wali Kelas ke Kelas</h2>";
    
    // Get all wali kelas users
    $stmt = $pdo->query("SELECT id, username, nama_lengkap FROM users WHERE role = 'wali_kelas' ORDER BY id");
    $waliKelasUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all kelas without wali_kelas_id
    $stmt = $pdo->query("SELECT id, nama_kelas, tingkat FROM kelas WHERE is_active = 1 AND (wali_kelas_id IS NULL OR wali_kelas_id = 0) ORDER BY tingkat, nama_kelas");
    $kelasWithoutWali = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($kelasWithoutWali)) {
        echo "<p style='color: green;'>✅ Semua kelas sudah memiliki wali kelas</p>";
    } else {
        echo "<p>🔄 Mengassign wali kelas ke {count($kelasWithoutWali)} kelas...</p>";
        
        $waliIndex = 0;
        foreach ($kelasWithoutWali as $kelas) {
            // Rotate through available wali kelas
            $assignedWali = $waliKelasUsers[$waliIndex % count($waliKelasUsers)];
            
            $stmt = $pdo->prepare("UPDATE kelas SET wali_kelas_id = ?, wali_kelas = ? WHERE id = ?");
            $stmt->execute([
                $assignedWali['id'],
                $assignedWali['nama_lengkap'],
                $kelas['id']
            ]);
            
            echo "<p style='color: green;'>✅ Kelas {$kelas['nama_kelas']} → Wali: {$assignedWali['nama_lengkap']}</p>";
            
            $waliIndex++;
        }
    }
    
    // Step 5: Update user_kelas_mapping jika tabel ada
    echo "<h2>🔗 Step 5: Update User Kelas Mapping</h2>";
    
    try {
        // Cek apakah tabel user_kelas_mapping ada
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_kelas_mapping'");
        $hasMappingTable = $stmt->rowCount() > 0;
        
        if (!$hasMappingTable) {
            echo "<p>🔄 Membuat tabel user_kelas_mapping...</p>";
            
            $pdo->exec("
                CREATE TABLE user_kelas_mapping (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    kelas_id INT NOT NULL,
                    role_type ENUM('wali_kelas', 'guru_mapel', 'guru_piket') DEFAULT 'wali_kelas',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user_kelas_role (user_id, kelas_id, role_type),
                    INDEX idx_user_id (user_id),
                    INDEX idx_kelas_id (kelas_id),
                    INDEX idx_role_type (role_type)
                )
            ");
            echo "<p style='color: green;'>✅ Tabel user_kelas_mapping berhasil dibuat</p>";
        }
        
        // Insert mapping untuk wali kelas yang sudah ada
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO user_kelas_mapping (user_id, kelas_id, role_type)
            SELECT wali_kelas_id, id, 'wali_kelas'
            FROM kelas 
            WHERE wali_kelas_id IS NOT NULL AND is_active = 1
        ");
        $stmt->execute();
        $insertedRows = $stmt->rowCount();
        
        echo "<p style='color: green;'>✅ {$insertedRows} mapping wali kelas berhasil ditambahkan</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Error pada user_kelas_mapping: " . $e->getMessage() . "</p>";
    }
    
    // Step 6: Verifikasi perbaikan
    echo "<h2>✅ Step 6: Verifikasi Perbaikan</h2>";
    
    // Cek jumlah kelas dengan wali kelas
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_kelas,
               COUNT(wali_kelas_id) as kelas_with_wali
        FROM kelas 
        WHERE is_active = 1
    ");
    $kelasStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Statistik Setelah Perbaikan:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Kelas Aktif:</strong> {$kelasStats['total_kelas']}</li>";
    echo "<li><strong>Kelas dengan Wali Kelas:</strong> {$kelasStats['kelas_with_wali']}</li>";
    echo "<li><strong>Total User Wali Kelas:</strong> " . count($waliKelasUsers) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test query getAllForWaliKelas dengan user pertama
    if (!empty($waliKelasUsers)) {
        $testUser = $waliKelasUsers[0];
        
        $stmt = $pdo->prepare("
            SELECT s.*, k.nama_kelas, k.tingkat, k.tahun_pelajaran
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.wali_kelas_id = ?
            AND s.status_siswa = 'aktif'
            AND k.is_active = 1
            ORDER BY s.nama_lengkap
        ");
        $stmt->execute([$testUser['id']]);
        $siswaResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🧪 Test Query untuk {$testUser['nama_lengkap']}:</h4>";
        echo "<p><strong>Siswa yang dapat diakses:</strong> " . count($siswaResult) . " siswa</p>";
        
        if (!empty($siswaResult)) {
            echo "<p style='color: green;'>✅ Query berhasil! Wali kelas dapat mengakses siswa di kelasnya.</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Belum ada siswa di kelas yang dikelola wali kelas ini.</p>";
        }
        echo "</div>";
    }
    
    // Informasi login untuk testing
    echo "<h2>🔑 Informasi Login untuk Testing</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>👨‍🏫 User Wali Kelas untuk Testing:</h4>";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Username</th><th>Password</th><th>Nama Lengkap</th><th>Role</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($waliKelasUsers as $user) {
        echo "<tr>";
        echo "<td><strong>{$user['username']}</strong></td>";
        echo "<td>wali123</td>";
        echo "<td>{$user['nama_lengkap']}</td>";
        echo "<td><span style='background: #28a745; color: white; padding: 2px 8px; border-radius: 3px;'>wali_kelas</span></td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    echo "<p><strong>📝 Cara Testing:</strong></p>";
    echo "<ol>";
    echo "<li>Login dengan salah satu username di atas</li>";
    echo "<li>Password: <code>wali123</code></li>";
    echo "<li>Akses halaman Daftar Siswa</li>";
    echo "<li>Verifikasi bahwa hanya siswa di kelas yang dikelola yang tampil</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎉 PERBAIKAN SELESAI!</h3>";
    echo "<p>Sistem RBAC wali kelas telah diperbaiki dan siap untuk digunakan.</p>";
    echo "<p>Setiap wali kelas sekarang hanya dapat mengakses siswa di kelas yang mereka kelola.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Silakan periksa kembali atau hubungi administrator.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Perbaikan selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
