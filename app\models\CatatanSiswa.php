<?php
require_once __DIR__ . '/Database.php';

class CatatanSiswa {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all catatan for a specific student
     */
    public function getBySiswaId($siswaId) {
        try {
            return $this->db->fetchAll("
                SELECT c.*, k.nama_kategori, k.warna_badge, k.icon_class, u.username as created_by_name
                FROM catatan_siswa c
                LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.siswa_id = ?
                ORDER BY c.tanggal_catatan DESC, c.created_at DESC
            ", [$siswaId]);
        } catch (Exception $e) {
            error_log("Error getting catatan: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get catatan by category for a student
     */
    public function getBySiswaIdAndCategory($siswaId, $jenisCatatan) {
        try {
            return $this->db->fetchAll("
                SELECT c.*, k.nama_kategori, k.warna_badge, k.icon_class, u.username as created_by_name
                FROM catatan_siswa c
                LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.siswa_id = ? AND c.jenis_catatan = ?
                ORDER BY c.tanggal_catatan DESC, c.created_at DESC
            ", [$siswaId, $jenisCatatan]);
        } catch (Exception $e) {
            error_log("Error getting catatan by category: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get catatan grouped by category (main categories only)
     */
    public function getGroupedBySiswaId($siswaId) {
        $allCatatan = $this->getBySiswaId($siswaId);
        $grouped = [
            'pamong' => [],
            'wali_kelas' => [],
            'bk' => []
        ];

        foreach ($allCatatan as $catatan) {
            $jenis = $catatan['jenis_catatan'];

            if (strpos($jenis, 'pamong_') === 0) {
                $grouped['pamong'][] = $catatan;
            } elseif (strpos($jenis, 'wali_') === 0) {
                $grouped['wali_kelas'][] = $catatan;
            } elseif (strpos($jenis, 'bk_') === 0) {
                // All BK catatan go to 'bk' group
                $grouped['bk'][] = $catatan;
            }
        }

        return $grouped;
    }

    /**
     * Get catatan BK grouped by sub-categories
     */
    public function getBKGroupedBySiswaId($siswaId) {
        $allCatatan = $this->getBySiswaId($siswaId);
        $bkGrouped = [
            'bk_konseling' => [],
            'bk_prestasi' => [],
            'bk_pelanggaran' => [],
            'bk_lainnya' => []
        ];

        foreach ($allCatatan as $catatan) {
            $jenis = $catatan['jenis_catatan'];

            if ($jenis === 'bk_konseling') {
                $bkGrouped['bk_konseling'][] = $catatan;
            } elseif ($jenis === 'bk_prestasi') {
                $bkGrouped['bk_prestasi'][] = $catatan;
            } elseif ($jenis === 'bk_pelanggaran') {
                $bkGrouped['bk_pelanggaran'][] = $catatan;
            } elseif ($jenis === 'bk_lainnya') {
                $bkGrouped['bk_lainnya'][] = $catatan;
            }
        }

        return $bkGrouped;
    }

    /**
     * Get catatan Pamong grouped by sub-categories
     */
    public function getPamongGroupedBySiswaId($siswaId) {
        $allCatatan = $this->getBySiswaId($siswaId);
        $pamongGrouped = [
            'pamong_mp' => [],
            'pamong_mt' => [],
            'pamong_mm' => [],
            'pamong_mu' => []
        ];

        foreach ($allCatatan as $catatan) {
            $jenis = $catatan['jenis_catatan'];

            if ($jenis === 'pamong_mp') {
                $pamongGrouped['pamong_mp'][] = $catatan;
            } elseif ($jenis === 'pamong_mt') {
                $pamongGrouped['pamong_mt'][] = $catatan;
            } elseif ($jenis === 'pamong_mm') {
                $pamongGrouped['pamong_mm'][] = $catatan;
            } elseif ($jenis === 'pamong_mu') {
                $pamongGrouped['pamong_mu'][] = $catatan;
            }
        }

        return $pamongGrouped;
    }

    /**
     * Get catatan Wali Kelas grouped by sub-categories
     */
    public function getWaliKelasGroupedBySiswaId($siswaId) {
        $allCatatan = $this->getBySiswaId($siswaId);
        $waliKelasGrouped = [
            'wali_kpp' => [],
            'wali_x' => [],
            'wali_xi' => [],
            'wali_xii' => [],
            'wali_kpa' => []
        ];

        foreach ($allCatatan as $catatan) {
            $jenis = $catatan['jenis_catatan'];

            if ($jenis === 'wali_kpp') {
                $waliKelasGrouped['wali_kpp'][] = $catatan;
            } elseif ($jenis === 'wali_x') {
                $waliKelasGrouped['wali_x'][] = $catatan;
            } elseif ($jenis === 'wali_xi') {
                $waliKelasGrouped['wali_xi'][] = $catatan;
            } elseif ($jenis === 'wali_xii') {
                $waliKelasGrouped['wali_xii'][] = $catatan;
            } elseif ($jenis === 'wali_kpa') {
                $waliKelasGrouped['wali_kpa'][] = $catatan;
            }
        }

        return $waliKelasGrouped;
    }

    /**
     * Get catatan grouped by category including specific BK types (for statistics)
     */
    public function getGroupedBySiswaIdDetailed($siswaId) {
        $allCatatan = $this->getBySiswaId($siswaId);
        $grouped = [
            'pamong' => [],
            'wali_kelas' => [],
            'bk' => [],
            'prestasi' => [],
            'pelanggaran' => [],
            'konseling' => []
        ];

        foreach ($allCatatan as $catatan) {
            $jenis = $catatan['jenis_catatan'];

            if (strpos($jenis, 'pamong_') === 0) {
                $grouped['pamong'][] = $catatan;
            } elseif (strpos($jenis, 'wali_') === 0) {
                $grouped['wali_kelas'][] = $catatan;
            } elseif (strpos($jenis, 'bk_') === 0) {
                // Group BK catatan by specific type
                $grouped['bk'][] = $catatan;

                // Also group by specific BK type for statistics
                if ($jenis === 'bk_prestasi') {
                    $grouped['prestasi'][] = $catatan;
                } elseif ($jenis === 'bk_pelanggaran') {
                    $grouped['pelanggaran'][] = $catatan;
                } elseif ($jenis === 'bk_konseling') {
                    $grouped['konseling'][] = $catatan;
                }
            }
        }

        return $grouped;
    }
    
    /**
     * Create new catatan
     */
    public function create($data) {
        try {
            // Validate required fields
            $requiredFields = ['siswa_id', 'jenis_catatan', 'judul_catatan', 'isi_catatan', 'created_by'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    throw new Exception("Field {$field} is required");
                }
            }

            // Validate siswa exists
            $siswaExists = $this->db->fetch("SELECT id_siswa FROM siswa WHERE id_siswa = ?", [$data['siswa_id']]);
            if (!$siswaExists) {
                throw new Exception("Siswa dengan ID {$data['siswa_id']} tidak ditemukan");
            }

            // Validate jenis_catatan exists in kategori
            $kategoriExists = $this->db->fetch("SELECT kode_kategori FROM kategori_catatan WHERE kode_kategori = ? AND is_active = 1", [$data['jenis_catatan']]);
            if (!$kategoriExists) {
                throw new Exception("Jenis catatan '{$data['jenis_catatan']}' tidak valid");
            }

            $sql = "INSERT INTO catatan_siswa (
                siswa_id, jenis_catatan, judul_catatan, isi_catatan,
                tanggal_catatan, tingkat_prioritas, status_catatan,
                tindak_lanjut, tanggal_tindak_lanjut, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $params = [
                $data['siswa_id'],
                $data['jenis_catatan'],
                $data['judul_catatan'],
                $data['isi_catatan'],
                $data['tanggal_catatan'] ?? date('Y-m-d'),
                $data['tingkat_prioritas'] ?? 'sedang',
                $data['status_catatan'] ?? 'aktif',
                $data['tindak_lanjut'] ?? null,
                $data['tanggal_tindak_lanjut'] ?? null,
                $data['created_by']
            ];

            $result = $this->db->query($sql, $params);

            if ($result) {
                $insertId = $this->db->lastInsertId();
                return $insertId ?: true; // Return ID if available, otherwise true for success
            } else {
                throw new Exception("Gagal menyimpan catatan ke database");
            }
        } catch (Exception $e) {
            error_log("Error creating catatan: " . $e->getMessage());
            throw $e; // Re-throw to let controller handle it
        }
    }
    
    /**
     * Update catatan
     */
    public function update($id, $data) {
        try {
            $sql = "UPDATE catatan_siswa SET 
                judul_catatan = ?, isi_catatan = ?, tanggal_catatan = ?,
                tingkat_prioritas = ?, status_catatan = ?, tindak_lanjut = ?,
                tanggal_tindak_lanjut = ?, updated_by = ?
                WHERE id = ?";
            
            $params = [
                $data['judul_catatan'],
                $data['isi_catatan'],
                $data['tanggal_catatan'],
                $data['tingkat_prioritas'],
                $data['status_catatan'],
                $data['tindak_lanjut'],
                $data['tanggal_tindak_lanjut'],
                $data['updated_by'],
                $id
            ];
            
            $this->db->query($sql, $params);
            return true;
        } catch (Exception $e) {
            error_log("Error updating catatan: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete catatan
     */
    public function delete($id) {
        try {
            $this->db->query("DELETE FROM catatan_siswa WHERE id = ?", [$id]);
            return true;
        } catch (Exception $e) {
            error_log("Error deleting catatan: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all categories
     */
    public function getCategories() {
        try {
            return $this->db->fetchAll("
                SELECT * FROM kategori_catatan 
                WHERE is_active = 1 
                ORDER BY kode_kategori
            ");
        } catch (Exception $e) {
            error_log("Error getting categories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get categories grouped by type
     */
    public function getCategoriesGrouped() {
        $categories = $this->getCategories();
        $grouped = [
            'pamong' => [],
            'wali_kelas' => [],
            'bk' => []
        ];

        foreach ($categories as $category) {
            $kode = $category['kode_kategori'];

            if (strpos($kode, 'pamong_') === 0) {
                $grouped['pamong'][] = $category;
            } elseif (strpos($kode, 'wali_') === 0) {
                $grouped['wali_kelas'][] = $category;
            } elseif (strpos($kode, 'bk_') === 0) {
                $grouped['bk'][] = $category;
            }
        }

        return $grouped;
    }

    /**
     * Get categories grouped by type with detailed BK grouping
     */
    public function getCategoriesGroupedDetailed() {
        $categories = $this->getCategories();
        $grouped = [
            'pamong' => [],
            'wali_kelas' => [],
            'bk_konseling' => [],
            'bk_prestasi' => [],
            'bk_pelanggaran' => [],
            'bk_lainnya' => []
        ];

        foreach ($categories as $category) {
            $kode = $category['kode_kategori'];

            if (strpos($kode, 'pamong_') === 0) {
                $grouped['pamong'][] = $category;
            } elseif (strpos($kode, 'wali_') === 0) {
                $grouped['wali_kelas'][] = $category;
            } elseif ($kode === 'bk_konseling') {
                $grouped['bk_konseling'][] = $category;
            } elseif ($kode === 'bk_prestasi') {
                $grouped['bk_prestasi'][] = $category;
            } elseif ($kode === 'bk_pelanggaran') {
                $grouped['bk_pelanggaran'][] = $category;
            } elseif ($kode === 'bk_lainnya') {
                $grouped['bk_lainnya'][] = $category;
            }
        }

        return $grouped;
    }
    
    /**
     * Get templates by category
     */
    public function getTemplatesByCategory($jenisCatatan) {
        try {
            return $this->db->fetchAll("
                SELECT * FROM template_catatan 
                WHERE jenis_catatan = ? AND is_active = 1
                ORDER BY nama_template
            ", [$jenisCatatan]);
        } catch (Exception $e) {
            error_log("Error getting templates: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get statistics for dashboard
     */
    public function getStatistics($siswaId = null) {
        try {
            $whereClause = $siswaId ? "WHERE siswa_id = ?" : "";
            $params = $siswaId ? [$siswaId] : [];

            return $this->db->fetchAll("
                SELECT
                    jenis_catatan,
                    COUNT(*) as total,
                    SUM(CASE WHEN status_catatan = 'aktif' THEN 1 ELSE 0 END) as aktif,
                    SUM(CASE WHEN tingkat_prioritas = 'urgent' THEN 1 ELSE 0 END) as urgent
                FROM catatan_siswa
                $whereClause
                GROUP BY jenis_catatan
            ", $params);
        } catch (Exception $e) {
            error_log("Error getting statistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get detailed statistics for student detail page
     */
    public function getDetailedStatistics($siswaId) {
        try {
            $grouped = $this->getGroupedBySiswaIdDetailed($siswaId);

            return [
                'total_catatan' => count($this->getBySiswaId($siswaId)),
                'total_prestasi' => count($grouped['prestasi']),
                'total_pelanggaran' => count($grouped['pelanggaran']),
                'total_konseling' => count($grouped['konseling']),
                'total_bimbingan' => count($grouped['pamong']) + count($grouped['wali_kelas']) + count($grouped['konseling']),
                'by_category' => [
                    'pamong' => count($grouped['pamong']),
                    'wali_kelas' => count($grouped['wali_kelas']),
                    'bk' => count($grouped['bk']),
                    'prestasi' => count($grouped['prestasi']),
                    'pelanggaran' => count($grouped['pelanggaran']),
                    'konseling' => count($grouped['konseling'])
                ]
            ];
        } catch (Exception $e) {
            error_log("Error getting detailed statistics: " . $e->getMessage());
            return [
                'total_catatan' => 0,
                'total_prestasi' => 0,
                'total_pelanggaran' => 0,
                'total_konseling' => 0,
                'total_bimbingan' => 0,
                'by_category' => []
            ];
        }
    }
}
?>
