# Fitur Pemilihan Tahun Ajaran

## Deskripsi
Fitur ini memungkinkan pengguna untuk memilih tahun ajaran yang ingin ditampilkan dalam aplikasi. Data siswa dan kelas akan difilter berdasarkan tahun ajaran yang dipilih.

## Fitur yang Diimplementasikan

### 1. Helper AcademicYear
- **File**: `app/helpers/AcademicYear.php`
- **Fungsi**:
  - Menentukan tahun ajaran saat ini berdasarkan tanggal
  - Mengelola tahun ajaran yang dipilih dalam session
  - Validasi format tahun ajaran (YYYY/YYYY)
  - Mendapatkan daftar tahun ajaran yang tersedia dari database

### 2. Controller AcademicYearController
- **File**: `app/controllers/AcademicYearController.php`
- **Endpoint**:
  - `POST /academic-year/change` - Mengubah tahun ajaran
  - `GET /academic-year/current` - Mendapatkan info tahun ajaran saat ini
  - `POST /academic-year/reset` - Reset ke tahun ajaran berjalan

### 3. Komponen Pemilih Tahun Ajaran
- **File**: `app/views/components/academic_year_selector.php`
- **Fitur**:
  - Dropdown untuk memilih tahun ajaran
  - Indikator tahun ajaran aktif/arsip
  - AJAX untuk perubahan tanpa reload halaman
  - Tombol reset ke tahun berjalan

### 4. Modifikasi Model
- **Siswa Model**: Filter data siswa berdasarkan tahun ajaran
- **Kelas Model**: Filter data kelas berdasarkan tahun ajaran
- Support untuk role-based access (Wali Kelas, Pamong)

### 5. Modifikasi Controller
- **SiswaController**: Menampilkan data siswa sesuai tahun ajaran
- **KelasController**: Menampilkan data kelas sesuai tahun ajaran
- Default tahun ajaran untuk form tambah kelas

### 6. Modifikasi View
- **Header**: Menambahkan komponen pemilih tahun ajaran di navbar
- **List Siswa**: Menampilkan info tahun ajaran yang aktif
- **List Kelas**: Menampilkan info tahun ajaran yang aktif
- **Form Kelas**: Default tahun ajaran sesuai yang dipilih

## Cara Kerja

### 1. Penentuan Tahun Ajaran Berjalan
Tahun ajaran ditentukan berdasarkan bulan:
- Juli - Desember: Tahun ajaran dimulai dari tahun saat ini
- Januari - Juni: Tahun ajaran dimulai dari tahun sebelumnya

Contoh:
- Bulan Oktober 2024 → Tahun ajaran 2024/2025
- Bulan Maret 2025 → Tahun ajaran 2024/2025

### 2. Session Management
- Tahun ajaran yang dipilih disimpan dalam `$_SESSION['selected_academic_year']`
- Default ke tahun ajaran berjalan jika belum ada pilihan
- Persisten selama session aktif

### 3. Filter Data
- Semua query data siswa dan kelas ditambahkan filter `tahun_pelajaran`
- Role-based access tetap berfungsi dengan filter tahun ajaran
- Data hanya menampilkan sesuai tahun ajaran yang dipilih

## Penggunaan

### 1. Memilih Tahun Ajaran
1. Klik dropdown tahun ajaran di navbar (sebelah kanan)
2. Pilih tahun ajaran yang diinginkan
3. Data akan otomatis ter-refresh

### 2. Reset ke Tahun Berjalan
1. Klik dropdown tahun ajaran
2. Pilih "Reset ke Tahun Berjalan"
3. Akan kembali ke tahun ajaran saat ini

### 3. Indikator Status
- **Badge Hijau "Berjalan"**: Tahun ajaran saat ini
- **Badge Biru "Aktif"**: Tahun ajaran yang sedang dipilih
- **Badge Kuning "Arsip"**: Tahun ajaran lama

## File yang Dimodifikasi

### File Baru:
- `app/helpers/AcademicYear.php`
- `app/controllers/AcademicYearController.php`
- `app/views/components/academic_year_selector.php`

### File yang Dimodifikasi:
- `public/index.php` (routing)
- `app/models/Siswa.php` (filter tahun ajaran)
- `app/models/Kelas.php` (filter tahun ajaran)
- `app/controllers/SiswaController.php` (menggunakan tahun ajaran)
- `app/controllers/KelasController.php` (menggunakan tahun ajaran)
- `app/views/layouts/header.php` (menambah komponen)
- `app/views/siswa/list.php` (info tahun ajaran)
- `app/views/kelas/list.php` (info tahun ajaran)
- `app/views/kelas/form.php` (default tahun ajaran)
- `public/assets/css/style.css` (styling komponen)

## Keamanan
- CSRF token protection untuk semua perubahan tahun ajaran
- Validasi format tahun ajaran
- Session-based storage
- Role-based access tetap terjaga

## Kompatibilitas
- Kompatibel dengan sistem role yang ada (Admin, Staff, Wali Kelas, Pamong)
- Tidak mengubah struktur database yang ada
- Backward compatible dengan data yang sudah ada
