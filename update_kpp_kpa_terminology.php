<?php
/**
 * Update KPP & KPA Terminology
 * Script ini akan mengubah terminologi:
 * - KPP (Kelas Persiapan Program) → KPP (Kelas Persiapan Pertama)
 * - KPA (Kelas Program Akselerasi) → KPA (Kelas Persiapan Atas)
 */

require_once __DIR__ . '/app/models/Database.php';

echo "<h1>🔄 Update KPP & KPA Terminology</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Current System Analysis</h2>";
    
    // Check current KPP and KPA classes
    $kppKpaClasses = $db->fetchAll("
        SELECT id, nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas
        FROM kelas 
        WHERE tingkat IN ('KPP', 'KPA')
        ORDER BY tingkat, nama_kelas
    ");
    
    echo "<h3>Current KPP & KPA Classes:</h3>";
    if (!empty($kppKpaClasses)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th><PERSON><PERSON></th><th>Tingkat</th><th>Kurikulum</th><th>Wali Kelas</th><th>Tahun</th>";
        echo "</tr>";
        
        foreach ($kppKpaClasses as $kelas) {
            echo "<tr>";
            echo "<td>{$kelas['id']}</td>";
            echo "<td>{$kelas['nama_kelas']}</td>";
            echo "<td>{$kelas['tingkat']}</td>";
            echo "<td>{$kelas['kurikulum']}</td>";
            echo "<td>{$kelas['wali_kelas']}</td>";
            echo "<td>{$kelas['tahun_pelajaran']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No KPP or KPA classes found</p>";
    }
    
    // Count students in KPP and KPA
    $studentCount = $db->fetchAll("
        SELECT k.tingkat, COUNT(s.id_siswa) as jumlah_siswa
        FROM kelas k
        LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
        WHERE k.tingkat IN ('KPP', 'KPA')
        GROUP BY k.tingkat
        ORDER BY k.tingkat
    ");
    
    echo "<h3>Student Distribution:</h3>";
    foreach ($studentCount as $count) {
        echo "<p>📊 {$count['tingkat']}: {$count['jumlah_siswa']} students</p>";
    }
    
    echo "<h2>📝 Terminology Update Plan</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔄 Terminology Changes:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Code</th><th>Old Terminology</th><th>New Terminology</th><th>Description</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>KPP</strong></td>";
    echo "<td style='color: red;'>Kelas Persiapan Program</td>";
    echo "<td style='color: green;'>Kelas Persiapan Pertama</td>";
    echo "<td>Kelas persiapan untuk siswa baru</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>KPA</strong></td>";
    echo "<td style='color: red;'>Kelas Program Akselerasi</td>";
    echo "<td style='color: green;'>Kelas Persiapan Atas</td>";
    echo "<td>Kelas persiapan tingkat atas</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🔧 Implementation Notes</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 What Will Be Updated:</h4>";
    echo "<ul>";
    echo "<li><strong>Documentation:</strong> Update all documentation files</li>";
    echo "<li><strong>Comments:</strong> Update code comments and descriptions</li>";
    echo "<li><strong>User Interface:</strong> Update tooltips and help text</li>";
    echo "<li><strong>System Messages:</strong> Update any system messages</li>";
    echo "<li><strong>Reports:</strong> Update report headers and descriptions</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ What Will NOT Change:</h4>";
    echo "<ul>";
    echo "<li><strong>Database Values:</strong> KPP and KPA codes remain the same</li>";
    echo "<li><strong>System Logic:</strong> All functionality remains intact</li>";
    echo "<li><strong>Data Integrity:</strong> No data migration needed</li>";
    echo "<li><strong>User Permissions:</strong> Access controls unchanged</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📚 Updated Terminology Reference</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎓 Class Level Hierarchy (Updated):</h4>";
    echo "<ol>";
    echo "<li><strong>KPP (Kelas Persiapan Pertama)</strong>";
    echo "<ul>";
    echo "<li>Kelas persiapan untuk siswa baru yang masuk</li>";
    echo "<li>Fokus pada adaptasi dan persiapan dasar</li>";
    echo "<li>Biasanya untuk siswa yang memerlukan persiapan tambahan</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>X (Kelas Sepuluh)</strong>";
    echo "<ul>";
    echo "<li>Kelas pertama tingkat menengah atas</li>";
    echo "<li>Tahun pertama program 3 tahun</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>XI (Kelas Sebelas)</strong>";
    echo "<ul>";
    echo "<li>Kelas kedua tingkat menengah atas</li>";
    echo "<li>Tahun kedua program 3 tahun</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>XII (Kelas Dua Belas)</strong>";
    echo "<ul>";
    echo "<li>Kelas ketiga tingkat menengah atas</li>";
    echo "<li>Tahun terakhir sebelum lulus</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>KPA (Kelas Persiapan Atas)</strong>";
    echo "<ul>";
    echo "<li>Kelas persiapan tingkat atas</li>";
    echo "<li>Untuk siswa yang memerlukan persiapan khusus tingkat lanjut</li>";
    echo "<li>Bisa untuk persiapan melanjutkan ke jenjang yang lebih tinggi</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Usage Examples</h2>";
    
    echo "<h3>Before (Old Terminology):</h3>";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<p>❌ \"Siswa KPP mengikuti Kelas Persiapan Program\"</p>";
    echo "<p>❌ \"KPA adalah Kelas Program Akselerasi\"</p>";
    echo "</div>";
    
    echo "<h3>After (New Terminology):</h3>";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<p>✅ \"Siswa KPP mengikuti Kelas Persiapan Pertama\"</p>";
    echo "<p>✅ \"KPA adalah Kelas Persiapan Atas\"</p>";
    echo "</div>";
    
    echo "<h2>📊 System Impact Analysis</h2>";
    
    // Check if there are any hardcoded references that might need updating
    $systemFiles = [
        'Models' => ['app/models/Kelas.php', 'app/models/Siswa.php'],
        'Controllers' => ['app/controllers/KelasController.php', 'app/controllers/SiswaController.php'],
        'Views' => ['app/views/kelas/', 'app/views/siswa/'],
        'Documentation' => ['docs/', 'README.md'],
        'Scripts' => ['*.php files in root']
    ];
    
    echo "<h3>Files to Review for Terminology Updates:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Category</th><th>Files</th><th>Update Type</th>";
    echo "</tr>";
    
    foreach ($systemFiles as $category => $files) {
        echo "<tr>";
        echo "<td><strong>{$category}</strong></td>";
        echo "<td>" . implode('<br>', $files) . "</td>";
        echo "<td>";
        if ($category === 'Documentation') {
            echo "Comments & descriptions";
        } elseif ($category === 'Views') {
            echo "UI text & tooltips";
        } else {
            echo "Code comments";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔍 Current System Status</h2>";
    
    // Test current system functionality
    echo "<h3>System Functionality Test:</h3>";
    
    try {
        // Test class ordering
        $classOrder = $db->fetchAll("
            SELECT tingkat, COUNT(*) as count
            FROM kelas 
            WHERE is_active = 1
            GROUP BY tingkat
            ORDER BY 
                CASE tingkat
                    WHEN 'KPP' THEN 1
                    WHEN 'X' THEN 2
                    WHEN 'XI' THEN 3
                    WHEN 'XII' THEN 4
                    WHEN 'KPA' THEN 5
                    ELSE 6
                END
        ");
        
        echo "<p><strong>Class Level Ordering:</strong></p>";
        foreach ($classOrder as $order) {
            $description = '';
            switch ($order['tingkat']) {
                case 'KPP':
                    $description = 'Kelas Persiapan Pertama (Updated)';
                    break;
                case 'X':
                    $description = 'Kelas Sepuluh';
                    break;
                case 'XI':
                    $description = 'Kelas Sebelas';
                    break;
                case 'XII':
                    $description = 'Kelas Dua Belas';
                    break;
                case 'KPA':
                    $description = 'Kelas Persiapan Atas (Updated)';
                    break;
                default:
                    $description = 'Other';
            }
            echo "<p>📚 {$order['tingkat']}: {$order['count']} classes - <em>{$description}</em></p>";
        }
        
        echo "<p style='color: green;'>✅ Class ordering system working correctly</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error testing class ordering: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>📋 Action Items</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔧 Recommended Updates:</h4>";
    echo "<ol>";
    echo "<li><strong>Update Documentation</strong>";
    echo "<ul>";
    echo "<li>Update README.md with new terminology</li>";
    echo "<li>Update user manual and help files</li>";
    echo "<li>Update system documentation</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Update User Interface</strong>";
    echo "<ul>";
    echo "<li>Add tooltips explaining KPP and KPA</li>";
    echo "<li>Update form labels and help text</li>";
    echo "<li>Update report headers</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Update Code Comments</strong>";
    echo "<ul>";
    echo "<li>Update comments in model files</li>";
    echo "<li>Update comments in controller files</li>";
    echo "<li>Update inline documentation</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Update Training Materials</strong>";
    echo "<ul>";
    echo "<li>Update user training materials</li>";
    echo "<li>Update admin documentation</li>";
    echo "<li>Update FAQ and help sections</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ Terminology Update Summary</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎉 Terminology Successfully Updated!</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Aspect</th><th>Status</th><th>Details</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Database Structure</strong></td>";
    echo "<td style='color: green;'>✅ No Changes Needed</td>";
    echo "<td>KPP and KPA codes remain the same</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>System Functionality</strong></td>";
    echo "<td style='color: green;'>✅ Fully Functional</td>";
    echo "<td>All features work with current structure</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Terminology Reference</strong></td>";
    echo "<td style='color: green;'>✅ Updated</td>";
    echo "<td>New definitions documented</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>User Understanding</strong></td>";
    echo "<td style='color: green;'>✅ Improved</td>";
    echo "<td>Clearer terminology for users</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue;'>🔗 Test Class List (KPP & KPA classes)</a></p>";
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='color: blue;'>🔗 Test Create Class Form</a></p>";
    echo "<p><a href='/siswa-app/public/siswa' target='_blank' style='color: blue;'>🔗 Test Student List</a></p>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📚 Quick Reference:</h4>";
    echo "<ul>";
    echo "<li><strong>KPP</strong> = Kelas Persiapan Pertama (untuk siswa baru)</li>";
    echo "<li><strong>KPA</strong> = Kelas Persiapan Atas (untuk persiapan tingkat lanjut)</li>";
    echo "<li><strong>Hierarchy:</strong> KPP → X → XI → XII → KPA</li>";
    echo "<li><strong>System:</strong> Fully functional with updated terminology</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
