<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}

// Security helper function
if (!class_exists('Security') || !method_exists('Security', 'canEditSiswa')) {
    class SecurityHelper {
        public static function canEditSiswa($siswaId) {
            // Default: allow edit if user is logged in
            return isset($_SESSION['user_id']);
        }
    }

    if (!class_exists('Security')) {
        class Security {
            public static function canEditSiswa($siswaId) {
                return SecurityHelper::canEditSiswa($siswaId);
            }
        }
    }
}

// Load berkas model safely
try {
    require_once __DIR__ . '/../../models/Berkas.php';
    $berkas_model = new Berkas();
    $berkas = $berkas_model->getBySiswaId($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $berkas = [];
}

// Load absensi data
try {
    require_once __DIR__ . '/../../models/Absensi.php';
    $absensi_model = new Absensi();
    $absensi_data = $absensi_model->getAbsensiByStudent($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
    $absensi_summary = $absensi_model->getAbsensiSummary($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $absensi_data = [];
    $absensi_summary = [
        'total_hadir' => 0,
        'total_sakit' => 0,
        'total_ijin' => 0,
        'total_alpha' => 0,
        'persentase_kehadiran' => 100
    ];
}
?>

<div class="container-fluid py-4">
    <!-- Main Layout -->
    <div class="row">
        <!-- Left Column - Student Profile -->
        <div class="col-lg-4 mb-4">
            <!-- Student Profile Card -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body text-center p-4">
                    <!-- Profile Photo -->
                    <div class="position-relative mb-3">
                        <div class="profile-photo mx-auto mb-3">
                            <?php if (!empty($siswa['foto'])): ?>
                                <img src="/siswa-app/public/uploads/foto_siswa/<?= htmlspecialchars($siswa['foto']) ?>" 
                                     alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                                     class="rounded-circle img-fluid shadow">
                            <?php else: ?>
                                <div class="bg-light text-muted rounded-circle d-flex align-items-center justify-content-center shadow">
                                    <i class="bi bi-person-fill" style="font-size: 3rem;"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                        <button class="btn btn-primary btn-sm position-absolute bottom-0 end-0 rounded-circle upload-photo-btn" 
                                style="width: 35px; height: 35px; right: 20px !important;"
                                data-bs-toggle="modal" data-bs-target="#uploadFotoModal">
                            <i class="bi bi-camera"></i>
                        </button>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Student Name & Basic Info -->
                    <h4 class="fw-bold mb-2"><?= htmlspecialchars($siswa['nama_lengkap']) ?></h4>
                    <p class="text-muted mb-1">
                        <i class="bi bi-card-text me-1"></i>
                        NIS: <?= htmlspecialchars($siswa['nis']) ?>
                    </p>
                    <p class="text-muted mb-3">
                        <i class="bi bi-mortarboard me-1"></i>
                        Kelas: <?= htmlspecialchars($siswa['nama_kelas']) ?>
                    </p>
                    
                    <!-- Status Badge -->
                    <div class="mb-3">
                        <?php if ($siswa['status_siswa'] === 'aktif'): ?>
                            <span class="badge bg-success px-3 py-2">
                                <i class="bi bi-check-circle me-1"></i>
                                Aktif
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary px-3 py-2">
                                <i class="bi bi-pause-circle me-1"></i>
                                <?= ucfirst($siswa['status_siswa']) ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                        <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?>" class="btn btn-warning">
                            <i class="bi bi-pencil me-1"></i>
                            Edit Data Siswa
                        </a>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadBerkasModal">
                            <i class="bi bi-upload me-1"></i>
                            Upload Berkas
                        </button>
                        <button class="btn btn-info text-white" data-bs-toggle="modal" data-bs-target="#addCatatanModal" data-siswa-id="<?= $siswa['id_siswa'] ?>">
                            <i class="bi bi-plus-circle me-1"></i>
                            Tambah Catatan
                        </button>
                        <?php endif; ?>
                        <a href="/siswa-app/public/siswa" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            Kembali ke Daftar
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Data Orang Tua Card -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 fw-bold">
                        <i class="bi bi-people me-2"></i>
                        Data Orang Tua
                    </h6>
                </div>
                <div class="card-body p-3">
                    <!-- Father Information -->
                    <div class="parent-info mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-2" style="width: 35px; height: 35px;">
                                <i class="bi bi-person-fill text-primary" style="font-size: 1rem;"></i>
                            </div>
                            <h6 class="mb-0 fw-bold text-primary">Ayah</h6>
                        </div>
                        <div class="ps-3">
                            <div class="mb-1">
                                <small class="text-muted d-block">Nama Lengkap</small>
                                <strong><?= htmlspecialchars($siswa['nama_ayah'] ?? 'Tidak diketahui') ?></strong>
                            </div>
                            <div class="mb-1">
                                <small class="text-muted d-block">Pekerjaan</small>
                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                    <?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? 'Tidak diketahui') ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mother Information -->
                    <div class="parent-info">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-2" style="width: 35px; height: 35px;">
                                <i class="bi bi-person-fill text-danger" style="font-size: 1rem;"></i>
                            </div>
                            <h6 class="mb-0 fw-bold text-danger">Ibu</h6>
                        </div>
                        <div class="ps-3">
                            <div class="mb-1">
                                <small class="text-muted d-block">Nama Lengkap</small>
                                <strong><?= htmlspecialchars($siswa['nama_ibu'] ?? 'Tidak diketahui') ?></strong>
                            </div>
                            <div class="mb-1">
                                <small class="text-muted d-block">Pekerjaan</small>
                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                    <?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? 'Tidak diketahui') ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Column - Main Content -->
        <div class="col-lg-8">
            <!-- Navigation Tabs -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom-0">
                    <ul class="nav nav-tabs card-header-tabs" id="studentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab">
                                <i class="bi bi-person me-1"></i>
                                Informasi Personal
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="absensi-tab" data-bs-toggle="tab" data-bs-target="#absensi" type="button" role="tab">
                                <i class="bi bi-calendar-check me-1"></i>
                                Data Absensi
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="akademik-tab" data-bs-toggle="tab" data-bs-target="#akademik" type="button" role="tab">
                                <i class="bi bi-book me-1"></i>
                                Informasi Akademik
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="catatan-tab" data-bs-toggle="tab" data-bs-target="#catatan" type="button" role="tab">
                                <i class="bi bi-journal-text me-1"></i>
                                Catatan Siswa
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="berkas-tab" data-bs-toggle="tab" data-bs-target="#berkas" type="button" role="tab">
                                <i class="bi bi-folder me-1"></i>
                                Berkas Siswa
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="studentTabsContent">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <!-- Data Identitas -->
                            <div class="row g-4 mb-4">
                                <div class="col-12">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-card-text text-primary me-2"></i>
                                            Data Identitas
                                        </h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                            <i class="bi bi-card-text text-primary"></i>
                                                        </div>
                                                        <div>
                                                            <small class="text-muted d-block">NISN</small>
                                                            <strong><?= htmlspecialchars($siswa['nisn'] ?? 'Tidak ada') ?></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                            <i class="bi bi-person-vcard text-primary"></i>
                                                        </div>
                                                        <div>
                                                            <small class="text-muted d-block">NIK</small>
                                                            <strong><?= htmlspecialchars($siswa['nik'] ?? 'Tidak ada') ?></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                            <i class="bi bi-house-door text-primary"></i>
                                                        </div>
                                                        <div>
                                                            <small class="text-muted d-block">No. KK</small>
                                                            <strong><?= htmlspecialchars($siswa['no_kk'] ?? 'Tidak ada') ?></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                                            <i class="bi bi-droplet text-danger"></i>
                                                        </div>
                                                        <div>
                                                            <small class="text-muted d-block">Golongan Darah</small>
                                                            <strong><?= htmlspecialchars($siswa['golongan_darah'] ?? 'Tidak diketahui') ?></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-4">
                                <!-- Birth Information -->
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-calendar-heart text-danger me-2"></i>
                                            Data Kelahiran
                                        </h6>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-geo-alt text-danger"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Tempat Lahir</small>
                                                    <strong><?= htmlspecialchars($siswa['tempat_lahir'] ?? 'Tidak diketahui') ?></strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-calendar-date text-danger"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Tanggal Lahir</small>
                                                    <strong>
                                                        <?php
                                                        if (!empty($siswa['tanggal_lahir'])) {
                                                            $birthDate = new DateTime($siswa['tanggal_lahir']);
                                                            $today = new DateTime();
                                                            $age = $today->diff($birthDate)->y;
                                                            echo $birthDate->format('d F Y') . " <small class='text-muted'>($age tahun)</small>";
                                                        } else {
                                                            echo 'Tidak diketahui';
                                                        }
                                                        ?>
                                                    </strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-telephone text-success me-2"></i>
                                            Kontak
                                        </h6>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-phone text-success"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">No. Telepon</small>
                                                    <?php if (!empty($siswa['no_telepon'])): ?>
                                                        <strong><?= htmlspecialchars($siswa['no_telepon']) ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-envelope text-success"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Email</small>
                                                    <?php if (!empty($siswa['email'])): ?>
                                                        <strong><?= htmlspecialchars($siswa['email']) ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Asal Sekolah & Paroki Section -->
                            <?php if (!empty($siswa['asal_sekolah']) || !empty($siswa['asal_paroki'])): ?>
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-building text-warning me-2"></i>
                                            Asal Sekolah & Paroki
                                        </h6>
                                        <div class="row g-3">
                                            <?php if (!empty($siswa['asal_sekolah'])): ?>
                                            <div class="col-md-6">
                                                <div class="bg-light rounded p-3">
                                                    <div class="d-flex">
                                                        <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3 flex-shrink-0">
                                                            <i class="bi bi-mortarboard text-warning"></i>
                                                        </div>
                                                        <div>
                                                            <small class="text-muted d-block">Asal Sekolah</small>
                                                            <strong><?= htmlspecialchars($siswa['asal_sekolah']) ?></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (!empty($siswa['asal_paroki'])): ?>
                                            <div class="col-md-6">
                                                <div class="bg-light rounded p-3">
                                                    <div class="d-flex">
                                                        <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3 flex-shrink-0">
                                                            <i class="bi bi-church text-warning"></i>
                                                        </div>
                                                        <div>
                                                            <small class="text-muted d-block">Asal Paroki</small>
                                                            <strong><?= htmlspecialchars($siswa['asal_paroki']) ?></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Address Section -->
                            <?php if (!empty($siswa['alamat'])): ?>
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-house text-info me-2"></i>
                                            Alamat Lengkap
                                        </h6>
                                        <div class="bg-light rounded p-3">
                                            <div class="d-flex">
                                                <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3 flex-shrink-0">
                                                    <i class="bi bi-geo-alt-fill text-info"></i>
                                                </div>
                                                <div>
                                                    <p class="mb-0"><?= nl2br(htmlspecialchars($siswa['alamat'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Data Absensi Tab -->
                        <div class="tab-pane fade" id="absensi" role="tabpanel">
                            <!-- Absensi Summary Cards -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-success bg-opacity-10 border-success border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-success rounded-circle p-2 me-2">
                                                    <i class="bi bi-check-circle text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-success"><?= number_format($absensi_summary['persentase_kehadiran'], 1) ?>%</h4>
                                            </div>
                                            <small class="text-muted">Kehadiran</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger bg-opacity-10 border-danger border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-danger rounded-circle p-2 me-2">
                                                    <i class="bi bi-thermometer-half text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-danger"><?= $absensi_summary['total_sakit'] ?></h4>
                                            </div>
                                            <small class="text-muted">Sakit</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-warning rounded-circle p-2 me-2">
                                                    <i class="bi bi-file-text text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-warning"><?= $absensi_summary['total_ijin'] ?></h4>
                                            </div>
                                            <small class="text-muted">Ijin</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary bg-opacity-10 border-secondary border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-secondary rounded-circle p-2 me-2">
                                                    <i class="bi bi-x-circle text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-secondary"><?= $absensi_summary['total_alpha'] ?></h4>
                                            </div>
                                            <small class="text-muted">Alpha</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Add Absensi Button -->
                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 fw-bold">Riwayat Absensi</h6>
                                <a href="/siswa-app/public/absensi/add/<?= $siswa['id_siswa'] ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Tambah Absensi
                                </a>
                            </div>
                            <?php endif; ?>

                            <!-- Absensi Table -->
                            <?php if (!empty($absensi_data)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Tanggal</th>
                                            <th>Jenis</th>
                                            <th>Keterangan</th>
                                            <th>Surat</th>
                                            <th>Dicatat Oleh</th>
                                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                            <th>Aksi</th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($absensi_data as $abs): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <strong><?= date('d/m/Y', strtotime($abs['tanggal'])) ?></strong>
                                                    <small class="text-muted"><?= date('l', strtotime($abs['tanggal'])) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $jenis_config = [
                                                    'sakit' => ['color' => 'danger', 'icon' => 'bi-thermometer-half', 'label' => 'Sakit'],
                                                    'ijin' => ['color' => 'warning', 'icon' => 'bi-file-text', 'label' => 'Ijin'],
                                                    'alpha' => ['color' => 'secondary', 'icon' => 'bi-x-circle', 'label' => 'Alpha']
                                                ];
                                                $config = $jenis_config[$abs['jenis_ketidakhadiran']] ?? $jenis_config['alpha'];
                                                ?>
                                                <span class="badge bg-<?= $config['color'] ?> d-flex align-items-center" style="width: fit-content;">
                                                    <i class="<?= $config['icon'] ?> me-1"></i>
                                                    <?= $config['label'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($abs['keterangan'])): ?>
                                                    <span class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($abs['keterangan']) ?>">
                                                        <?= htmlspecialchars(substr($abs['keterangan'], 0, 50)) ?><?= strlen($abs['keterangan']) > 50 ? '...' : '' ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($abs['surat_keterangan'])): ?>
                                                    <a href="/siswa-app/public/uploads/surat_keterangan/<?= htmlspecialchars($abs['surat_keterangan']) ?>"
                                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                                        <i class="bi bi-file-earmark-text me-1"></i>
                                                        Lihat Surat
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($abs['created_by_name'] ?? 'System') ?><br>
                                                    <?= date('d/m/Y H:i', strtotime($abs['created_at'])) ?>
                                                </small>
                                            </td>
                                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/siswa-app/public/absensi/edit/<?= $abs['id'] ?>"
                                                       class="btn btn-outline-warning btn-sm" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger btn-sm"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#deleteAbsensiModal"
                                                            data-absensi-id="<?= $abs['id'] ?>"
                                                            data-tanggal="<?= date('d/m/Y', strtotime($abs['tanggal'])) ?>"
                                                            title="Hapus">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <?php endif; ?>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada catatan absensi</h6>
                                <p class="text-muted mb-3">Siswa ini belum memiliki catatan ketidakhadiran</p>
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <a href="/siswa-app/public/absensi/add/<?= $siswa['id_siswa'] ?>" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Tambah Absensi Pertama
                                </a>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Informasi Akademik Tab -->
                        <div class="tab-pane fade" id="akademik" role="tabpanel">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25">
                                        <div class="card-body text-center">
                                            <div class="bg-warning bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                                <i class="bi bi-calendar-plus text-warning fs-4"></i>
                                            </div>
                                            <h6 class="fw-bold">Tahun Masuk</h6>
                                            <h4 class="text-warning fw-bold"><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-info bg-opacity-10 border-info border-opacity-25">
                                        <div class="card-body text-center">
                                            <div class="bg-info bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                                <i class="bi bi-calendar-check text-info fs-4"></i>
                                            </div>
                                            <h6 class="fw-bold">Terdaftar Sejak</h6>
                                            <h4 class="text-info fw-bold">
                                                <?php
                                                if (!empty($siswa['created_at'])) {
                                                    echo date('d M Y', strtotime($siswa['created_at']));
                                                } else {
                                                    echo 'N/A';
                                                }
                                                ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Catatan Siswa Tab -->
                        <div class="tab-pane fade" id="catatan" role="tabpanel">
                            <?php if (!empty($catatan_grouped)): ?>
                                <!-- Catatan Summary Cards -->
                                <div class="row g-3 mb-4">
                                    <?php
                                    // Use detailed statistics if available
                                    $stats = $catatan_stats ?? [
                                        'total_catatan' => 0,
                                        'total_prestasi' => 0,
                                        'total_pelanggaran' => 0,
                                        'total_bimbingan' => 0
                                    ];
                                    ?>
                                    <div class="col-md-3">
                                        <div class="card bg-info bg-opacity-10 border-info border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-info rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-journal-text text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-info"><?= $stats['total_catatan'] ?></h4>
                                                <small class="text-muted">Total Catatan</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success bg-opacity-10 border-success border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-success rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-award text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-success">
                                                    <?= $stats['total_prestasi'] ?>
                                                </h4>
                                                <small class="text-muted">Prestasi</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-warning rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-exclamation-triangle text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-warning">
                                                    <?= $stats['total_pelanggaran'] ?>
                                                </h4>
                                                <small class="text-muted">Pelanggaran</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-primary bg-opacity-10 border-primary border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-primary rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-people text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-primary">
                                                    <?= $stats['total_bimbingan'] ?>
                                                </h4>
                                                <small class="text-muted">Bimbingan</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add Catatan Button -->
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0 fw-bold">Riwayat Catatan</h6>
                                    <button class="btn btn-info text-white btn-sm" data-bs-toggle="modal" data-bs-target="#addCatatanModal">
                                        <i class="bi bi-plus-circle me-1"></i>
                                        Tambah Catatan
                                    </button>
                                </div>
                                <?php endif; ?>

                                <!-- Catatan by Category -->
                                <div class="accordion" id="catatanAccordion">
                                    <?php foreach ($catatan_grouped as $jenis => $catatanList): ?>
                                        <?php if (!empty($catatanList)): ?>
                                        <div class="accordion-item border-0 shadow-sm mb-3">
                                            <h2 class="accordion-header" id="heading<?= ucfirst($jenis) ?>">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= ucfirst($jenis) ?>" aria-expanded="false">
                                                    <?php
                                                    $jenisConfig = [
                                                        'wali_kelas' => ['icon' => 'bi-person-badge', 'color' => 'primary', 'label' => 'Catatan Wali Kelas'],
                                                        'pamong' => ['icon' => 'bi-people', 'color' => 'info', 'label' => 'Catatan Pamong'],
                                                        'bk' => ['icon' => 'bi-heart', 'color' => 'success', 'label' => 'Catatan BK'],
                                                        'akademik' => ['icon' => 'bi-book', 'color' => 'warning', 'label' => 'Catatan Akademik'],
                                                        'prestasi' => ['icon' => 'bi-award', 'color' => 'success', 'label' => 'Prestasi'],
                                                        'pelanggaran' => ['icon' => 'bi-exclamation-triangle', 'color' => 'danger', 'label' => 'Pelanggaran']
                                                    ];
                                                    $config = $jenisConfig[$jenis] ?? ['icon' => 'bi-journal', 'color' => 'secondary', 'label' => ucfirst($jenis)];
                                                    ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-<?= $config['color'] ?> bg-opacity-10 rounded-circle p-2 me-3">
                                                            <i class="<?= $config['icon'] ?> text-<?= $config['color'] ?>"></i>
                                                        </div>
                                                        <div>
                                                            <strong><?= $config['label'] ?></strong>
                                                            <span class="badge bg-<?= $config['color'] ?> ms-2"><?= count($catatanList) ?></span>
                                                        </div>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id="collapse<?= ucfirst($jenis) ?>" class="accordion-collapse collapse" data-bs-parent="#catatanAccordion">
                                                <div class="accordion-body">
                                                    <?php if ($jenis === 'bk' && isset($catatan_bk_grouped)): ?>
                                                        <!-- Sub-accordion untuk BK -->
                                                        <div class="accordion" id="bkSubAccordion">
                                                            <?php
                                                            $bkSubConfig = [
                                                                'bk_konseling' => ['icon' => 'bi-heart', 'color' => 'info', 'label' => 'Konseling'],
                                                                'bk_prestasi' => ['icon' => 'bi-award', 'color' => 'warning', 'label' => 'Prestasi'],
                                                                'bk_pelanggaran' => ['icon' => 'bi-exclamation-triangle', 'color' => 'danger', 'label' => 'Pelanggaran'],
                                                                'bk_lainnya' => ['icon' => 'bi-chat-dots', 'color' => 'secondary', 'label' => 'Lainnya']
                                                            ];
                                                            ?>

                                                            <?php foreach ($catatan_bk_grouped as $bkJenis => $bkCatatanList): ?>
                                                                <?php if (!empty($bkCatatanList)): ?>
                                                                    <?php $bkConfig = $bkSubConfig[$bkJenis] ?? ['icon' => 'bi-journal', 'color' => 'secondary', 'label' => ucfirst($bkJenis)]; ?>
                                                                    <div class="accordion-item border-0 shadow-sm mb-2">
                                                                        <h2 class="accordion-header" id="headingBK<?= ucfirst($bkJenis) ?>">
                                                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBK<?= ucfirst($bkJenis) ?>" aria-expanded="false">
                                                                                <div class="d-flex align-items-center">
                                                                                    <div class="bg-<?= $bkConfig['color'] ?> bg-opacity-10 rounded-circle p-2 me-3">
                                                                                        <i class="<?= $bkConfig['icon'] ?> text-<?= $bkConfig['color'] ?>"></i>
                                                                                    </div>
                                                                                    <div>
                                                                                        <strong><?= $bkConfig['label'] ?></strong>
                                                                                        <span class="badge bg-<?= $bkConfig['color'] ?> ms-2"><?= count($bkCatatanList) ?></span>
                                                                                    </div>
                                                                                </div>
                                                                            </button>
                                                                        </h2>
                                                                        <div id="collapseBK<?= ucfirst($bkJenis) ?>" class="accordion-collapse collapse" data-bs-parent="#bkSubAccordion">
                                                                            <div class="accordion-body">
                                                                                <div class="row g-3">
                                                                                    <?php foreach ($bkCatatanList as $catatan): ?>
                                                                                    <div class="col-12">
                                                                                        <div class="card border-start border-<?= $bkConfig['color'] ?> border-3">
                                                                                            <div class="card-body p-3">
                                                                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                                                                    <h6 class="mb-1 fw-bold"><?= htmlspecialchars($catatan['judul_catatan'] ?? 'Catatan ' . $bkConfig['label']) ?></h6>
                                                                                                    <small class="text-muted">
                                                                                                        <i class="bi bi-calendar me-1"></i>
                                                                                                        <?= date('d/m/Y', strtotime($catatan['tanggal_catatan'] ?? $catatan['created_at'])) ?>
                                                                                                    </small>
                                                                                                </div>
                                                                                                <p class="mb-2 text-muted"><?= nl2br(htmlspecialchars($catatan['isi_catatan'] ?? '')) ?></p>

                                                                                                <?php if (!empty($catatan['tindak_lanjut'])): ?>
                                                                                                <div class="bg-light rounded p-2 mb-2">
                                                                                                    <small class="fw-bold text-muted d-block">Tindak Lanjut:</small>
                                                                                                    <small class="text-muted"><?= nl2br(htmlspecialchars($catatan['tindak_lanjut'])) ?></small>
                                                                                                </div>
                                                                                                <?php endif; ?>

                                                                                                <div class="d-flex justify-content-between align-items-center">
                                                                                                    <small class="text-muted">
                                                                                                        <i class="bi bi-person me-1"></i>
                                                                                                        <?= htmlspecialchars($catatan['created_by_name'] ?? 'System') ?>
                                                                                                    </small>
                                                                                                    <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                                                                    <div class="btn-group btn-group-sm">
                                                                                                        <button class="btn btn-outline-warning btn-sm"
                                                                                                                onclick="editCatatan(<?= $catatan['id'] ?>)" title="Edit">
                                                                                                            <i class="bi bi-pencil"></i>
                                                                                                        </button>
                                                                                                        <button class="btn btn-outline-danger btn-sm"
                                                                                                                onclick="deleteCatatan(<?= $catatan['id'] ?>)" title="Hapus">
                                                                                                            <i class="bi bi-trash"></i>
                                                                                                        </button>
                                                                                                    </div>
                                                                                                    <?php endif; ?>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <?php endforeach; ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php elseif ($jenis === 'pamong' && isset($catatan_pamong_grouped)): ?>
                                                        <!-- Sub-accordion untuk Pamong -->
                                                        <div class="accordion" id="pamongSubAccordion">
                                                            <?php
                                                            $pamongSubConfig = [
                                                                'pamong_mp' => ['icon' => 'bi-person-badge', 'color' => 'info', 'label' => 'Pamong MP (KPP)'],
                                                                'pamong_mt' => ['icon' => 'bi-person-check', 'color' => 'success', 'label' => 'Pamong MT (X)'],
                                                                'pamong_mm' => ['icon' => 'bi-person-gear', 'color' => 'warning', 'label' => 'Pamong MM (XI)'],
                                                                'pamong_mu' => ['icon' => 'bi-person-exclamation', 'color' => 'danger', 'label' => 'Pamong MU (XII & KPA)']
                                                            ];
                                                            ?>

                                                            <?php foreach ($catatan_pamong_grouped as $pamongJenis => $pamongCatatanList): ?>
                                                                <?php if (!empty($pamongCatatanList)): ?>
                                                                    <?php $pamongConfig = $pamongSubConfig[$pamongJenis] ?? ['icon' => 'bi-journal', 'color' => 'secondary', 'label' => ucfirst($pamongJenis)]; ?>
                                                                    <div class="accordion-item border-0 shadow-sm mb-2">
                                                                        <h2 class="accordion-header" id="headingPamong<?= ucfirst($pamongJenis) ?>">
                                                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePamong<?= ucfirst($pamongJenis) ?>" aria-expanded="false">
                                                                                <div class="d-flex align-items-center">
                                                                                    <div class="bg-<?= $pamongConfig['color'] ?> bg-opacity-10 rounded-circle p-2 me-3">
                                                                                        <i class="<?= $pamongConfig['icon'] ?> text-<?= $pamongConfig['color'] ?>"></i>
                                                                                    </div>
                                                                                    <div>
                                                                                        <strong><?= $pamongConfig['label'] ?></strong>
                                                                                        <span class="badge bg-<?= $pamongConfig['color'] ?> ms-2"><?= count($pamongCatatanList) ?></span>
                                                                                    </div>
                                                                                </div>
                                                                            </button>
                                                                        </h2>
                                                                        <div id="collapsePamong<?= ucfirst($pamongJenis) ?>" class="accordion-collapse collapse" data-bs-parent="#pamongSubAccordion">
                                                                            <div class="accordion-body">
                                                                                <div class="row g-3">
                                                                                    <?php foreach ($pamongCatatanList as $catatan): ?>
                                                                                    <div class="col-12">
                                                                                        <div class="card border-start border-<?= $pamongConfig['color'] ?> border-3">
                                                                                            <div class="card-body p-3">
                                                                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                                                                    <h6 class="mb-1 fw-bold"><?= htmlspecialchars($catatan['judul_catatan'] ?? 'Catatan ' . $pamongConfig['label']) ?></h6>
                                                                                                    <small class="text-muted">
                                                                                                        <i class="bi bi-calendar me-1"></i>
                                                                                                        <?= date('d/m/Y', strtotime($catatan['tanggal_catatan'] ?? $catatan['created_at'])) ?>
                                                                                                    </small>
                                                                                                </div>
                                                                                                <p class="mb-2 text-muted"><?= nl2br(htmlspecialchars($catatan['isi_catatan'] ?? '')) ?></p>

                                                                                                <?php if (!empty($catatan['tindak_lanjut'])): ?>
                                                                                                <div class="bg-light rounded p-2 mb-2">
                                                                                                    <small class="fw-bold text-muted d-block">Tindak Lanjut:</small>
                                                                                                    <small class="text-muted"><?= nl2br(htmlspecialchars($catatan['tindak_lanjut'])) ?></small>
                                                                                                </div>
                                                                                                <?php endif; ?>

                                                                                                <div class="d-flex justify-content-between align-items-center">
                                                                                                    <small class="text-muted">
                                                                                                        <i class="bi bi-person me-1"></i>
                                                                                                        <?= htmlspecialchars($catatan['created_by_name'] ?? 'System') ?>
                                                                                                    </small>
                                                                                                    <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                                                                    <div class="btn-group btn-group-sm">
                                                                                                        <button class="btn btn-outline-warning btn-sm"
                                                                                                                onclick="editCatatan(<?= $catatan['id'] ?>)" title="Edit">
                                                                                                            <i class="bi bi-pencil"></i>
                                                                                                        </button>
                                                                                                        <button class="btn btn-outline-danger btn-sm"
                                                                                                                onclick="deleteCatatan(<?= $catatan['id'] ?>)" title="Hapus">
                                                                                                            <i class="bi bi-trash"></i>
                                                                                                        </button>
                                                                                                    </div>
                                                                                                    <?php endif; ?>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <?php endforeach; ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php elseif ($jenis === 'wali_kelas' && isset($catatan_wali_kelas_grouped)): ?>
                                                        <!-- Sub-accordion untuk Wali Kelas -->
                                                        <div class="accordion" id="waliKelasSubAccordion">
                                                            <?php
                                                            $waliKelasSubConfig = [
                                                                'wali_kpp' => ['icon' => 'bi-mortarboard', 'color' => 'primary', 'label' => 'Wali Kelas KPP'],
                                                                'wali_x' => ['icon' => 'bi-book', 'color' => 'info', 'label' => 'Wali Kelas X'],
                                                                'wali_xi' => ['icon' => 'bi-journal', 'color' => 'success', 'label' => 'Wali Kelas XI'],
                                                                'wali_xii' => ['icon' => 'bi-graduation', 'color' => 'warning', 'label' => 'Wali Kelas XII'],
                                                                'wali_kpa' => ['icon' => 'bi-award', 'color' => 'danger', 'label' => 'Wali Kelas KPA']
                                                            ];
                                                            ?>

                                                            <?php foreach ($catatan_wali_kelas_grouped as $waliKelasJenis => $waliKelasCatatanList): ?>
                                                                <?php if (!empty($waliKelasCatatanList)): ?>
                                                                    <?php $waliKelasConfig = $waliKelasSubConfig[$waliKelasJenis] ?? ['icon' => 'bi-journal', 'color' => 'secondary', 'label' => ucfirst($waliKelasJenis)]; ?>
                                                                    <div class="accordion-item border-0 shadow-sm mb-2">
                                                                        <h2 class="accordion-header" id="headingWaliKelas<?= ucfirst($waliKelasJenis) ?>">
                                                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWaliKelas<?= ucfirst($waliKelasJenis) ?>" aria-expanded="false">
                                                                                <div class="d-flex align-items-center">
                                                                                    <div class="bg-<?= $waliKelasConfig['color'] ?> bg-opacity-10 rounded-circle p-2 me-3">
                                                                                        <i class="<?= $waliKelasConfig['icon'] ?> text-<?= $waliKelasConfig['color'] ?>"></i>
                                                                                    </div>
                                                                                    <div>
                                                                                        <strong><?= $waliKelasConfig['label'] ?></strong>
                                                                                        <span class="badge bg-<?= $waliKelasConfig['color'] ?> ms-2"><?= count($waliKelasCatatanList) ?></span>
                                                                                    </div>
                                                                                </div>
                                                                            </button>
                                                                        </h2>
                                                                        <div id="collapseWaliKelas<?= ucfirst($waliKelasJenis) ?>" class="accordion-collapse collapse" data-bs-parent="#waliKelasSubAccordion">
                                                                            <div class="accordion-body">
                                                                                <div class="row g-3">
                                                                                    <?php foreach ($waliKelasCatatanList as $catatan): ?>
                                                                                    <div class="col-12">
                                                                                        <div class="card border-start border-<?= $waliKelasConfig['color'] ?> border-3">
                                                                                            <div class="card-body p-3">
                                                                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                                                                    <h6 class="mb-1 fw-bold"><?= htmlspecialchars($catatan['judul_catatan'] ?? 'Catatan ' . $waliKelasConfig['label']) ?></h6>
                                                                                                    <small class="text-muted">
                                                                                                        <i class="bi bi-calendar me-1"></i>
                                                                                                        <?= date('d/m/Y', strtotime($catatan['tanggal_catatan'] ?? $catatan['created_at'])) ?>
                                                                                                    </small>
                                                                                                </div>
                                                                                                <p class="mb-2 text-muted"><?= nl2br(htmlspecialchars($catatan['isi_catatan'] ?? '')) ?></p>

                                                                                                <?php if (!empty($catatan['tindak_lanjut'])): ?>
                                                                                                <div class="bg-light rounded p-2 mb-2">
                                                                                                    <small class="fw-bold text-muted d-block">Tindak Lanjut:</small>
                                                                                                    <small class="text-muted"><?= nl2br(htmlspecialchars($catatan['tindak_lanjut'])) ?></small>
                                                                                                </div>
                                                                                                <?php endif; ?>

                                                                                                <div class="d-flex justify-content-between align-items-center">
                                                                                                    <small class="text-muted">
                                                                                                        <i class="bi bi-person me-1"></i>
                                                                                                        <?= htmlspecialchars($catatan['created_by_name'] ?? 'System') ?>
                                                                                                    </small>
                                                                                                    <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                                                                    <div class="btn-group btn-group-sm">
                                                                                                        <button class="btn btn-outline-warning btn-sm"
                                                                                                                onclick="editCatatan(<?= $catatan['id'] ?>)" title="Edit">
                                                                                                            <i class="bi bi-pencil"></i>
                                                                                                        </button>
                                                                                                        <button class="btn btn-outline-danger btn-sm"
                                                                                                                onclick="deleteCatatan(<?= $catatan['id'] ?>)" title="Hapus">
                                                                                                            <i class="bi bi-trash"></i>
                                                                                                        </button>
                                                                                                    </div>
                                                                                                    <?php endif; ?>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <?php endforeach; ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <!-- Tampilan normal untuk kategori non-BK -->
                                                        <div class="row g-3">
                                                            <?php foreach ($catatanList as $catatan): ?>
                                                            <div class="col-12">
                                                                <div class="card border-start border-<?= $config['color'] ?> border-3">
                                                                    <div class="card-body p-3">
                                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                                            <h6 class="mb-1 fw-bold"><?= htmlspecialchars($catatan['judul_catatan'] ?? 'Catatan ' . $config['label']) ?></h6>
                                                                            <small class="text-muted">
                                                                                <i class="bi bi-calendar me-1"></i>
                                                                                <?= date('d/m/Y', strtotime($catatan['tanggal_catatan'] ?? $catatan['created_at'])) ?>
                                                                            </small>
                                                                        </div>
                                                                        <p class="mb-2 text-muted"><?= nl2br(htmlspecialchars($catatan['isi_catatan'] ?? '')) ?></p>

                                                                        <?php if (!empty($catatan['tindak_lanjut'])): ?>
                                                                        <div class="bg-light rounded p-2 mb-2">
                                                                            <small class="fw-bold text-muted d-block">Tindak Lanjut:</small>
                                                                            <small class="text-muted"><?= nl2br(htmlspecialchars($catatan['tindak_lanjut'])) ?></small>
                                                                        </div>
                                                                        <?php endif; ?>

                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <small class="text-muted">
                                                                                <i class="bi bi-person me-1"></i>
                                                                                <?= htmlspecialchars($catatan['created_by_name'] ?? 'System') ?>
                                                                            </small>
                                                                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                                            <div class="btn-group btn-group-sm">
                                                                                <button class="btn btn-outline-warning btn-sm"
                                                                                        onclick="editCatatan(<?= $catatan['id'] ?>)" title="Edit">
                                                                                    <i class="bi bi-pencil"></i>
                                                                                </button>
                                                                                <button class="btn btn-outline-danger btn-sm"
                                                                                        onclick="deleteCatatan(<?= $catatan['id'] ?>)" title="Hapus">
                                                                                    <i class="bi bi-trash"></i>
                                                                                </button>
                                                                            </div>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-journal-x text-muted" style="font-size: 3rem;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada catatan</h6>
                                <p class="text-muted mb-3">Siswa ini belum memiliki catatan apapun</p>
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <button class="btn btn-info text-white" data-bs-toggle="modal" data-bs-target="#addCatatanModal">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Tambah Catatan Pertama
                                </button>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Berkas Siswa Tab -->
                        <div class="tab-pane fade" id="berkas" role="tabpanel">
                            <?php if (!empty($berkas)): ?>
                                <!-- File Statistics -->
                                <div class="row g-3 mb-4">
                                    <div class="col-md-4">
                                        <div class="card bg-primary bg-opacity-10 border-primary border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-primary rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-files text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-primary"><?= count($berkas) ?></h4>
                                                <small class="text-muted">Total Berkas</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-success bg-opacity-10 border-success border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-success rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-folder text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-success">
                                                    <?php
                                                    $categories = array_unique(array_column($berkas, 'jenis_berkas'));
                                                    echo count($categories);
                                                    ?>
                                                </h4>
                                                <small class="text-muted">Kategori</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-info bg-opacity-10 border-info border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-info rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-clock text-white"></i>
                                                </div>
                                                <h6 class="mb-0 fw-bold text-info">
                                                    <?php
                                                    if (!empty($berkas)) {
                                                        $latest = max(array_column($berkas, 'created_at'));
                                                        echo date('d/m/Y', strtotime($latest));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </h6>
                                                <small class="text-muted">Terakhir Upload</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Upload Button -->
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0 fw-bold">Daftar Berkas</h6>
                                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#uploadBerkasModal">
                                        <i class="bi bi-upload me-1"></i>
                                        Upload Berkas
                                    </button>
                                </div>
                                <?php endif; ?>

                                <!-- Files List by Category -->
                                <?php
                                // Group berkas by category
                                $berkasGrouped = [];
                                $berkasModel = new Berkas();
                                $fileCategories = $berkasModel->getFileCategories();

                                // Define category mapping with icons and colors
                                $categoryMapping = [
                                    'Dokumen Identitas' => [
                                        'icon' => 'bi-person-vcard',
                                        'color' => 'primary',
                                        'bg' => 'bg-primary-subtle'
                                    ],
                                    'Rapor' => [
                                        'icon' => 'bi-journal-bookmark',
                                        'color' => 'success',
                                        'bg' => 'bg-success-subtle'
                                    ],
                                    'Ijazah' => [
                                        'icon' => 'bi-award',
                                        'color' => 'warning',
                                        'bg' => 'bg-warning-subtle'
                                    ],
                                    'Foto' => [
                                        'icon' => 'bi-camera',
                                        'color' => 'info',
                                        'bg' => 'bg-info-subtle'
                                    ],
                                    'Surat' => [
                                        'icon' => 'bi-envelope-paper',
                                        'color' => 'danger',
                                        'bg' => 'bg-danger-subtle'
                                    ],
                                    'Prestasi' => [
                                        'icon' => 'bi-trophy',
                                        'color' => 'warning',
                                        'bg' => 'bg-warning-subtle'
                                    ],
                                    'Lainnya' => [
                                        'icon' => 'bi-folder-plus',
                                        'color' => 'secondary',
                                        'bg' => 'bg-secondary-subtle'
                                    ]
                                ];

                                // Group files by category
                                foreach ($berkas as $file) {
                                    $jenisberkas = $file['jenis_berkas'];
                                    $categoryFound = false;

                                    foreach ($fileCategories as $categoryName => $types) {
                                        if (array_key_exists($jenisberkas, $types)) {
                                            if (!isset($berkasGrouped[$categoryName])) {
                                                $berkasGrouped[$categoryName] = [];
                                            }
                                            $berkasGrouped[$categoryName][] = $file;
                                            $categoryFound = true;
                                            break;
                                        }
                                    }

                                    // If not found in any category, put in "Lainnya"
                                    if (!$categoryFound) {
                                        if (!isset($berkasGrouped['Lainnya'])) {
                                            $berkasGrouped['Lainnya'] = [];
                                        }
                                        $berkasGrouped['Lainnya'][] = $file;
                                    }
                                }

                                // Function to get document display name
                                function getDocumentDisplayName($jenisberkas) {
                                    $displayNames = [
                                        // Dokumen Identitas
                                        'kartu_keluarga' => 'Kartu Keluarga',
                                        'akta_lahir' => 'Akta Kelahiran',

                                        // Rapor
                                        'rapor_kelas_x' => 'Rapor Kelas X',
                                        'rapor_kelas_xi' => 'Rapor Kelas XI',
                                        'rapor_kelas_xii' => 'Rapor Kelas XII',
                                        'rapor_kpp' => 'Rapor KPP',
                                        'rapor_kpa' => 'Rapor KPA',

                                        // Ijazah
                                        'ijazah_sd' => 'Ijazah SD/MI',
                                        'ijazah_smp' => 'Ijazah SMP/MTs',
                                        'ijazah_sma' => 'Ijazah SMA/SMK/MA',

                                        // Foto
                                        'foto_siswa' => 'Foto Siswa',

                                        // Surat
                                        'surat_keterangan_sehat' => 'Surat Keterangan Sehat',
                                        'surat_kelakuan_baik' => 'Surat Kelakuan Baik',
                                        'surat_peringatan_1' => 'Surat Peringatan 1',
                                        'surat_peringatan_2' => 'Surat Peringatan 2',
                                        'surat_peringatan_3' => 'Surat Peringatan 3',
                                        'surat_panggilan_ortu' => 'Surat Panggilan Orang Tua',

                                        // Prestasi
                                        'piagam_prestasi' => 'Piagam Prestasi',
                                        'sertifikat_lomba' => 'Sertifikat Lomba',
                                        'penghargaan_akademik' => 'Penghargaan Akademik',

                                        // Lainnya
                                        'lainnya' => 'Dokumen Lainnya'
                                    ];
                                    return $displayNames[$jenisberkas] ?? ucwords(str_replace('_', ' ', $jenisberkas));
                                }
                                ?>

                                <?php foreach ($berkasGrouped as $categoryName => $categoryFiles): ?>
                                <div class="mb-4">
                                    <?php
                                    $categoryInfo = $categoryMapping[$categoryName] ?? [
                                        'icon' => 'bi-folder',
                                        'color' => 'secondary',
                                        'bg' => 'bg-secondary-subtle'
                                    ];
                                    ?>

                                    <!-- Category Header -->
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="<?= $categoryInfo['bg'] ?> rounded-circle p-2 me-3">
                                            <i class="<?= $categoryInfo['icon'] ?> text-<?= $categoryInfo['color'] ?> fs-5"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-bold text-<?= $categoryInfo['color'] ?>"><?= $categoryName ?></h6>
                                            <small class="text-muted"><?= count($categoryFiles) ?> berkas</small>
                                        </div>
                                    </div>

                                    <!-- Category Files -->
                                    <div class="row g-2">
                                        <?php foreach ($categoryFiles as $file): ?>
                                        <div class="col-md-6 col-lg-4">
                                            <div class="card border-0 shadow-sm h-100">
                                                <div class="card-body p-3">
                                                    <div class="d-flex align-items-start">
                                                        <div class="me-3">
                                                            <?php
                                                            // Get file extension for icon
                                                            $fileName = $file['nama_file_asli'] ?? $file['nama_berkas'] ?? '';
                                                            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                                                            $iconClass = 'bi-file-earmark';
                                                            $iconColor = 'text-secondary';

                                                            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                                                                $iconClass = 'bi-file-earmark-image';
                                                                $iconColor = 'text-success';
                                                            } elseif ($extension === 'pdf') {
                                                                $iconClass = 'bi-file-earmark-pdf';
                                                                $iconColor = 'text-danger';
                                                            } elseif (in_array($extension, ['doc', 'docx'])) {
                                                                $iconClass = 'bi-file-earmark-word';
                                                                $iconColor = 'text-primary';
                                                            }
                                                            ?>
                                                            <div class="bg-light rounded p-2">
                                                                <i class="<?= $iconClass ?> <?= $iconColor ?> fs-4"></i>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-1 fw-bold"><?= getDocumentDisplayName($file['jenis_berkas']) ?></h6>
                                                            <?php if (!empty($file['keterangan'])): ?>
                                                            <small class="text-info d-block">
                                                                <i class="bi bi-info-circle me-1"></i>
                                                                <?= htmlspecialchars($file['keterangan']) ?>
                                                            </small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                    <!-- Action Buttons -->
                                                    <div class="mt-3 d-flex gap-1">
                                                        <a href="/siswa-app/public/<?= htmlspecialchars($file['file_path']) ?>"
                                                           target="_blank" class="btn btn-outline-primary btn-sm flex-fill" title="Lihat">
                                                            <i class="bi bi-eye me-1"></i>Lihat
                                                        </a>
                                                        <a href="/siswa-app/public/berkas/download/<?= $file['id'] ?>"
                                                           class="btn btn-outline-success btn-sm flex-fill" title="Download">
                                                            <i class="bi bi-download me-1"></i>Download
                                                        </a>
                                                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                        <button class="btn btn-outline-danger btn-sm"
                                                                onclick="deleteBerkas(<?= $file['id'] ?>)" title="Hapus">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-folder-x text-muted" style="font-size: 3rem;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada berkas</h6>
                                <p class="text-muted mb-3">Siswa ini belum memiliki berkas yang diupload</p>
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadBerkasModal">
                                    <i class="bi bi-upload me-1"></i>
                                    Upload Berkas Pertama
                                </button>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Styles -->
<style>
/* Profile Photo Styling */
.profile-photo {
    width: 150px;
    height: 150px;
    position: relative;
}

.profile-photo img,
.profile-photo > div {
    width: 150px;
    height: 150px;
    object-fit: cover;
}

/* Card Enhancements */
.card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

/* Tab Styling */
.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 4px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.nav-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Badge Styling */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Info Group Styling */
.info-group {
    margin-bottom: 2rem;
}

.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

/* Parent Info Styling */
.parent-info {
    padding: 1rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .profile-photo {
        width: 120px;
        height: 120px;
    }

    .profile-photo img,
    .profile-photo > div {
        width: 120px;
        height: 120px;
    }

    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
}

/* Loading Animation */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sub-accordion styling for BK, Pamong, and Wali Kelas */
#bkSubAccordion .accordion-item,
#pamongSubAccordion .accordion-item,
#waliKelasSubAccordion .accordion-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

#bkSubAccordion .accordion-button,
#pamongSubAccordion .accordion-button,
#waliKelasSubAccordion .accordion-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    background-color: #f8f9fa;
}

#bkSubAccordion .accordion-button:not(.collapsed),
#pamongSubAccordion .accordion-button:not(.collapsed),
#waliKelasSubAccordion .accordion-button:not(.collapsed) {
    background-color: #e7f3ff;
    border-color: #b6d7ff;
}

#bkSubAccordion .accordion-body,
#pamongSubAccordion .accordion-body,
#waliKelasSubAccordion .accordion-body {
    padding: 1rem;
    background-color: #fdfdfd;
}
</style>

<!-- Upload Foto Modal -->
<div class="modal fade" id="uploadFotoModal" tabindex="-1" aria-labelledby="uploadFotoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFotoModalLabel">
                    <i class="bi bi-camera me-2"></i>
                    Upload Foto Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/siswa-app/public/siswa/upload-foto/<?= $siswa['id_siswa'] ?>" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                    <div class="mb-3">
                        <label for="foto" class="form-label">Pilih Foto</label>
                        <input type="file" class="form-control" id="foto" name="foto" accept="image/*" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            Format: JPG, JPEG, PNG. Maksimal 2MB.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview</label>
                        <div id="fotoPreview" class="text-center">
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview foto akan muncul di sini</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload me-1"></i>
                        Upload Foto
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Upload Berkas Modal -->
<div class="modal fade" id="uploadBerkasModal" tabindex="-1" aria-labelledby="uploadBerkasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadBerkasModalLabel">
                    <i class="bi bi-upload me-2"></i>
                    Upload Berkas Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?>" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis_berkas" class="form-label">Jenis Berkas</label>
                                <select class="form-select" id="jenis_berkas" name="jenis_berkas" required>
                                    <option value="">Pilih Jenis Berkas</option>
                                    <?php
                                    // Load berkas model to get categories
                                    require_once __DIR__ . '/../../models/Berkas.php';
                                    $berkasModel = new Berkas();
                                    $fileCategories = $berkasModel->getFileCategories();

                                    foreach ($fileCategories as $categoryName => $types):
                                        if (!empty($types)): ?>
                                        <optgroup label="<?= htmlspecialchars($categoryName) ?>">
                                            <?php foreach ($types as $typeKey => $typeName): ?>
                                            <option value="<?= htmlspecialchars($typeKey) ?>">
                                                <?= htmlspecialchars($typeName) ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                    <?php endif;
                                    endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="berkas_file" class="form-label">File Berkas</label>
                                <input type="file" class="form-control" id="berkas_file" name="file" required
                                       accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx">
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Format: PDF, JPG, PNG, DOC, DOCX. Maksimal 5MB.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="keterangan_berkas" class="form-label">Keterangan (Opsional)</label>
                        <textarea class="form-control" id="keterangan_berkas" name="keterangan" rows="3" placeholder="Tambahkan keterangan untuk berkas ini..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview File</label>
                        <div id="berkasPreview" class="text-center">
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview file akan muncul di sini</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-upload me-1"></i>
                        Upload Berkas
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Tambah Catatan Modal -->
<div class="modal fade" id="addCatatanModal" tabindex="-1" aria-labelledby="addCatatanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCatatanModalLabel">
                    <i class="bi bi-journal-plus me-2"></i>
                    Tambah Catatan Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/siswa-app/public/catatan/create" method="POST" id="modalCatatanForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?>">

                    <!-- Alert Container -->
                    <div id="modalAlertContainer"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal_jenis_catatan" class="form-label">
                                    <i class="bi bi-tag me-1"></i>Jenis Catatan
                                </label>
                                <select class="form-select" id="modal_jenis_catatan" name="jenis_catatan" required>
                                    <option value="">Pilih Jenis Catatan</option>
                                    <?php
                                    // Get categories for current user role
                                    require_once __DIR__ . '/../../models/CatatanSiswa.php';
                                    $catatanModel = new CatatanSiswa();
                                    $categories = $catatanModel->getCategoriesGroupedDetailed();
                                    $filteredCategories = Security::filterCatatanCategoriesByRole($categories);

                                    // Define group labels with icons
                                    $groupLabels = [
                                        'pamong' => '📚 Pamong',
                                        'wali_kelas' => '👨‍🏫 Wali Kelas',
                                        'bk_konseling' => '💬 Konseling BK',
                                        'bk_prestasi' => '🏆 Prestasi',
                                        'bk_pelanggaran' => '⚠️ Pelanggaran',
                                        'bk_lainnya' => '📝 BK Lainnya',
                                        'bk' => '🧠 Bimbingan Konseling'
                                    ];

                                    foreach ($filteredCategories as $groupName => $groupCategories):
                                        if (!empty($groupCategories)):
                                            $label = $groupLabels[$groupName] ?? ucfirst(str_replace('_', ' ', $groupName));
                                        ?>
                                        <optgroup label="<?= $label ?>">
                                            <?php foreach ($groupCategories as $category): ?>
                                            <option value="<?= $category['kode_kategori'] ?>">
                                                <?= $category['nama_kategori'] ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                    <?php endif;
                                    endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal_tanggal_catatan" class="form-label">
                                    <i class="bi bi-calendar me-1"></i>Tanggal
                                </label>
                                <input type="date" class="form-control" id="modal_tanggal_catatan" name="tanggal_catatan" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="modal_judul_catatan" class="form-label">
                            <i class="bi bi-pencil me-1"></i>Judul Catatan
                        </label>
                        <input type="text" class="form-control" id="modal_judul_catatan" name="judul_catatan" placeholder="Masukkan judul catatan..." maxlength="255" required>
                        <div class="form-text">
                            <small class="text-muted" id="modalJudulCounter">0/255 karakter</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="modal_isi_catatan" class="form-label">
                            <i class="bi bi-journal-text me-1"></i>Isi Catatan
                        </label>
                        <textarea class="form-control" id="modal_isi_catatan" name="isi_catatan" rows="5" placeholder="Tulis catatan lengkap di sini..." required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal_tingkat_prioritas" class="form-label">
                                    <i class="bi bi-exclamation-circle me-1"></i>Tingkat Prioritas
                                </label>
                                <select class="form-select" id="modal_tingkat_prioritas" name="tingkat_prioritas">
                                    <option value="rendah">Rendah</option>
                                    <option value="sedang" selected>Sedang</option>
                                    <option value="tinggi">Tinggi</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal_status_catatan" class="form-label">
                                    <i class="bi bi-check-circle me-1"></i>Status
                                </label>
                                <select class="form-select" id="modal_status_catatan" name="status_catatan">
                                    <option value="draft">Draft</option>
                                    <option value="aktif" selected>Aktif</option>
                                    <option value="selesai">Selesai</option>
                                    <option value="ditunda">Ditunda</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="modal_tindak_lanjut" class="form-label">
                            <i class="bi bi-arrow-right-circle me-1"></i>Tindak Lanjut (Opsional)
                        </label>
                        <textarea class="form-control" id="modal_tindak_lanjut" name="tindak_lanjut" rows="3" placeholder="Rencana tindak lanjut atau rekomendasi..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="modal_tanggal_tindak_lanjut" class="form-label">
                            <i class="bi bi-calendar-check me-1"></i>Tanggal Tindak Lanjut (Opsional)
                        </label>
                        <input type="date" class="form-control" id="modal_tanggal_tindak_lanjut" name="tanggal_tindak_lanjut">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-info text-white">
                        <i class="bi bi-save me-1"></i>
                        Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript for File Preview and Interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Photo upload preview
    const fotoInput = document.getElementById('foto');
    const fotoPreview = document.getElementById('fotoPreview');

    if (fotoInput && fotoPreview) {
        fotoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        fotoPreview.innerHTML = `
                            <img src="${e.target.result}"
                                 class="img-fluid rounded"
                                 style="max-height: 200px; object-fit: cover;">
                        `;
                    };
                    reader.readAsDataURL(file);
                } else {
                    fotoPreview.innerHTML = `
                        <div class="bg-danger bg-opacity-10 rounded p-4">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                            <p class="text-danger mb-0 mt-2">File harus berupa gambar</p>
                        </div>
                    `;
                }
            }
        });
    }

    // Berkas upload preview
    const berkasInput = document.getElementById('berkas_file');
    const berkasPreview = document.getElementById('berkasPreview');

    if (berkasInput && berkasPreview) {
        berkasInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileExtension = file.name.split('.').pop().toLowerCase();
                let iconClass = 'bi-file-earmark';
                let iconColor = 'text-secondary';

                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                    iconClass = 'bi-file-earmark-image';
                    iconColor = 'text-success';
                } else if (fileExtension === 'pdf') {
                    iconClass = 'bi-file-earmark-pdf';
                    iconColor = 'text-danger';
                } else if (['doc', 'docx'].includes(fileExtension)) {
                    iconClass = 'bi-file-earmark-word';
                    iconColor = 'text-primary';
                }

                berkasPreview.innerHTML = `
                    <div class="bg-light rounded p-4">
                        <i class="${iconClass} ${iconColor}" style="font-size: 3rem;"></i>
                        <p class="mb-1 mt-2 fw-bold">${file.name}</p>
                        <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                    </div>
                `;
            }
        });
    }

    // Modal Catatan Form Validation and Enhancement
    const modalCatatanForm = document.getElementById('modalCatatanForm');
    const modalJudulInput = document.getElementById('modal_judul_catatan');
    const modalJudulCounter = document.getElementById('modalJudulCounter');
    const modalIsiTextarea = document.getElementById('modal_isi_catatan');
    const modalTanggalCatatan = document.getElementById('modal_tanggal_catatan');
    const modalTanggalTindakLanjut = document.getElementById('modal_tanggal_tindak_lanjut');

    // Character counter for modal judul
    if (modalJudulInput && modalJudulCounter) {
        modalJudulInput.addEventListener('input', function() {
            const length = this.value.length;
            modalJudulCounter.textContent = `${length}/255 karakter`;

            if (length > 200) {
                modalJudulCounter.className = 'text-warning';
            } else if (length > 240) {
                modalJudulCounter.className = 'text-danger';
            } else {
                modalJudulCounter.className = 'text-muted';
            }
        });
    }

    // Auto-resize modal textarea
    if (modalIsiTextarea) {
        modalIsiTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }

    // Modal form validation
    function validateModalForm() {
        let isValid = true;
        const alertContainer = document.getElementById('modalAlertContainer');
        alertContainer.innerHTML = '';

        // Clear previous validation states
        modalCatatanForm.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        modalCatatanForm.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

        // Validate jenis catatan
        const jenisCatatan = document.getElementById('modal_jenis_catatan');
        if (!jenisCatatan.value) {
            showModalFieldError(jenisCatatan, 'Jenis catatan harus dipilih');
            isValid = false;
        }

        // Validate judul
        if (!modalJudulInput.value.trim()) {
            showModalFieldError(modalJudulInput, 'Judul catatan harus diisi');
            isValid = false;
        } else if (modalJudulInput.value.length > 255) {
            showModalFieldError(modalJudulInput, 'Judul catatan maksimal 255 karakter');
            isValid = false;
        }

        // Validate isi catatan
        if (!modalIsiTextarea.value.trim()) {
            showModalFieldError(modalIsiTextarea, 'Isi catatan harus diisi');
            isValid = false;
        }

        // Validate tanggal
        if (!modalTanggalCatatan.value) {
            showModalFieldError(modalTanggalCatatan, 'Tanggal catatan harus diisi');
            isValid = false;
        }

        // Validate tanggal tindak lanjut (if provided)
        if (modalTanggalTindakLanjut.value && modalTanggalCatatan.value) {
            if (new Date(modalTanggalTindakLanjut.value) < new Date(modalTanggalCatatan.value)) {
                showModalFieldError(modalTanggalTindakLanjut, 'Tanggal tindak lanjut tidak boleh sebelum tanggal catatan');
                isValid = false;
            }
        }

        return isValid;
    }

    function showModalFieldError(field, message) {
        field.classList.add('is-invalid');
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        field.parentNode.appendChild(feedback);
    }

    function showModalAlert(type, message) {
        const alertContainer = document.getElementById('modalAlertContainer');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            <i class="bi bi-${type === 'danger' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alert);
    }

    // Modal form submission
    if (modalCatatanForm) {
        modalCatatanForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!validateModalForm()) {
                showModalAlert('danger', 'Mohon perbaiki kesalahan pada form');
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Menyimpan...';
            submitBtn.disabled = true;

            // Submit form
            this.submit();
        });
    }

    // Reset modals when closed
    const modals = ['uploadFotoModal', 'uploadBerkasModal', 'addCatatanModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('hidden.bs.modal', function() {
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();

                    // Reset validation states
                    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
                    form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

                    // Reset alert container
                    const alertContainer = form.querySelector('[id$="AlertContainer"]');
                    if (alertContainer) {
                        alertContainer.innerHTML = '';
                    }

                    // Reset character counter
                    if (modalId === 'addCatatanModal' && modalJudulCounter) {
                        modalJudulCounter.textContent = '0/255 karakter';
                        modalJudulCounter.className = 'text-muted';
                    }

                    // Reset previews
                    if (modalId === 'uploadFotoModal' && fotoPreview) {
                        fotoPreview.innerHTML = `
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview foto akan muncul di sini</p>
                            </div>
                        `;
                    } else if (modalId === 'uploadBerkasModal' && berkasPreview) {
                        berkasPreview.innerHTML = `
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview file akan muncul di sini</p>
                            </div>
                        `;
                    }
                }
            });
        }
    });
});

// Delete functions
function deleteFoto(siswaId) {
    if (confirm('Apakah Anda yakin ingin menghapus foto siswa ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/siswa-app/public/siswa/delete-foto/${siswaId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= $csrf_token ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteBerkas(berkasId) {
    if (confirm('Apakah Anda yakin ingin menghapus berkas ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/siswa-app/public/berkas/delete/${berkasId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= $csrf_token ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteCatatan(catatanId) {
    if (confirm('Apakah Anda yakin ingin menghapus catatan ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/siswa-app/public/catatan/delete/${catatanId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= $csrf_token ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function editCatatan(catatanId) {
    // Redirect to edit page
    window.location.href = `/siswa-app/public/catatan/edit/${catatanId}`;
}

// Form submission with loading state
document.addEventListener('submit', function(e) {
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');

    if (submitBtn) {
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Memproses...';

        // Reset after 10 seconds if still loading
        setTimeout(() => {
            if (submitBtn.classList.contains('loading')) {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }, 10000);
    }
});
</script>
