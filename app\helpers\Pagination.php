<?php
/**
 * Pagination Helper
 * Helper untuk menangani pagination di seluruh aplikasi
 */

class Pagination {
    
    /**
     * Calculate pagination data
     * 
     * @param int $totalRecords Total number of records
     * @param int $currentPage Current page number (1-based)
     * @param int $recordsPerPage Number of records per page
     * @return array Pagination data
     */
    public static function calculate($totalRecords, $currentPage = 1, $recordsPerPage = 10) {
        // Ensure minimum values
        $totalRecords = max(0, (int)$totalRecords);
        $currentPage = max(1, (int)$currentPage);
        $recordsPerPage = max(1, (int)$recordsPerPage);
        
        // Calculate total pages
        $totalPages = ceil($totalRecords / $recordsPerPage);
        
        // Ensure current page is within bounds
        $currentPage = min($currentPage, max(1, $totalPages));
        
        // Calculate offset for SQL LIMIT
        $offset = ($currentPage - 1) * $recordsPerPage;
        
        // Calculate record range
        $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
        $endRecord = min($offset + $recordsPerPage, $totalRecords);
        
        // Calculate page range for pagination links
        $pageRange = self::calculatePageRange($currentPage, $totalPages);
        
        return [
            'total_records' => $totalRecords,
            'current_page' => $currentPage,
            'total_pages' => $totalPages,
            'records_per_page' => $recordsPerPage,
            'offset' => $offset,
            'start_record' => $startRecord,
            'end_record' => $endRecord,
            'has_previous' => $currentPage > 1,
            'has_next' => $currentPage < $totalPages,
            'previous_page' => max(1, $currentPage - 1),
            'next_page' => min($totalPages, $currentPage + 1),
            'page_range' => $pageRange,
            'showing_text' => self::getShowingText($startRecord, $endRecord, $totalRecords)
        ];
    }
    
    /**
     * Calculate page range for pagination links
     * 
     * @param int $currentPage Current page
     * @param int $totalPages Total pages
     * @param int $maxLinks Maximum number of page links to show
     * @return array Array of page numbers to show
     */
    private static function calculatePageRange($currentPage, $totalPages, $maxLinks = 5) {
        if ($totalPages <= $maxLinks) {
            return range(1, $totalPages);
        }
        
        $halfLinks = floor($maxLinks / 2);
        $start = max(1, $currentPage - $halfLinks);
        $end = min($totalPages, $start + $maxLinks - 1);
        
        // Adjust start if we're near the end
        if ($end - $start + 1 < $maxLinks) {
            $start = max(1, $end - $maxLinks + 1);
        }
        
        return range($start, $end);
    }
    
    /**
     * Get showing text (e.g., "Showing 1 to 10 of 25 entries")
     * 
     * @param int $startRecord Start record number
     * @param int $endRecord End record number
     * @param int $totalRecords Total records
     * @return string Showing text
     */
    private static function getShowingText($startRecord, $endRecord, $totalRecords) {
        if ($totalRecords == 0) {
            return "Tidak ada data";
        }
        
        if ($totalRecords == 1) {
            return "Menampilkan 1 data";
        }
        
        return "Menampilkan {$startRecord} sampai {$endRecord} dari {$totalRecords} data";
    }
    
    /**
     * Generate pagination HTML
     * 
     * @param array $paginationData Pagination data from calculate()
     * @param string $baseUrl Base URL for pagination links
     * @param array $queryParams Additional query parameters
     * @return string HTML for pagination
     */
    public static function render($paginationData, $baseUrl, $queryParams = []) {
        if ($paginationData['total_pages'] <= 1) {
            return '';
        }
        
        $html = '<nav aria-label="Pagination Navigation">';
        $html .= '<ul class="pagination justify-content-center mb-0">';
        
        // Previous button
        if ($paginationData['has_previous']) {
            $prevUrl = self::buildUrl($baseUrl, array_merge($queryParams, ['page' => $paginationData['previous_page']]));
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . htmlspecialchars($prevUrl) . '" aria-label="Previous">';
            $html .= '<span aria-hidden="true"><i class="bi bi-chevron-left"></i></span>';
            $html .= '</a>';
            $html .= '</li>';
        } else {
            $html .= '<li class="page-item disabled">';
            $html .= '<span class="page-link" aria-label="Previous">';
            $html .= '<span aria-hidden="true"><i class="bi bi-chevron-left"></i></span>';
            $html .= '</span>';
            $html .= '</li>';
        }
        
        // First page link (if not in range)
        if (!in_array(1, $paginationData['page_range']) && $paginationData['total_pages'] > 1) {
            $firstUrl = self::buildUrl($baseUrl, array_merge($queryParams, ['page' => 1]));
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . htmlspecialchars($firstUrl) . '">1</a>';
            $html .= '</li>';
            
            if ($paginationData['page_range'][0] > 2) {
                $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Page number links
        foreach ($paginationData['page_range'] as $page) {
            $pageUrl = self::buildUrl($baseUrl, array_merge($queryParams, ['page' => $page]));
            $isActive = $page == $paginationData['current_page'];
            
            $html .= '<li class="page-item' . ($isActive ? ' active' : '') . '">';
            if ($isActive) {
                $html .= '<span class="page-link">' . $page . '</span>';
            } else {
                $html .= '<a class="page-link" href="' . htmlspecialchars($pageUrl) . '">' . $page . '</a>';
            }
            $html .= '</li>';
        }
        
        // Last page link (if not in range)
        $lastPageInRange = end($paginationData['page_range']);
        if ($lastPageInRange < $paginationData['total_pages']) {
            if ($lastPageInRange < $paginationData['total_pages'] - 1) {
                $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            
            $lastUrl = self::buildUrl($baseUrl, array_merge($queryParams, ['page' => $paginationData['total_pages']]));
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . htmlspecialchars($lastUrl) . '">' . $paginationData['total_pages'] . '</a>';
            $html .= '</li>';
        }
        
        // Next button
        if ($paginationData['has_next']) {
            $nextUrl = self::buildUrl($baseUrl, array_merge($queryParams, ['page' => $paginationData['next_page']]));
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . htmlspecialchars($nextUrl) . '" aria-label="Next">';
            $html .= '<span aria-hidden="true"><i class="bi bi-chevron-right"></i></span>';
            $html .= '</a>';
            $html .= '</li>';
        } else {
            $html .= '<li class="page-item disabled">';
            $html .= '<span class="page-link" aria-label="Next">';
            $html .= '<span aria-hidden="true"><i class="bi bi-chevron-right"></i></span>';
            $html .= '</span>';
            $html .= '</li>';
        }
        
        $html .= '</ul>';
        $html .= '</nav>';
        
        return $html;
    }
    
    /**
     * Build URL with query parameters
     * 
     * @param string $baseUrl Base URL
     * @param array $params Query parameters
     * @return string Complete URL
     */
    private static function buildUrl($baseUrl, $params = []) {
        if (empty($params)) {
            return $baseUrl;
        }
        
        $queryString = http_build_query($params);
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        
        return $baseUrl . $separator . $queryString;
    }
    
    /**
     * Get current page from request
     * 
     * @param int $default Default page number
     * @return int Current page number
     */
    public static function getCurrentPage($default = 1) {
        $page = $_GET['page'] ?? $default;
        return max(1, (int)$page);
    }
    
    /**
     * Get records per page from request or use default
     * 
     * @param int $default Default records per page
     * @param array $allowedValues Allowed values for records per page
     * @return int Records per page
     */
    public static function getRecordsPerPage($default = 10, $allowedValues = [10, 25, 50, 100]) {
        $perPage = $_GET['per_page'] ?? $default;
        $perPage = (int)$perPage;
        
        return in_array($perPage, $allowedValues) ? $perPage : $default;
    }
    
    /**
     * Render records per page selector
     * 
     * @param int $currentPerPage Current records per page
     * @param string $baseUrl Base URL
     * @param array $queryParams Additional query parameters
     * @param array $options Available options
     * @return string HTML for per page selector
     */
    public static function renderPerPageSelector($currentPerPage, $baseUrl, $queryParams = [], $options = [10, 25, 50, 100]) {
        $html = '<div class="d-flex align-items-center">';
        $html .= '<label class="form-label me-2 mb-0">Tampilkan:</label>';
        $html .= '<select class="form-select form-select-sm" style="width: auto;" onchange="window.location.href=this.value">';
        
        foreach ($options as $option) {
            $url = self::buildUrl($baseUrl, array_merge($queryParams, ['per_page' => $option, 'page' => 1]));
            $selected = $option == $currentPerPage ? ' selected' : '';
            $html .= '<option value="' . htmlspecialchars($url) . '"' . $selected . '>' . $option . '</option>';
        }
        
        $html .= '</select>';
        $html .= '<span class="ms-2 text-muted">per halaman</span>';
        $html .= '</div>';
        
        return $html;
    }
}
?>
