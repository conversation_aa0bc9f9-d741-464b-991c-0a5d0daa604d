-- Script untuk mengubah field 'jurusan' men<PERSON><PERSON> 'kurikulum' di tabel kelas
-- File: database/change_jurusan_to_kurikulum.sql

-- 1. Backup data jurusan yang ada (optional, untuk safety)
CREATE TABLE IF NOT EXISTS backup_kelas_jurusan AS 
SELECT id, nama_kelas, jurusan, created_at 
FROM kelas 
WHERE jurusan IS NOT NULL;

-- 2. Tambah kolom kurikulum baru dengan ENUM values
ALTER TABLE kelas 
ADD COLUMN kurikulum ENUM(
    'Kurikulum Seminari',
    'Kurikulum K13', 
    'Kurikulum Merdeka',
    'Kurikulum Deep Learning'
) AFTER tingkat;

-- 3. Migrasi data dari jurusan ke kurikulum (mapping logic)
-- Mapping berdasarkan nilai jurusan yang ada:
-- IPA -> Kurikulum K13 (asumsi)
-- IPS -> Kurikulum K13 (asumsi) 
-- Bahasa -> Kurikulum Merdeka (asumsi)
-- Umum -> Kurikulum Seminari (asumsi)

UPDATE kelas SET kurikulum = 
    CASE 
        WHEN jurusan = 'IPA' THEN 'Kurikulum K13'
        WHEN jurusan = 'IPS' THEN 'Kurikulum K13'
        WHEN jurusan = 'Bahasa' THEN 'Kurikulum Merdeka'
        WHEN jurusan = 'Umum' THEN 'Kurikulum Seminari'
        WHEN jurusan IS NULL OR jurusan = '' THEN 'Kurikulum K13'
        ELSE 'Kurikulum K13'
    END
WHERE kurikulum IS NULL;

-- 4. Hapus kolom jurusan lama (setelah memastikan data sudah dimigrasikan)
-- UNCOMMENT baris di bawah setelah yakin migrasi berhasil:
-- ALTER TABLE kelas DROP COLUMN jurusan;

-- 5. Tambahkan index untuk performa
ALTER TABLE kelas ADD INDEX idx_kurikulum (kurikulum);

-- 6. Update komentar kolom untuk dokumentasi
ALTER TABLE kelas 
MODIFY COLUMN kurikulum ENUM(
    'Kurikulum Seminari',
    'Kurikulum K13', 
    'Kurikulum Merdeka',
    'Kurikulum Deep Learning'
) COMMENT 'Jenis kurikulum yang digunakan kelas';

-- 7. Tampilkan hasil migrasi
SELECT 
    'Migration Summary' as info,
    COUNT(*) as total_kelas,
    COUNT(CASE WHEN kurikulum = 'Kurikulum Seminari' THEN 1 END) as seminari,
    COUNT(CASE WHEN kurikulum = 'Kurikulum K13' THEN 1 END) as k13,
    COUNT(CASE WHEN kurikulum = 'Kurikulum Merdeka' THEN 1 END) as merdeka,
    COUNT(CASE WHEN kurikulum = 'Kurikulum Deep Learning' THEN 1 END) as deep_learning
FROM kelas;

-- 8. Tampilkan sample data hasil migrasi
SELECT id, nama_kelas, tingkat, kurikulum, tahun_pelajaran, jurusan as old_jurusan
FROM kelas 
ORDER BY tingkat, nama_kelas 
LIMIT 10;
