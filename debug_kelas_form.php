<?php
/**
 * Debug Script untuk Form Kelas
 * Script ini akan membantu debug mengapa form tambah kelas gagal
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/helpers/Security.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 Debug Form Kelas</h1>";

try {
    $db = new Database();
    $kelasModel = new Kelas();
    
    echo "<h2>📋 Database Connection Test</h2>";
    
    // Test database connection
    try {
        $testQuery = $db->fetch("SELECT 1 as test");
        echo "<p style='color: green;'>✅ Database connection successful</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        exit;
    }
    
    echo "<h2>🗄️ Table Structure Check</h2>";
    
    // Check kelas table structure
    try {
        $tableStructure = $db->fetchAll("SHOW COLUMNS FROM kelas");
        echo "<h3>Kelas Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th>";
        echo "</tr>";
        
        foreach ($tableStructure as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking table structure: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🧪 Test Insert Query</h2>";
    
    // Test the exact insert query that would be used
    $testData = [
        'nama_kelas' => 'TEST-CLASS',
        'tingkat' => 'X',
        'kurikulum' => 'Kurikulum K13',
        'tahun_pelajaran' => '2024/2025',
        'wali_kelas' => 'Test Wali Kelas',
        'kapasitas' => 30,
        'created_by' => 1
    ];
    
    echo "<h3>Test Data:</h3>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    $sql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    echo "<h3>SQL Query:</h3>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    $params = [
        $testData['nama_kelas'],
        $testData['tingkat'],
        $testData['kurikulum'],
        $testData['tahun_pelajaran'],
        $testData['wali_kelas'],
        $testData['kapasitas'],
        $testData['created_by']
    ];
    
    echo "<h3>Parameters:</h3>";
    echo "<pre>" . print_r($params, true) . "</pre>";
    
    // Try to execute the query
    try {
        // First, check if test class already exists
        $existingTest = $db->fetch("SELECT id FROM kelas WHERE nama_kelas = ?", ['TEST-CLASS']);
        if ($existingTest) {
            echo "<p style='color: orange;'>⚠️ Test class already exists, deleting first...</p>";
            $db->query("DELETE FROM kelas WHERE nama_kelas = ?", ['TEST-CLASS']);
        }
        
        $db->query($sql, $params);
        $insertId = $db->lastInsertId();
        
        echo "<p style='color: green;'>✅ Test insert successful! Insert ID: {$insertId}</p>";
        
        // Verify the inserted data
        $insertedData = $db->fetch("SELECT * FROM kelas WHERE id = ?", [$insertId]);
        echo "<h3>Inserted Data:</h3>";
        echo "<pre>" . print_r($insertedData, true) . "</pre>";
        
        // Clean up test data
        $db->query("DELETE FROM kelas WHERE id = ?", [$insertId]);
        echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Test insert failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>Error Details:</strong></p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>🔐 Security Check</h2>";
    
    // Test Security class
    try {
        $testToken = Security::generateCSRFToken();
        echo "<p style='color: green;'>✅ CSRF token generation successful</p>";
        
        $isValid = Security::verifyCSRFToken($testToken);
        echo "<p style='color: green;'>✅ CSRF token verification successful: " . ($isValid ? 'Valid' : 'Invalid') . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Security check failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>📝 Session Check</h2>";
    
    // Check session data
    echo "<h3>Session Data:</h3>";
    if (isset($_SESSION['user'])) {
        echo "<p><strong>User ID:</strong> " . ($_SESSION['user']['id'] ?? 'Not set') . "</p>";
        echo "<p><strong>Username:</strong> " . ($_SESSION['user']['username'] ?? 'Not set') . "</p>";
        echo "<p><strong>Role:</strong> " . ($_SESSION['user']['role'] ?? 'Not set') . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No user session found</p>";
    }
    
    if (isset($_SESSION['user_id'])) {
        echo "<p><strong>Legacy User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No legacy user_id in session</p>";
    }
    
    echo "<h2>🧪 Test Model Method</h2>";
    
    // Test the createKelas method directly
    try {
        $testModelData = [
            'nama_kelas' => 'MODEL-TEST',
            'tingkat' => 'XI',
            'kurikulum' => 'Kurikulum Merdeka',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Model Test Wali',
            'kapasitas' => 25,
            'created_by' => 1
        ];
        
        echo "<h3>Testing Model createKelas Method:</h3>";
        echo "<pre>" . print_r($testModelData, true) . "</pre>";
        
        $result = $kelasModel->createKelas($testModelData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Model createKelas successful! Result: {$result}</p>";
            
            // Verify and clean up
            $createdClass = $db->fetch("SELECT * FROM kelas WHERE id = ?", [$result]);
            echo "<h3>Created Class Data:</h3>";
            echo "<pre>" . print_r($createdClass, true) . "</pre>";
            
            // Clean up
            $db->query("DELETE FROM kelas WHERE id = ?", [$result]);
            echo "<p style='color: blue;'>🧹 Model test data cleaned up</p>";
            
        } else {
            echo "<p style='color: red;'>❌ Model createKelas failed</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Model test failed: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>📋 Form Validation Test</h2>";
    
    // Simulate form data
    $formData = [
        'nama_kelas' => 'X-IPA-1',
        'tingkat' => 'X',
        'kurikulum' => 'Kurikulum K13',
        'tahun_pelajaran' => '2024/2025',
        'wali_kelas' => 'Budi Santoso',
        'kapasitas' => '30'
    ];
    
    echo "<h3>Simulated Form Data:</h3>";
    echo "<pre>" . print_r($formData, true) . "</pre>";
    
    // Test validation logic
    $nama_kelas = Security::sanitizeInput($formData['nama_kelas']);
    $tingkat = Security::sanitizeInput($formData['tingkat']);
    $tahun_pelajaran = Security::sanitizeInput($formData['tahun_pelajaran']);
    
    echo "<h3>Validation Results:</h3>";
    echo "<p><strong>Nama Kelas:</strong> '{$nama_kelas}' - " . ($nama_kelas ? '✅ Valid' : '❌ Invalid') . "</p>";
    echo "<p><strong>Tingkat:</strong> '{$tingkat}' - " . ($tingkat ? '✅ Valid' : '❌ Invalid') . "</p>";
    echo "<p><strong>Tahun Pelajaran:</strong> '{$tahun_pelajaran}' - " . ($tahun_pelajaran ? '✅ Valid' : '❌ Invalid') . "</p>";
    
    $isValid = $nama_kelas && $tingkat && $tahun_pelajaran;
    echo "<p><strong>Overall Validation:</strong> " . ($isValid ? '✅ Valid' : '❌ Invalid') . "</p>";
    
    echo "<h2>🔍 Possible Issues</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔍 Common Issues to Check:</h4>";
    echo "<ol>";
    echo "<li><strong>Database Connection:</strong> Check if database is accessible</li>";
    echo "<li><strong>Table Structure:</strong> Ensure all required columns exist</li>";
    echo "<li><strong>Data Types:</strong> Check if data types match table schema</li>";
    echo "<li><strong>Constraints:</strong> Check for unique constraints or foreign keys</li>";
    echo "<li><strong>Permissions:</strong> Check database user permissions</li>";
    echo "<li><strong>Session Data:</strong> Ensure user session is properly set</li>";
    echo "<li><strong>CSRF Token:</strong> Verify token generation and validation</li>";
    echo "<li><strong>Error Logging:</strong> Check server error logs</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/kelas/create' target='_blank' style='color: blue;'>🔗 Try Create Class Form Again</a></p>";
    echo "<p><a href='/siswa-app/public/kelas' target='_blank' style='color: blue;'>🔗 View Class List</a></p>";
    
    echo "<h2>📊 Summary</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Debug Summary:</h4>";
    echo "<ul>";
    echo "<li>Database connection tested</li>";
    echo "<li>Table structure verified</li>";
    echo "<li>Insert query tested</li>";
    echo "<li>Model method tested</li>";
    echo "<li>Security functions tested</li>";
    echo "<li>Form validation tested</li>";
    echo "</ul>";
    echo "<p>If all tests pass but form still fails, check browser console for JavaScript errors and server error logs.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Debug Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
