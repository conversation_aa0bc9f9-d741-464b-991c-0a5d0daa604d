<?php
/**
 * Script untuk menguji pengelompokan catatan yang sudah diperbaiki
 */

require_once __DIR__ . '/app/models/CatatanSiswa.php';

try {
    echo "<h2>Testing Pengelompokan Catatan</h2>";
    
    $catatanModel = new CatatanSiswa();
    
    // Test dengan siswa ID 19 (yang ada di screenshot)
    $siswaId = 19;
    
    echo "<h3>1. Pengelompokan Normal (untuk tampilan accordion):</h3>";
    $grouped = $catatanModel->getGroupedBySiswaId($siswaId);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Kate<PERSON>i</th><th>Jumlah Catatan</th><th>Detail</th></tr>";
    
    foreach ($grouped as $category => $catatanList) {
        echo "<tr>";
        echo "<td><strong>{$category}</strong></td>";
        echo "<td>" . count($catatanList) . "</td>";
        echo "<td>";
        if (!empty($catatanList)) {
            foreach ($catatanList as $catatan) {
                echo "- {$catatan['jenis_catatan']}: {$catatan['judul_catatan']}<br>";
            }
        } else {
            echo "<em>Tidak ada catatan</em>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>2. Pengelompokan Detail (untuk statistik):</h3>";
    $groupedDetailed = $catatanModel->getGroupedBySiswaIdDetailed($siswaId);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Kategori</th><th>Jumlah Catatan</th><th>Detail</th></tr>";
    
    foreach ($groupedDetailed as $category => $catatanList) {
        echo "<tr>";
        echo "<td><strong>{$category}</strong></td>";
        echo "<td>" . count($catatanList) . "</td>";
        echo "<td>";
        if (!empty($catatanList)) {
            foreach ($catatanList as $catatan) {
                echo "- {$catatan['jenis_catatan']}: {$catatan['judul_catatan']}<br>";
            }
        } else {
            echo "<em>Tidak ada catatan</em>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>3. Statistik Detail:</h3>";
    $stats = $catatanModel->getDetailedStatistics($siswaId);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metrik</th><th>Nilai</th></tr>";
    echo "<tr><td>Total Catatan</td><td>{$stats['total_catatan']}</td></tr>";
    echo "<tr><td>Total Prestasi</td><td>{$stats['total_prestasi']}</td></tr>";
    echo "<tr><td>Total Pelanggaran</td><td>{$stats['total_pelanggaran']}</td></tr>";
    echo "<tr><td>Total Konseling</td><td>{$stats['total_konseling']}</td></tr>";
    echo "<tr><td>Total Bimbingan</td><td>{$stats['total_bimbingan']}</td></tr>";
    echo "</table>";
    
    echo "<h4>Statistik per Kategori:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Kategori</th><th>Jumlah</th></tr>";
    foreach ($stats['by_category'] as $category => $count) {
        echo "<tr><td>{$category}</td><td>{$count}</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>4. Semua Catatan Siswa:</h3>";
    $allCatatan = $catatanModel->getBySiswaId($siswaId);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Jenis</th><th>Judul</th><th>Tanggal</th></tr>";
    
    foreach ($allCatatan as $catatan) {
        echo "<tr>";
        echo "<td>{$catatan['id']}</td>";
        echo "<td>{$catatan['jenis_catatan']}</td>";
        echo "<td>{$catatan['judul_catatan']}</td>";
        echo "<td>" . date('d/m/Y', strtotime($catatan['tanggal_catatan'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>✅ Hasil Pengujian:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Yang Diperbaiki:</h4>";
    echo "<ul>";
    echo "<li><strong>Accordion di tab Catatan Siswa</strong>: Sekarang hanya menampilkan 3 kategori utama (Pamong, Wali Kelas, BK)</li>";
    echo "<li><strong>Statistik tetap akurat</strong>: Prestasi, Pelanggaran, dan Konseling masih dihitung dengan benar</li>";
    echo "<li><strong>Form tambah catatan</strong>: Tetap menampilkan pengelompokan detail untuk kemudahan pemilihan</li>";
    echo "</ul>";
    
    echo "<h4>Pengelompokan Accordion:</h4>";
    echo "<ul>";
    foreach ($grouped as $category => $catatanList) {
        if (!empty($catatanList)) {
            echo "<li><strong>" . ucfirst(str_replace('_', ' ', $category)) . "</strong>: " . count($catatanList) . " catatan</li>";
        }
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🎯 Kesimpulan:</h3>";
    echo "<p>Sekarang sistem menampilkan:</p>";
    echo "<ul>";
    echo "<li><strong>Di Accordion</strong>: Hanya kategori utama (Pamong, Wali Kelas, BK)</li>";
    echo "<li><strong>Di Statistik</strong>: Detail prestasi, pelanggaran, konseling tetap akurat</li>";
    echo "<li><strong>Di Form</strong>: Pengelompokan detail untuk kemudahan pemilihan</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
