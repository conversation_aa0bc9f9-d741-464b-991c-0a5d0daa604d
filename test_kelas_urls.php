<?php
/**
 * Test Kelas URLs
 * Script ini akan test semua URL untuk kelas
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/models/Kelas.php';

echo "<h1>🔗 Test Kelas URLs</h1>";

try {
    $db = new Database();
    $kelasModel = new Kelas();
    
    // Get a test class
    $testClasses = $kelasModel->getAll('2024/2025');
    
    if (empty($testClasses)) {
        echo "<p style='color: red;'>❌ No classes found to test with</p>";
        
        // Create a test class
        $testData = [
            'nama_kelas' => 'URL-TEST-' . date('His'),
            'tingkat' => 'X',
            'kurikulum' => 'Kurikulum K13',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'URL Test Teacher',
            'kapasitas' => 30,
            'created_by' => 1
        ];
        
        $testId = $kelasModel->createKelas($testData);
        if ($testId) {
            echo "<p style='color: green;'>✅ Created test class with ID: {$testId}</p>";
            $testClass = $kelasModel->getById($testId);
            $testClasses = [$testClass];
        } else {
            echo "<p style='color: red;'>❌ Failed to create test class</p>";
            exit;
        }
    }
    
    $testClass = $testClasses[0];
    $classId = $testClass['id_kelas'];
    
    echo "<h2>📋 Test Class Information</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Test Class:</strong> {$testClass['nama_kelas']}</p>";
    echo "<p><strong>ID:</strong> {$classId}</p>";
    echo "<p><strong>Tingkat:</strong> {$testClass['tingkat']}</p>";
    echo "</div>";
    
    echo "<h2>🧪 URL Tests</h2>";
    
    $urls = [
        'List Classes' => '/siswa-app/public/kelas',
        'Create Class' => '/siswa-app/public/kelas/create',
        'View Detail' => "/siswa-app/public/kelas/detail/{$classId}",
        'Edit Class' => "/siswa-app/public/kelas/edit/{$classId}",
        'Delete Class' => "/siswa-app/public/kelas/delete/{$classId}"
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Function</th><th>URL</th><th>Test</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($urls as $function => $url) {
        echo "<tr>";
        echo "<td><strong>{$function}</strong></td>";
        echo "<td><code>{$url}</code></td>";
        echo "<td>";
        
        if ($function === 'Delete Class') {
            echo "<button onclick=\"testDelete('{$url}')\" style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Test Delete</button>";
        } else {
            echo "<a href='{$url}' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test URL</a>";
        }
        
        echo "</td>";
        echo "<td>";
        
        // Test URL accessibility
        $testResult = testUrlAccessibility($url);
        if ($testResult['accessible']) {
            echo "<span style='color: green;'>✅ Accessible</span>";
        } else {
            echo "<span style='color: red;'>❌ " . $testResult['error'] . "</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔍 Routing Analysis</h2>";
    
    // Check routing file
    $routingFile = __DIR__ . '/public/index.php';
    if (file_exists($routingFile)) {
        echo "<p style='color: green;'>✅ Routing file exists</p>";
        
        $routingContent = file_get_contents($routingFile);
        
        // Check for kelas routing
        if (strpos($routingContent, "case 'kelas':") !== false) {
            echo "<p style='color: green;'>✅ Kelas routing case found</p>";
            
            // Extract kelas routing section
            $kelasStart = strpos($routingContent, "case 'kelas':");
            $kelasEnd = strpos($routingContent, "break;", $kelasStart);
            $kelasRouting = substr($routingContent, $kelasStart, $kelasEnd - $kelasStart + 6);
            
            echo "<h3>Kelas Routing Code:</h3>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars($kelasRouting);
            echo "</pre>";
            
            // Check for specific actions
            $actions = ['edit', 'detail', 'delete'];
            foreach ($actions as $action) {
                if (strpos($kelasRouting, $action) !== false) {
                    echo "<p style='color: green;'>✅ {$action} action found in routing</p>";
                } else {
                    echo "<p style='color: red;'>❌ {$action} action NOT found in routing</p>";
                }
            }
            
        } else {
            echo "<p style='color: red;'>❌ Kelas routing case NOT found</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Routing file not found</p>";
    }
    
    echo "<h2>🔧 Controller Analysis</h2>";
    
    // Check controller file
    $controllerFile = __DIR__ . '/app/controllers/KelasController.php';
    if (file_exists($controllerFile)) {
        echo "<p style='color: green;'>✅ Controller file exists</p>";
        
        $controllerContent = file_get_contents($controllerFile);
        
        // Check for methods
        $methods = ['edit', 'detail', 'delete'];
        foreach ($methods as $method) {
            if (strpos($controllerContent, "public function {$method}") !== false) {
                echo "<p style='color: green;'>✅ {$method}() method found in controller</p>";
            } else {
                echo "<p style='color: red;'>❌ {$method}() method NOT found in controller</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Controller file not found</p>";
    }
    
    echo "<h2>📊 Manual Testing Guide</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 Step-by-Step Testing:</h4>";
    echo "<ol>";
    echo "<li><strong>Test List:</strong> Click the 'Test URL' button for 'List Classes'</li>";
    echo "<li><strong>Test Create:</strong> Click the 'Test URL' button for 'Create Class'</li>";
    echo "<li><strong>Test Detail:</strong> Click the 'Test URL' button for 'View Detail'</li>";
    echo "<li><strong>Test Edit:</strong> Click the 'Test URL' button for 'Edit Class'</li>";
    echo "<li><strong>Test Delete:</strong> Click the 'Test Delete' button (be careful!)</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🐛 Debugging Tips</h2>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ If URLs don't work:</h4>";
    echo "<ol>";
    echo "<li><strong>Check Browser Console:</strong> F12 → Console for JavaScript errors</li>";
    echo "<li><strong>Check Network Tab:</strong> F12 → Network to see HTTP status codes</li>";
    echo "<li><strong>Check Server Logs:</strong> Look for PHP errors in server logs</li>";
    echo "<li><strong>Check .htaccess:</strong> Ensure URL rewriting is working</li>";
    echo "<li><strong>Check Base Path:</strong> Ensure /siswa-app base path is correct</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ Quick Fix Suggestions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔧 Common Issues & Fixes:</h4>";
    echo "<ul>";
    echo "<li><strong>404 Not Found:</strong> Check routing configuration in index.php</li>";
    echo "<li><strong>Method Not Found:</strong> Check controller methods exist</li>";
    echo "<li><strong>Blank Page:</strong> Check for PHP errors in logs</li>";
    echo "<li><strong>Wrong Data:</strong> Check model methods and database queries</li>";
    echo "<li><strong>Permission Denied:</strong> Check authentication and authorization</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

function testUrlAccessibility($url) {
    // Simple accessibility test
    $fullUrl = "http://localhost" . $url;
    
    // Use cURL to test if URL is accessible
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $fullUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['accessible' => false, 'error' => 'cURL Error: ' . $error];
    }
    
    if ($httpCode >= 200 && $httpCode < 400) {
        return ['accessible' => true, 'code' => $httpCode];
    } else {
        return ['accessible' => false, 'error' => 'HTTP ' . $httpCode];
    }
}

echo "<script>";
echo "function testDelete(url) {";
echo "    if (confirm('⚠️ WARNING: This will actually delete the class!\\n\\nAre you sure you want to proceed?')) {";
echo "        window.open(url, '_blank');";
echo "    }";
echo "}";
echo "</script>";
?>
