<?php
/**
 * Script untuk memperbarui kategori catatan dengan nama yang lebih jelas
 */

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>Memperbarui Kategori Catatan</h2>";
    
    $db = new Database();
    
    // Cek kategori yang ada
    echo "<p>1. Memeriksa kategori yang ada...</p>";
    $existingCategories = $db->fetchAll("SELECT * FROM kategori_catatan ORDER BY kode_kategori");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Kode</th><th>Nama</th><th>Deskripsi</th><th>Warna</th><th>Icon</th></tr>";
    foreach ($existingCategories as $cat) {
        echo "<tr>";
        echo "<td>{$cat['kode_kategori']}</td>";
        echo "<td>{$cat['nama_kategori']}</td>";
        echo "<td>{$cat['deskripsi']}</td>";
        echo "<td>{$cat['warna_badge']}</td>";
        echo "<td>{$cat['icon_class']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Update kategori dengan nama yang lebih jelas
    echo "<p>2. Memperbarui nama kategori...</p>";
    
    $updates = [
        'bk_konseling' => [
            'nama_kategori' => 'Konseling BK',
            'deskripsi' => 'Catatan konseling dan bimbingan dari guru BK',
            'warna_badge' => '#17a2b8',
            'icon_class' => 'bi-heart'
        ],
        'bk_prestasi' => [
            'nama_kategori' => 'Prestasi (BK)',
            'deskripsi' => 'Catatan prestasi siswa yang dicatat oleh guru BK',
            'warna_badge' => '#28a745',
            'icon_class' => 'bi-award'
        ],
        'bk_pelanggaran' => [
            'nama_kategori' => 'Pelanggaran (BK)',
            'deskripsi' => 'Catatan pelanggaran siswa yang ditangani oleh guru BK',
            'warna_badge' => '#dc3545',
            'icon_class' => 'bi-exclamation-triangle'
        ],
        'bk_lainnya' => [
            'nama_kategori' => 'Catatan BK Lainnya',
            'deskripsi' => 'Catatan lainnya dari guru BK yang tidak termasuk kategori khusus',
            'warna_badge' => '#6c757d',
            'icon_class' => 'bi-chat-dots'
        ]
    ];
    
    foreach ($updates as $kode => $data) {
        try {
            $sql = "UPDATE kategori_catatan SET 
                    nama_kategori = ?, 
                    deskripsi = ?, 
                    warna_badge = ?, 
                    icon_class = ? 
                    WHERE kode_kategori = ?";
            
            $params = [
                $data['nama_kategori'],
                $data['deskripsi'],
                $data['warna_badge'],
                $data['icon_class'],
                $kode
            ];
            
            $result = $db->query($sql, $params);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Updated: {$kode} -> {$data['nama_kategori']}</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to update: {$kode}</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error updating {$kode}: {$e->getMessage()}</p>";
        }
    }
    
    // Tambahkan kategori yang mungkin belum ada
    echo "<p>3. Menambahkan kategori yang belum ada...</p>";
    
    $newCategories = [
        ['bk_konseling', 'Konseling BK', 'Catatan konseling dan bimbingan dari guru BK', '#17a2b8', 'bi-heart'],
        ['bk_prestasi', 'Prestasi (BK)', 'Catatan prestasi siswa yang dicatat oleh guru BK', '#28a745', 'bi-award'],
        ['bk_pelanggaran', 'Pelanggaran (BK)', 'Catatan pelanggaran siswa yang ditangani oleh guru BK', '#dc3545', 'bi-exclamation-triangle'],
        ['bk_lainnya', 'Catatan BK Lainnya', 'Catatan lainnya dari guru BK yang tidak termasuk kategori khusus', '#6c757d', 'bi-chat-dots']
    ];
    
    foreach ($newCategories as $cat) {
        try {
            $sql = "INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES (?, ?, ?, ?, ?)";
            $result = $db->query($sql, $cat);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Ensured category exists: {$cat[1]}</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Category {$cat[1]} already exists or error: {$e->getMessage()}</p>";
        }
    }
    
    // Tampilkan hasil akhir
    echo "<p>4. Kategori setelah update:</p>";
    $updatedCategories = $db->fetchAll("SELECT * FROM kategori_catatan WHERE kode_kategori LIKE 'bk_%' ORDER BY kode_kategori");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Kode</th><th>Nama</th><th>Deskripsi</th><th>Warna</th><th>Icon</th></tr>";
    foreach ($updatedCategories as $cat) {
        echo "<tr>";
        echo "<td>{$cat['kode_kategori']}</td>";
        echo "<td><strong>{$cat['nama_kategori']}</strong></td>";
        echo "<td>{$cat['deskripsi']}</td>";
        echo "<td><span style='color: {$cat['warna_badge']};'>●</span> {$cat['warna_badge']}</td>";
        echo "<td><i class='bi {$cat['icon_class']}'></i> {$cat['icon_class']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test pengelompokan
    echo "<p>5. Testing pengelompokan kategori...</p>";
    
    require_once __DIR__ . '/app/models/CatatanSiswa.php';
    $catatanModel = new CatatanSiswa();
    
    echo "<h4>Pengelompokan Normal:</h4>";
    $grouped = $catatanModel->getCategoriesGrouped();
    foreach ($grouped as $group => $categories) {
        echo "<p><strong>{$group}:</strong> " . count($categories) . " kategori</p>";
        foreach ($categories as $cat) {
            echo "<li>{$cat['nama_kategori']}</li>";
        }
    }
    
    echo "<h4>Pengelompokan Detail:</h4>";
    $groupedDetailed = $catatanModel->getCategoriesGroupedDetailed();
    foreach ($groupedDetailed as $group => $categories) {
        if (!empty($categories)) {
            echo "<p><strong>{$group}:</strong> " . count($categories) . " kategori</p>";
            foreach ($categories as $cat) {
                echo "<li>{$cat['nama_kategori']}</li>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>✅ Update kategori selesai!</h3>";
    echo "<p>Sekarang form tambah catatan akan menampilkan pengelompokan yang lebih jelas.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
