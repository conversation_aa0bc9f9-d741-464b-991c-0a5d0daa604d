<?php
/**
 * Script Test untuk Verifikasi Role Spesifik Wali Kelas
 * 
 * Test apakah sistem role spesifik (wali_kelas_x_1, wali_kelas_kpp_a, dll) berfungsi dengan benar
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/Siswa.php';
require_once __DIR__ . '/app/models/Kelas.php';
require_once __DIR__ . '/app/helpers/Security.php';

echo "<h1>🧪 Test Role Spesifik Wali Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Test yang Akan Dilakukan</h3>";
    echo "<ul>";
    echo "<li>Verifikasi role spesifik sudah ada di database</li>";
    echo "<li>Test login simulasi untuk setiap role wali kelas</li>";
    echo "<li>Test akses data siswa berdasarkan role spesifik</li>";
    echo "<li>Test akses data kelas berdasarkan role spesifik</li>";
    echo "<li>Verifikasi Security helper methods</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 1: Verifikasi role spesifik di database
    echo "<h2>📋 Test 1: Verifikasi Role Spesifik</h2>";
    
    $stmt = $pdo->query("
        SELECT role, COUNT(*) as count, GROUP_CONCAT(username) as usernames
        FROM users 
        WHERE role LIKE 'wali_kelas_%' 
        GROUP BY role 
        ORDER BY role
    ");
    $specificRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($specificRoles)) {
        echo "<p style='color: red;'>❌ Tidak ada role spesifik wali kelas ditemukan</p>";
        echo "<p>Silakan jalankan script implement_role_specific_wali_kelas.php terlebih dahulu.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Ditemukan " . count($specificRoles) . " role spesifik wali kelas</p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Role</th><th>Jumlah User</th><th>Username</th><th>Kelas Target</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($specificRoles as $role) {
        $className = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $role['role']));
        echo "<tr>";
        echo "<td><code>{$role['role']}</code></td>";
        echo "<td>{$role['count']}</td>";
        echo "<td>{$role['usernames']}</td>";
        echo "<td><strong>{$className}</strong></td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Test 2: Test Security helper methods
    echo "<h2>🔒 Test 2: Security Helper Methods</h2>";
    
    foreach ($specificRoles as $role) {
        $testRole = $role['role'];
        $expectedClass = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $testRole));
        
        // Simulasi session
        $_SESSION['user_role'] = $testRole;
        $_SESSION['user_id'] = 1; // Dummy user ID
        
        echo "<h4>Test untuk role: <code>{$testRole}</code></h4>";
        
        // Test isWaliKelas()
        $isWaliKelas = Security::isWaliKelas();
        echo "<p><strong>Security::isWaliKelas():</strong> " . ($isWaliKelas ? '✅ TRUE' : '❌ FALSE') . "</p>";
        
        // Test getWaliKelasClass()
        $kelasClass = Security::getWaliKelasClass();
        echo "<p><strong>Security::getWaliKelasClass():</strong> " . ($kelasClass ? "✅ '{$kelasClass}'" : '❌ NULL') . "</p>";
        
        // Verify class name matches expected
        if ($kelasClass === $expectedClass) {
            echo "<p style='color: green;'>✅ Class name sesuai ekspektasi: {$expectedClass}</p>";
        } else {
            echo "<p style='color: red;'>❌ Class name tidak sesuai. Expected: {$expectedClass}, Got: {$kelasClass}</p>";
        }
        
        // Test isWaliKelasFor()
        $isWaliKelasFor = Security::isWaliKelasFor($expectedClass);
        echo "<p><strong>Security::isWaliKelasFor('{$expectedClass}'):</strong> " . ($isWaliKelasFor ? '✅ TRUE' : '❌ FALSE') . "</p>";
        
        echo "<hr style='margin: 10px 0;'>";
    }
    
    // Test 3: Test akses data siswa
    echo "<h2>📚 Test 3: Akses Data Siswa per Role</h2>";
    
    $siswaModel = new Siswa();
    
    foreach ($specificRoles as $role) {
        $testRole = $role['role'];
        $expectedClass = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $testRole));
        $usernames = explode(',', $role['usernames']);
        $testUsername = trim($usernames[0]);
        
        // Get user ID
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$testUsername]);
        $userId = $stmt->fetchColumn();
        
        if (!$userId) {
            echo "<p style='color: red;'>❌ User {$testUsername} tidak ditemukan</p>";
            continue;
        }
        
        // Simulasi session untuk user ini
        $_SESSION['user_role'] = $testRole;
        $_SESSION['user_id'] = $userId;
        
        echo "<h4>Test akses siswa untuk {$testUsername} (role: {$testRole})</h4>";
        
        // Test getAllForWaliKelas()
        $siswaList = $siswaModel->getAllForWaliKelas();
        echo "<p><strong>Jumlah siswa yang dapat diakses:</strong> " . count($siswaList) . "</p>";
        
        if (!empty($siswaList)) {
            // Verify all students are from the correct class
            $correctClass = true;
            $classNames = [];
            
            foreach ($siswaList as $siswa) {
                $classNames[] = $siswa['nama_kelas'];
                if ($siswa['nama_kelas'] !== $expectedClass) {
                    $correctClass = false;
                }
            }
            
            $uniqueClasses = array_unique($classNames);
            
            if ($correctClass && count($uniqueClasses) === 1 && $uniqueClasses[0] === $expectedClass) {
                echo "<p style='color: green;'>✅ Semua siswa dari kelas yang benar: {$expectedClass}</p>";
            } else {
                echo "<p style='color: red;'>❌ Ada siswa dari kelas yang salah. Kelas ditemukan: " . implode(', ', $uniqueClasses) . "</p>";
            }
            
            // Show sample students
            echo "<p><strong>Sample siswa:</strong></p>";
            echo "<ul>";
            $count = 0;
            foreach ($siswaList as $siswa) {
                if ($count >= 3) break;
                echo "<li>{$siswa['nama_lengkap']} - {$siswa['nama_kelas']}</li>";
                $count++;
            }
            if (count($siswaList) > 3) {
                echo "<li><em>... dan " . (count($siswaList) - 3) . " siswa lainnya</em></li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada siswa yang dapat diakses (mungkin belum ada siswa di kelas {$expectedClass})</p>";
        }
        
        echo "<hr style='margin: 10px 0;'>";
    }
    
    // Test 4: Test akses data kelas
    echo "<h2>🏫 Test 4: Akses Data Kelas per Role</h2>";
    
    $kelasModel = new Kelas();
    
    foreach ($specificRoles as $role) {
        $testRole = $role['role'];
        $expectedClass = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $testRole));
        $usernames = explode(',', $role['usernames']);
        $testUsername = trim($usernames[0]);
        
        // Get user ID
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$testUsername]);
        $userId = $stmt->fetchColumn();
        
        if (!$userId) continue;
        
        // Simulasi session untuk user ini
        $_SESSION['user_role'] = $testRole;
        $_SESSION['user_id'] = $userId;
        
        echo "<h4>Test akses kelas untuk {$testUsername} (role: {$testRole})</h4>";
        
        // Test getForWaliKelas()
        $kelasList = $kelasModel->getForWaliKelas();
        echo "<p><strong>Jumlah kelas yang dapat diakses:</strong> " . count($kelasList) . "</p>";
        
        if (!empty($kelasList)) {
            // Verify all classes are correct
            $correctClass = true;
            $classNames = [];
            
            foreach ($kelasList as $kelas) {
                $classNames[] = $kelas['nama_kelas'];
                if ($kelas['nama_kelas'] !== $expectedClass) {
                    $correctClass = false;
                }
            }
            
            $uniqueClasses = array_unique($classNames);
            
            if ($correctClass && count($uniqueClasses) === 1 && $uniqueClasses[0] === $expectedClass) {
                echo "<p style='color: green;'>✅ Hanya dapat mengakses kelas yang benar: {$expectedClass}</p>";
            } else {
                echo "<p style='color: red;'>❌ Dapat mengakses kelas yang salah. Kelas ditemukan: " . implode(', ', $uniqueClasses) . "</p>";
            }
            
            // Show classes
            echo "<ul>";
            foreach ($kelasList as $kelas) {
                echo "<li>{$kelas['nama_kelas']} - {$kelas['tahun_pelajaran']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ Tidak ada kelas yang dapat diakses (mungkin kelas {$expectedClass} tidak ada)</p>";
        }
        
        echo "<hr style='margin: 10px 0;'>";
    }
    
    // Test 5: Ringkasan dan rekomendasi
    echo "<h2>📋 Test 5: Ringkasan Hasil</h2>";
    
    $totalTests = count($specificRoles) * 4; // 4 tests per role
    $passedTests = 0;
    
    // Simple scoring based on whether we found data
    foreach ($specificRoles as $role) {
        $testRole = $role['role'];
        $usernames = explode(',', $role['usernames']);
        $testUsername = trim($usernames[0]);
        
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$testUsername]);
        $userId = $stmt->fetchColumn();
        
        if ($userId) {
            $_SESSION['user_role'] = $testRole;
            $_SESSION['user_id'] = $userId;
            
            if (Security::isWaliKelas()) $passedTests++;
            if (Security::getWaliKelasClass()) $passedTests++;
            
            $siswaList = $siswaModel->getAllForWaliKelas();
            $kelasList = $kelasModel->getForWaliKelas();
            
            if (count($siswaList) >= 0) $passedTests++; // Pass if no error
            if (count($kelasList) >= 0) $passedTests++; // Pass if no error
        }
    }
    
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Statistik Test:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Role Spesifik:</strong> " . count($specificRoles) . "</li>";
    echo "<li><strong>Total Test:</strong> {$totalTests}</li>";
    echo "<li><strong>Test Passed:</strong> {$passedTests}</li>";
    echo "<li><strong>Success Rate:</strong> " . round($successRate, 1) . "%</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($successRate >= 80) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 SISTEM ROLE SPESIFIK BERFUNGSI DENGAN BAIK!</h3>";
        echo "<p>Role spesifik wali kelas sudah berhasil diimplementasikan dan berfungsi dengan benar.</p>";
        echo "<p>Setiap wali kelas hanya dapat mengakses data siswa dan kelas yang sesuai dengan role mereka.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ MASIH ADA MASALAH DENGAN SISTEM ROLE SPESIFIK</h3>";
        echo "<p>Success rate masih di bawah 80%. Silakan periksa kembali implementasi.</p>";
        echo "</div>";
    }
    
    // Login info for manual testing
    echo "<h2>🔑 Informasi Login untuk Test Manual</h2>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Username</th><th>Password</th><th>Role</th><th>Kelas yang Dikelola</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($specificRoles as $role) {
        $usernames = explode(',', $role['usernames']);
        foreach ($usernames as $username) {
            $username = trim($username);
            $expectedClass = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $role['role']));
            
            echo "<tr>";
            echo "<td><strong>{$username}</strong></td>";
            echo "<td>wali123</td>";
            echo "<td><code>{$role['role']}</code></td>";
            echo "<td>{$expectedClass}</td>";
            echo "</tr>";
        }
    }
    echo "</tbody></table>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Cara Test Manual:</h4>";
    echo "<ol>";
    echo "<li>Login dengan salah satu username di atas</li>";
    echo "<li>Password: <code>wali123</code></li>";
    echo "<li>Akses halaman Daftar Siswa</li>";
    echo "<li>Verifikasi hanya siswa dari kelas yang sesuai yang tampil</li>";
    echo "<li>Akses halaman Data Kelas</li>";
    echo "<li>Verifikasi hanya kelas yang sesuai yang tampil</li>";
    echo "</ol>";
    echo "</div>";
    
    // Reset session
    unset($_SESSION['user_id'], $_SESSION['user_role']);
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
