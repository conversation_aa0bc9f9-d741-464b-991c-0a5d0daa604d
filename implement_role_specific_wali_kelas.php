<?php
/**
 * Script untuk Implementasi Role Spesifik per Kelas
 * 
 * Mengubah sistem dari role 'wali_kelas' umum menjadi role spesifik seperti:
 * - wali_kelas_kpp_a, wali_kelas_kpp_b, wali_kelas_kpp_c
 * - wali_kelas_x_1, wali_kelas_x_2
 * - wali_kelas_xi_1, wali_kelas_xi_2
 * - wali_kelas_xii_1, wali_kelas_xii_2
 * - wali_kelas_kpa
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';

echo "<h1>🔧 Implementasi Role Spesifik per Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 Tujuan Implementasi</h3>";
    echo "<p>Mengubah sistem role dari 'wali_kelas' umum menjadi role spesifik per kelas:</p>";
    echo "<ul>";
    echo "<li><strong>wali_kelas_kpp_a</strong> → Hanya akses siswa KPP A</li>";
    echo "<li><strong>wali_kelas_x_1</strong> → Hanya akses siswa X-1</li>";
    echo "<li><strong>wali_kelas_xi_2</strong> → Hanya akses siswa XI-2</li>";
    echo "<li>Dan seterusnya...</li>";
    echo "</ul>";
    echo "</div>";
    
    // Step 1: Analisis kelas yang ada
    echo "<h2>📊 Step 1: Analisis Kelas yang Ada</h2>";
    
    $stmt = $pdo->query("
        SELECT id, nama_kelas, tingkat, tahun_pelajaran, wali_kelas, wali_kelas_id
        FROM kelas 
        WHERE is_active = 1 
        ORDER BY tingkat, nama_kelas
    ");
    $kelasList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($kelasList)) {
        echo "<p style='color: red;'>❌ Tidak ada kelas yang ditemukan</p>";
        exit;
    }
    
    echo "<p><strong>Total kelas aktif:</strong> " . count($kelasList) . "</p>";
    
    // Generate role mapping
    $roleMapping = [];
    foreach ($kelasList as $kelas) {
        $namaKelas = strtolower($kelas['nama_kelas']);
        $namaKelas = str_replace(['-', ' '], '_', $namaKelas);
        $roleMapping[$kelas['id']] = [
            'kelas_id' => $kelas['id'],
            'nama_kelas' => $kelas['nama_kelas'],
            'tingkat' => $kelas['tingkat'],
            'role_name' => 'wali_kelas_' . $namaKelas,
            'role_display' => 'Wali Kelas ' . $kelas['nama_kelas'],
            'current_wali_id' => $kelas['wali_kelas_id']
        ];
    }
    
    echo "<h4>Mapping Role yang Akan Dibuat:</h4>";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Kelas</th><th>Role Name</th><th>Role Display</th><th>Current Wali</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($roleMapping as $mapping) {
        $currentWali = $mapping['current_wali_id'] ? "ID: {$mapping['current_wali_id']}" : "Belum ada";
        echo "<tr>";
        echo "<td>{$mapping['nama_kelas']}</td>";
        echo "<td><code>{$mapping['role_name']}</code></td>";
        echo "<td>{$mapping['role_display']}</td>";
        echo "<td>{$currentWali}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Step 2: Update enum role
    echo "<h2>🔧 Step 2: Update Enum Role</h2>";
    
    // Build new enum values
    $baseRoles = ['admin', 'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu'];
    $waliKelasRoles = array_column($roleMapping, 'role_name');
    $staffRoles = ['staff'];
    
    $allRoles = array_merge($baseRoles, $waliKelasRoles, $staffRoles);
    $enumValues = "'" . implode("','", $allRoles) . "'";
    
    echo "<p>🔄 Mengupdate enum role dengan " . count($waliKelasRoles) . " role wali kelas baru...</p>";
    
    $updateEnumSQL = "ALTER TABLE users MODIFY COLUMN role ENUM({$enumValues}) DEFAULT 'staff'";
    
    try {
        $pdo->exec($updateEnumSQL);
        echo "<p style='color: green;'>✅ Enum role berhasil diupdate</p>";
        
        // Show new enum values
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
        $roleColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Enum values baru:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 0.9em;'>";
        echo htmlspecialchars($roleColumn['Type']);
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error updating enum: " . $e->getMessage() . "</p>";
        
        // Show alternative approach
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>💡 Alternatif: Manual SQL</h4>";
        echo "<p>Jika error di atas, jalankan SQL ini secara manual:</p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($updateEnumSQL);
        echo "</pre>";
        echo "</div>";
    }
    
    // Step 3: Update existing users
    echo "<h2>👥 Step 3: Update User yang Ada</h2>";
    
    $stmt = $pdo->query("SELECT id, username, nama_lengkap, role FROM users WHERE role = 'wali_kelas'");
    $existingWaliKelas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($existingWaliKelas)) {
        echo "<p>🔄 Mengupdate " . count($existingWaliKelas) . " user dengan role 'wali_kelas' lama...</p>";
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>User</th><th>Role Lama</th><th>Role Baru</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($existingWaliKelas as $user) {
            // Find kelas yang dikelola user ini
            $stmt = $pdo->prepare("SELECT id, nama_kelas FROM kelas WHERE wali_kelas_id = ? AND is_active = 1");
            $stmt->execute([$user['id']]);
            $userKelas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($userKelas)) {
                // Ambil kelas pertama (asumsi satu user satu kelas)
                $kelas = $userKelas[0];
                $newRole = $roleMapping[$kelas['id']]['role_name'] ?? null;
                
                if ($newRole) {
                    try {
                        $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
                        $stmt->execute([$newRole, $user['id']]);
                        
                        echo "<tr style='background: #d4edda;'>";
                        echo "<td>{$user['nama_lengkap']} ({$user['username']})</td>";
                        echo "<td>wali_kelas</td>";
                        echo "<td><code>{$newRole}</code></td>";
                        echo "<td style='color: green;'>✅ Berhasil</td>";
                        echo "</tr>";
                    } catch (Exception $e) {
                        echo "<tr style='background: #f8d7da;'>";
                        echo "<td>{$user['nama_lengkap']} ({$user['username']})</td>";
                        echo "<td>wali_kelas</td>";
                        echo "<td><code>{$newRole}</code></td>";
                        echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                        echo "</tr>";
                    }
                } else {
                    echo "<tr style='background: #fff3cd;'>";
                    echo "<td>{$user['nama_lengkap']} ({$user['username']})</td>";
                    echo "<td>wali_kelas</td>";
                    echo "<td>-</td>";
                    echo "<td style='color: orange;'>⚠️ Tidak ada kelas</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr style='background: #fff3cd;'>";
                echo "<td>{$user['nama_lengkap']} ({$user['username']})</td>";
                echo "<td>wali_kelas</td>";
                echo "<td>-</td>";
                echo "<td style='color: orange;'>⚠️ Tidak mengelola kelas</td>";
                echo "</tr>";
            }
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ Tidak ada user dengan role 'wali_kelas' lama</p>";
    }
    
    // Step 4: Create sample users for missing classes
    echo "<h2>👨‍🏫 Step 4: Buat User Sample untuk Kelas yang Belum Ada Wali</h2>";
    
    $kelasWithoutWali = [];
    foreach ($roleMapping as $mapping) {
        if (!$mapping['current_wali_id']) {
            $kelasWithoutWali[] = $mapping;
        }
    }
    
    if (!empty($kelasWithoutWali)) {
        echo "<p>🔄 Membuat user sample untuk " . count($kelasWithoutWali) . " kelas yang belum ada wali...</p>";
        
        $defaultPassword = password_hash('wali123', PASSWORD_DEFAULT);
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Kelas</th><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($kelasWithoutWali as $mapping) {
            $username = str_replace('wali_kelas_', 'wali_', $mapping['role_name']);
            $namaLengkap = "Pak " . ucwords(str_replace('_', ' ', str_replace('wali_kelas_', '', $mapping['role_name'])));
            $email = $username . "@sekolah.com";
            
            try {
                // Insert user
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, role, nama_lengkap, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, 1, NOW())
                ");
                $stmt->execute([$username, $email, $defaultPassword, $mapping['role_name'], $namaLengkap]);
                $newUserId = $pdo->lastInsertId();
                
                // Update kelas dengan wali baru
                $stmt = $pdo->prepare("UPDATE kelas SET wali_kelas_id = ?, wali_kelas = ? WHERE id = ?");
                $stmt->execute([$newUserId, $namaLengkap, $mapping['kelas_id']]);
                
                echo "<tr style='background: #d4edda;'>";
                echo "<td>{$mapping['nama_kelas']}</td>";
                echo "<td><strong>{$username}</strong></td>";
                echo "<td>{$namaLengkap}</td>";
                echo "<td><code>{$mapping['role_name']}</code></td>";
                echo "<td style='color: green;'>✅ Berhasil dibuat</td>";
                echo "</tr>";
                
            } catch (Exception $e) {
                echo "<tr style='background: #f8d7da;'>";
                echo "<td>{$mapping['nama_kelas']}</td>";
                echo "<td>{$username}</td>";
                echo "<td>{$namaLengkap}</td>";
                echo "<td><code>{$mapping['role_name']}</code></td>";
                echo "<td style='color: red;'>❌ Error: {$e->getMessage()}</td>";
                echo "</tr>";
            }
        }
        echo "</tbody></table>";
        
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📝 Catatan:</strong> Password default untuk semua user baru adalah: <code>wali123</code></p>";
        echo "</div>";
    } else {
        echo "<p style='color: green;'>✅ Semua kelas sudah memiliki wali kelas</p>";
    }
    
    // Step 5: Update Security helper
    echo "<h2>🔒 Step 5: Update Security Helper</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Update yang Diperlukan di app/helpers/Security.php:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo "public static function isWaliKelas() {\n";
    echo "    \$role = \$_SESSION['user_role'] ?? '';\n";
    echo "    return strpos(\$role, 'wali_kelas_') === 0;\n";
    echo "}\n\n";
    echo "public static function getWaliKelasClass() {\n";
    echo "    \$role = \$_SESSION['user_role'] ?? '';\n";
    echo "    if (strpos(\$role, 'wali_kelas_') === 0) {\n";
    echo "        \$className = str_replace('wali_kelas_', '', \$role);\n";
    echo "        return strtoupper(str_replace('_', '-', \$className));\n";
    echo "    }\n";
    echo "    return null;\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
    // Step 6: Verifikasi hasil
    echo "<h2>✅ Step 6: Verifikasi Hasil</h2>";
    
    // Count users by role
    $stmt = $pdo->query("
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE role LIKE 'wali_kelas_%' 
        GROUP BY role 
        ORDER BY role
    ");
    $roleStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>Statistik User Wali Kelas:</h4>";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th>Role</th><th>Jumlah User</th><th>Kelas</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($roleStats as $stat) {
        $className = strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $stat['role']));
        echo "<tr>";
        echo "<td><code>{$stat['role']}</code></td>";
        echo "<td>{$stat['count']}</td>";
        echo "<td>{$className}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Final summary
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎉 IMPLEMENTASI SELESAI!</h3>";
    echo "<p><strong>Yang telah dilakukan:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Enum role diupdate dengan role spesifik per kelas</li>";
    echo "<li>✅ User existing diupdate ke role baru</li>";
    echo "<li>✅ User baru dibuat untuk kelas yang belum ada wali</li>";
    echo "<li>✅ Database relasi sudah benar</li>";
    echo "</ul>";
    
    echo "<p><strong>Langkah selanjutnya:</strong></p>";
    echo "<ol>";
    echo "<li>Update Security helper dengan code di atas</li>";
    echo "<li>Update model queries untuk menggunakan role spesifik</li>";
    echo "<li>Test login dengan user wali kelas baru</li>";
    echo "<li>Verifikasi akses data sudah benar</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Implementasi selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
