<?php
/**
 * Test Direct Modal Submission
 * Simulate form submission langsung ke controller
 */

// Start session
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'admin';

require_once 'app/controllers/CatatanController.php';
require_once 'app/helpers/Security.php';

echo "<h2>🎯 Test Direct Modal Submission</h2>";

// Generate CSRF token
$csrf_token = Security::generateCSRFToken();

// Simulate POST data seperti dari modal
$_POST = [
    'csrf_token' => $csrf_token,
    'siswa_id' => '1',
    'jenis_catatan' => 'pamong_mp',
    'judul_catatan' => 'Test Modal Direct - ' . date('H:i:s'),
    'isi_catatan' => 'Ini adalah test catatan langsung dari modal yang sudah diperbaiki. Test dilakukan pada ' . date('Y-m-d H:i:s'),
    'tanggal_catatan' => date('Y-m-d'),
    'tingkat_prioritas' => 'sedang',
    'status_catatan' => 'aktif',
    'tindak_lanjut' => 'Follow up dalam 1 minggu',
    'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+7 days'))
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h3>📝 Simulating Form Data:</h3>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

try {
    echo "<h3>🚀 Testing Controller...</h3>";
    
    // Create controller instance
    $controller = new CatatanController();
    
    // Capture output
    ob_start();
    
    // Call create method
    $controller->create();
    
    // Get output
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✅ Controller method executed successfully</p>";
    
    // Check session messages
    if (isset($_SESSION['success'])) {
        echo "<p style='color: green;'>✅ Success Message: " . $_SESSION['success'] . "</p>";
        unset($_SESSION['success']);
    }
    
    if (isset($_SESSION['error'])) {
        echo "<p style='color: red;'>❌ Error Message: " . $_SESSION['error'] . "</p>";
        unset($_SESSION['error']);
    }
    
    echo "<h3>📊 Verification:</h3>";
    
    // Verify catatan was created
    require_once 'app/models/CatatanSiswa.php';
    $catatanModel = new CatatanSiswa();
    $allCatatan = $catatanModel->getBySiswaId(1);
    
    echo "<p><strong>Total catatan for siswa ID 1:</strong> " . count($allCatatan) . "</p>";
    
    // Show latest catatan
    if (!empty($allCatatan)) {
        $latest = $allCatatan[0]; // Should be sorted by date DESC
        echo "<h4>Latest Catatan:</h4>";
        echo "<div class='border p-3 rounded' style='background: #f8f9fa;'>";
        echo "<p><strong>ID:</strong> {$latest['id']}</p>";
        echo "<p><strong>Kategori:</strong> {$latest['nama_kategori']}</p>";
        echo "<p><strong>Judul:</strong> {$latest['judul_catatan']}</p>";
        echo "<p><strong>Isi:</strong> " . substr($latest['isi_catatan'], 0, 100) . "...</p>";
        echo "<p><strong>Tanggal:</strong> {$latest['tanggal_catatan']}</p>";
        echo "<p><strong>Prioritas:</strong> {$latest['tingkat_prioritas']}</p>";
        echo "<p><strong>Status:</strong> {$latest['status_catatan']}</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links:</h3>";
echo "<p><a href='public/siswa/detail/1' class='btn btn-primary'>👤 Lihat Detail Siswa</a></p>";
echo "<p><a href='test_modal_catatan.php' class='btn btn-info'>🧪 Test Modal Form</a></p>";
echo "<p><a href='test_tambah_catatan.php' class='btn btn-success'>🔧 Test Functionality</a></p>";

// Test form submission via browser
echo "<hr>";
echo "<h3>🌐 Test via Browser Form:</h3>";
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-info">
            <h5><i class="bi bi-info-circle me-2"></i>Test Form Submission ke Controller</h5>
            <p>Form ini akan mengirim data langsung ke <code>/siswa-app/public/catatan/create</code></p>
        </div>
        
        <form action="/siswa-app/public/catatan/create" method="POST" class="border p-4 rounded">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            <input type="hidden" name="siswa_id" value="1">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-tag me-1"></i>Jenis Catatan
                        </label>
                        <select name="jenis_catatan" class="form-select" required>
                            <option value="">Pilih Jenis</option>
                            <option value="pamong_mp" selected>Pamong MP</option>
                            <option value="pamong_mt">Pamong MT</option>
                            <option value="wali_x">Wali Kelas X</option>
                            <option value="bk_konseling">BK Konseling</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-calendar me-1"></i>Tanggal
                        </label>
                        <input type="date" name="tanggal_catatan" class="form-control" value="<?= date('Y-m-d') ?>" required>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-pencil me-1"></i>Judul Catatan
                </label>
                <input type="text" name="judul_catatan" class="form-control" value="Test Browser Form - <?= date('H:i:s') ?>" required>
            </div>
            
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-journal-text me-1"></i>Isi Catatan
                </label>
                <textarea name="isi_catatan" class="form-control" rows="4" required>Test catatan melalui browser form. Submitted pada <?= date('Y-m-d H:i:s') ?>. 

Ini adalah test untuk memastikan:
1. Form dapat submit dengan benar
2. Data tersimpan ke database
3. Redirect berfungsi dengan baik
4. Session message ditampilkan</textarea>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-exclamation-circle me-1"></i>Prioritas
                        </label>
                        <select name="tingkat_prioritas" class="form-select">
                            <option value="rendah">Rendah</option>
                            <option value="sedang" selected>Sedang</option>
                            <option value="tinggi">Tinggi</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-check-circle me-1"></i>Status
                        </label>
                        <select name="status_catatan" class="form-select">
                            <option value="draft">Draft</option>
                            <option value="aktif" selected>Aktif</option>
                            <option value="selesai">Selesai</option>
                            <option value="ditunda">Ditunda</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-arrow-right-circle me-1"></i>Tindak Lanjut (Opsional)
                </label>
                <textarea name="tindak_lanjut" class="form-control" rows="2">Monitoring progress dalam 1 minggu ke depan</textarea>
            </div>
            
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-calendar-check me-1"></i>Tanggal Tindak Lanjut
                </label>
                <input type="date" name="tanggal_tindak_lanjut" class="form-control" value="<?= date('Y-m-d', strtotime('+7 days')) ?>">
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-send me-2"></i>
                    Submit Test Form
                </button>
            </div>
        </form>
        
        <div class="mt-4">
            <h5>Expected Result:</h5>
            <ul class="list-group">
                <li class="list-group-item">✅ Form akan redirect ke <code>/siswa-app/public/siswa/detail/1</code></li>
                <li class="list-group-item">✅ Success message akan muncul</li>
                <li class="list-group-item">✅ Catatan baru akan tampil di tab "Catatan Siswa"</li>
                <li class="list-group-item">✅ Counter catatan akan bertambah</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
