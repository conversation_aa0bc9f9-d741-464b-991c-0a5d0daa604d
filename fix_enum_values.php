<?php
/**
 * Fix ENUM Values
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=siswa_app', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 Fix ENUM Values</h2>";
    
    echo "<h3>Problem Found:</h3>";
    echo "<p style='color: red;'>❌ Field <code>jenis_berkas</code> menggunakan ENUM dengan nilai terbatas:</p>";
    echo "<ul>";
    echo "<li>kartu_keluarga</li>";
    echo "<li>akta_lahir</li>";
    echo "<li>rapor</li>";
    echo "<li>ijazah_sebelumnya</li>";
    echo "<li>foto</li>";
    echo "<li>surat_keterangan_sehat</li>";
    echo "<li>lainnya</li>";
    echo "</ul>";
    
    echo "<h3>Solution 1: Update with existing ENUM values</h3>";
    
    // Update with valid ENUM values
    $updates = [
        16 => 'rapor',  // Rapor Kelas X -> rapor
        17 => 'rapor',  // Rapor Kelas XI -> rapor  
        18 => 'rapor'   // Rapor Kelas XII -> rapor
    ];
    
    foreach ($updates as $id => $jenis) {
        $stmt = $pdo->prepare("UPDATE berkas SET jenis_berkas = ? WHERE id = ?");
        $result = $stmt->execute([$jenis, $id]);
        echo "<p>✅ Updated ID $id to '$jenis' - Affected rows: " . $stmt->rowCount() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>Verification:</h3>";
    
    // Check after update
    $stmt = $pdo->prepare("SELECT id, jenis_berkas, nama_file_sistem FROM berkas WHERE siswa_id = 57 ORDER BY id");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($results as $row) {
        echo "<p><strong>ID " . $row['id'] . ":</strong> ";
        echo "jenis_berkas = '" . ($row['jenis_berkas'] ?: 'NULL/EMPTY') . "' - ";
        echo htmlspecialchars($row['nama_file_sistem']);
        echo "</p>";
    }
    
    echo "<hr>";
    echo "<h3>Solution 2: Modify ENUM to support more values</h3>";
    echo "<p>Untuk mendukung kategori yang lebih spesifik, kita perlu mengubah ENUM:</p>";
    
    $alterSql = "ALTER TABLE berkas MODIFY COLUMN jenis_berkas ENUM(
        'kartu_keluarga',
        'akta_lahir',
        'rapor_kelas_x',
        'rapor_kelas_xi', 
        'rapor_kelas_xii',
        'ijazah_sd',
        'ijazah_smp',
        'ijazah_sma',
        'foto_siswa',
        'surat_keterangan_sehat',
        'surat_kelakuan_baik',
        'surat_peringatan_1',
        'surat_peringatan_2',
        'surat_peringatan_3',
        'surat_panggilan_ortu',
        'piagam_prestasi',
        'sertifikat_lomba',
        'penghargaan_akademik',
        'lainnya'
    ) NOT NULL";
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<code>" . htmlspecialchars($alterSql) . "</code>";
    echo "</div>";
    
    echo "<p><strong>Execute ALTER TABLE?</strong></p>";
    echo "<p style='text-align: center;'>";
    echo "<a href='execute_alter_table.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚠️ Execute ALTER TABLE</a>";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='test_berkas_grouped.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Grouped Display</a>";
echo "</p>";
?>
