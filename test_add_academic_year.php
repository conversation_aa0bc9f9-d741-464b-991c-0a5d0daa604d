<?php
/**
 * Test Script untuk Menambah Tahun Ajaran
 * Script ini akan menguji fitur penambahan tahun ajaran secara programatis
 */

require_once __DIR__ . '/app/models/Database.php';
require_once __DIR__ . '/app/helpers/AcademicYear.php';
require_once __DIR__ . '/app/helpers/Security.php';

echo "<h1>🧪 Test Add Academic Year Functionality</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Current System Status</h2>";
    
    // Check current academic year
    $currentYear = AcademicYear::getCurrentAcademicYear();
    echo "<p style='color: green;'>✅ Current Academic Year: <strong>{$currentYear}</strong></p>";
    
    // Check existing years
    $existingYears = AcademicYear::getAvailableAcademicYears();
    echo "<p style='color: blue;'>ℹ️ Existing Years: " . count($existingYears) . " years</p>";
    foreach ($existingYears as $year) {
        echo "<p style='margin-left: 20px;'>📅 {$year}</p>";
    }
    
    echo "<h2>🧪 Testing Add Academic Year Function</h2>";
    
    // Test years to add
    $testYears = [
        '2019/2020',
        '2018/2019',
        '2026/2027',
        '2027/2028'
    ];
    
    foreach ($testYears as $testYear) {
        echo "<h3>Testing: {$testYear}</h3>";
        
        // Check if year already exists
        if (in_array($testYear, $existingYears)) {
            echo "<p style='color: orange;'>⚠️ Year {$testYear} already exists, skipping...</p>";
            continue;
        }
        
        // Validate format
        if (!AcademicYear::isValidAcademicYear($testYear)) {
            echo "<p style='color: red;'>❌ Invalid format for {$testYear}</p>";
            continue;
        }
        
        try {
            // Simulate the add process from controller
            $db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                'PLACEHOLDER_' . str_replace('/', '_', $testYear),
                0, // Special tingkat for placeholder
                $testYear,
                'System Generated (Test)',
                0,
                false, // Not active
                1 // Assuming user ID 1 exists
            ]);
            
            echo "<p style='color: green;'>✅ Successfully added {$testYear}</p>";
            
            // Verify it was added
            $verification = $db->fetch("
                SELECT * FROM kelas 
                WHERE tahun_pelajaran = ? AND nama_kelas LIKE 'PLACEHOLDER_%'
            ", [$testYear]);
            
            if ($verification) {
                echo "<p style='color: green;'>✅ Verification: {$testYear} found in database</p>";
            } else {
                echo "<p style='color: red;'>❌ Verification failed: {$testYear} not found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add {$testYear}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🔄 Testing Bulk Generation</h2>";
    
    // Test bulk generation
    $startYear = 2015;
    $endYear = 2017;
    
    echo "<p>Testing bulk generation from {$startYear} to {$endYear}...</p>";
    
    $addedYears = [];
    $skippedYears = [];
    
    for ($year = $startYear; $year <= $endYear; $year++) {
        $academicYear = $year . '/' . ($year + 1);
        
        // Check if already exists
        $updatedExistingYears = AcademicYear::getAvailableAcademicYears();
        if (in_array($academicYear, $updatedExistingYears)) {
            $skippedYears[] = $academicYear;
            echo "<p style='color: orange;'>⚠️ Skipped {$academicYear} (already exists)</p>";
            continue;
        }
        
        try {
            // Create placeholder entry
            $db->query("
                INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                'PLACEHOLDER_' . str_replace('/', '_', $academicYear),
                0,
                $academicYear,
                'System Generated (Bulk Test)',
                0,
                false,
                1
            ]);
            
            $addedYears[] = $academicYear;
            echo "<p style='color: green;'>✅ Bulk added: {$academicYear}</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Bulk failed: {$academicYear} - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>Bulk Generation Results:</h3>";
    if (!empty($addedYears)) {
        echo "<p style='color: green;'>✅ Added " . count($addedYears) . " years: " . implode(', ', $addedYears) . "</p>";
    }
    if (!empty($skippedYears)) {
        echo "<p style='color: orange;'>⚠️ Skipped " . count($skippedYears) . " years: " . implode(', ', $skippedYears) . "</p>";
    }
    
    echo "<h2>📊 Final Statistics</h2>";
    
    // Get updated statistics
    $finalYears = AcademicYear::getAvailableAcademicYears();
    echo "<p style='color: green;'>✅ Total Academic Years: <strong>" . count($finalYears) . "</strong></p>";
    
    echo "<h3>All Available Years:</h3>";
    foreach ($finalYears as $year) {
        // Count classes for this year
        $classCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM kelas 
            WHERE tahun_pelajaran = ? AND tingkat > 0
        ", [$year]);
        
        $placeholderCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM kelas 
            WHERE tahun_pelajaran = ? AND tingkat = 0 AND nama_kelas LIKE 'PLACEHOLDER_%'
        ", [$year]);
        
        $isCurrent = AcademicYear::isCurrentAcademicYear($year) ? ' (Current)' : '';
        $isPlaceholder = ($placeholderCount['count'] ?? 0) > 0 ? ' [Placeholder]' : '';
        
        echo "<p style='margin-left: 20px;'>📅 {$year}{$isCurrent}{$isPlaceholder}: " . 
             ($classCount['count'] ?? 0) . " real classes</p>";
    }
    
    echo "<h2>🧪 Testing Delete Functionality</h2>";
    
    // Test delete for empty years
    $testDeleteYear = '2027/2028';
    if (in_array($testDeleteYear, $finalYears)) {
        echo "<p>Testing delete for empty year: {$testDeleteYear}</p>";
        
        // Check if it has data
        $siswaCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM siswa s 
            JOIN kelas k ON s.kelas_id = k.id 
            WHERE k.tahun_pelajaran = ?
        ", [$testDeleteYear]);
        
        $activeClassCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM kelas 
            WHERE tahun_pelajaran = ? AND is_active = 1 AND tingkat > 0
        ", [$testDeleteYear]);
        
        $canDelete = ($siswaCount['count'] ?? 0) == 0 && ($activeClassCount['count'] ?? 0) == 0 && !AcademicYear::isCurrentAcademicYear($testDeleteYear);
        
        if ($canDelete) {
            try {
                // Delete placeholder entries
                $result = $db->query("
                    DELETE FROM kelas 
                    WHERE tahun_pelajaran = ? 
                    AND nama_kelas LIKE 'PLACEHOLDER_%' 
                    AND tingkat = 0 
                    AND is_active = 0
                ", [$testDeleteYear]);
                
                echo "<p style='color: green;'>✅ Successfully deleted {$testDeleteYear}</p>";
                
                // Verify deletion
                $verification = $db->fetch("
                    SELECT COUNT(*) as count 
                    FROM kelas 
                    WHERE tahun_pelajaran = ?
                ", [$testDeleteYear]);
                
                if (($verification['count'] ?? 0) == 0) {
                    echo "<p style='color: green;'>✅ Verification: {$testDeleteYear} completely removed</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Verification: {$testDeleteYear} still has some entries</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to delete {$testDeleteYear}: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Cannot delete {$testDeleteYear}: has data or is current year</p>";
        }
    }
    
    echo "<h2>🔗 Test Links</h2>";
    echo "<p><a href='/siswa-app/public/academic-year-management' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Open Academic Year Management Page
    </a></p>";
    
    echo "<p><a href='/siswa-app/setup_sample_academic_years.php' target='_blank' style='color: blue; text-decoration: underline;'>
        🔗 Run Sample Data Setup
    </a></p>";
    
    echo "<h2>✅ Test Complete</h2>";
    echo "<p style='color: green; font-weight: bold;'>
        Academic Year add/delete functionality testing completed! 
        All core functions are working properly.
    </p>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Test Summary:</h4>";
    echo "<ul>";
    echo "<li>✅ Manual add functionality tested</li>";
    echo "<li>✅ Bulk generation functionality tested</li>";
    echo "<li>✅ Delete functionality tested</li>";
    echo "<li>✅ Validation and error handling tested</li>";
    echo "<li>✅ Database operations verified</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
