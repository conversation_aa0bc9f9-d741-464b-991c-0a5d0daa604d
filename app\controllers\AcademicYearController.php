<?php
require_once __DIR__ . '/../helpers/AcademicYear.php';
require_once __DIR__ . '/../helpers/Security.php';

class AcademicYearController {
    
    /**
     * Change academic year via AJAX
     */
    public function change() {
        header('Content-Type: application/json');
        
        try {
            // Check if user is authenticated
            Security::requireAuth();
            
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $academicYear = $_POST['academic_year'] ?? '';
            
            if (empty($academicYear)) {
                throw new Exception('Tahun ajaran tidak boleh kosong');
            }
            
            if (!AcademicYear::isValidAcademicYear($academicYear)) {
                throw new Exception('Format tahun ajaran tidak valid');
            }
            
            // Set selected academic year
            if (AcademicYear::setSelectedAcademicYear($academicYear)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Tahun ajaran berhasil diubah ke ' . $academicYear,
                    'academic_year' => $academicYear,
                    'display_text' => AcademicYear::getAcademicYearDisplayText($academicYear)
                ]);
            } else {
                throw new Exception('Gagal mengubah tahun ajaran');
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        
        exit;
    }
    
    /**
     * Get current academic year info
     */
    public function current() {
        header('Content-Type: application/json');
        
        try {
            Security::requireAuth();
            
            $selectedYear = AcademicYear::getSelectedAcademicYear();
            $currentYear = AcademicYear::getCurrentAcademicYear();
            $availableYears = AcademicYear::getAvailableAcademicYears();
            
            echo json_encode([
                'success' => true,
                'selected_year' => $selectedYear,
                'current_year' => $currentYear,
                'available_years' => $availableYears,
                'is_current' => AcademicYear::isCurrentAcademicYear($selectedYear),
                'display_text' => AcademicYear::getAcademicYearDisplayText($selectedYear)
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        
        exit;
    }
    
    /**
     * Reset to current academic year
     */
    public function reset() {
        header('Content-Type: application/json');
        
        try {
            Security::requireAuth();
            
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $currentYear = AcademicYear::getCurrentAcademicYear();
            
            if (AcademicYear::setSelectedAcademicYear($currentYear)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Tahun ajaran direset ke tahun berjalan',
                    'academic_year' => $currentYear,
                    'display_text' => AcademicYear::getAcademicYearDisplayText($currentYear)
                ]);
            } else {
                throw new Exception('Gagal mereset tahun ajaran');
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        
        exit;
    }
}
?>
