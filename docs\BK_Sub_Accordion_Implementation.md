# Implementasi Sub-Accordion untuk Catatan Siswa

## Deskripsi
Implementasi ini menambahkan sub-accordion di dalam accordion utama untuk mengelompokkan catatan berdasarkan jenis:

### **Catatan BK**
- **Konseling** - Catatan konseling dan bimbingan
- **Prestasi** - Catatan prestasi dan penghargaan
- **Pelanggaran** - Catatan pelanggaran dan tindakan disiplin
- **Lainnya** - Catatan BK lainnya

### **Catatan Pamong**
- **Pamong MP** - Catatan <PERSON>ong Masa Persiapan (KPP)
- **Pamong MT** - Catatan Pamong Masa Transisi (X)
- **Pamong MM** - Catatan Pamong Masa Mandiri (XI)
- **Pamong MU** - Catatan Pamong Masa Uji (XII & KPA)

### **Catatan Wali Kelas**
- **Wali KPP** - Catatan Wali Kelas Persiapan Profesi
- **Wali X** - Catatan Wali Kelas X
- **Wali XI** - Catatan Wali Kelas XI
- **Wali XII** - Catatan Wali Kelas XII
- **Wali KPA** - Catatan Wali Kelas Pasca

## Perubahan yang Dibuat

### 1. Model CatatanSiswa.php
**File:** `app/models/CatatanSiswa.php`

Menambahkan method baru untuk mengelompokkan catatan berdasarkan sub-kategori:

#### Method untuk BK:
- `getBKGroupedBySiswaId()` - Mengelompokkan catatan BK

#### Method untuk Pamong:
- `getPamongGroupedBySiswaId()` - Mengelompokkan catatan Pamong

#### Method untuk Wali Kelas:
- `getWaliKelasGroupedBySiswaId()` - Mengelompokkan catatan Wali Kelas

**Contoh implementasi method BK:**
```php
public function getBKGroupedBySiswaId($siswaId) {
    $allCatatan = $this->getBySiswaId($siswaId);
    $bkGrouped = [
        'bk_konseling' => [],
        'bk_prestasi' => [],
        'bk_pelanggaran' => [],
        'bk_lainnya' => []
    ];
    // Logic untuk mengelompokkan...
    return $bkGrouped;
}
```

**Contoh implementasi method Pamong:**
```php
public function getPamongGroupedBySiswaId($siswaId) {
    $allCatatan = $this->getBySiswaId($siswaId);
    $pamongGrouped = [
        'pamong_mp' => [],
        'pamong_mt' => [],
        'pamong_mm' => [],
        'pamong_mu' => []
    ];
    // Logic untuk mengelompokkan...
    return $pamongGrouped;
}
```

**Contoh implementasi method Wali Kelas:**
```php
public function getWaliKelasGroupedBySiswaId($siswaId) {
    $allCatatan = $this->getBySiswaId($siswaId);
    $waliKelasGrouped = [
        'wali_kpp' => [],
        'wali_x' => [],
        'wali_xi' => [],
        'wali_xii' => [],
        'wali_kpa' => []
    ];
    // Logic untuk mengelompokkan...
    return $waliKelasGrouped;
}
```

### 2. Controller SiswaController.php
**File:** `app/controllers/SiswaController.php`

Menambahkan pemanggilan method baru dan menyediakan data untuk view:

```php
// Get catatan for this student
require_once __DIR__ . '/../models/CatatanSiswa.php';
$catatanModel = new CatatanSiswa();
$catatanGrouped = $catatanModel->getGroupedBySiswaId($id);
$catatanBKGrouped = $catatanModel->getBKGroupedBySiswaId($id);
$catatanPamongGrouped = $catatanModel->getPamongGroupedBySiswaId($id); // Baru
$catatanWaliKelasGrouped = $catatanModel->getWaliKelasGroupedBySiswaId($id); // Baru
$catatanCategories = $catatanModel->getCategoriesGrouped();
$catatanStats = $catatanModel->getDetailedStatistics($id);

$data = [
    'title' => 'Detail Siswa - ' . $siswa['nama_lengkap'],
    'siswa' => $siswa,
    'berkas' => $berkas,
    'catatan_grouped' => $catatanGrouped,
    'catatan_bk_grouped' => $catatanBKGrouped,
    'catatan_pamong_grouped' => $catatanPamongGrouped, // Baru
    'catatan_wali_kelas_grouped' => $catatanWaliKelasGrouped, // Baru
    'catatan_categories' => $catatanCategories,
    'catatan_stats' => $catatanStats,
    'csrf_token' => Security::generateCSRFToken()
];
```

### 3. View detail_new.php
**File:** `app/views/siswa/detail_new.php`

Memodifikasi accordion untuk menampilkan sub-accordion khusus untuk kategori BK:

#### Konfigurasi Sub-Kategori:

**BK:**
```php
$bkSubConfig = [
    'bk_konseling' => ['icon' => 'bi-heart', 'color' => 'info', 'label' => 'Konseling'],
    'bk_prestasi' => ['icon' => 'bi-award', 'color' => 'warning', 'label' => 'Prestasi'],
    'bk_pelanggaran' => ['icon' => 'bi-exclamation-triangle', 'color' => 'danger', 'label' => 'Pelanggaran'],
    'bk_lainnya' => ['icon' => 'bi-chat-dots', 'color' => 'secondary', 'label' => 'Lainnya']
];
```

**Pamong:**
```php
$pamongSubConfig = [
    'pamong_mp' => ['icon' => 'bi-person-badge', 'color' => 'info', 'label' => 'Pamong MP (KPP)'],
    'pamong_mt' => ['icon' => 'bi-person-check', 'color' => 'success', 'label' => 'Pamong MT (X)'],
    'pamong_mm' => ['icon' => 'bi-person-gear', 'color' => 'warning', 'label' => 'Pamong MM (XI)'],
    'pamong_mu' => ['icon' => 'bi-person-exclamation', 'color' => 'danger', 'label' => 'Pamong MU (XII & KPA)']
];
```

**Wali Kelas:**
```php
$waliKelasSubConfig = [
    'wali_kpp' => ['icon' => 'bi-mortarboard', 'color' => 'primary', 'label' => 'Wali Kelas KPP'],
    'wali_x' => ['icon' => 'bi-book', 'color' => 'info', 'label' => 'Wali Kelas X'],
    'wali_xi' => ['icon' => 'bi-journal', 'color' => 'success', 'label' => 'Wali Kelas XI'],
    'wali_xii' => ['icon' => 'bi-graduation', 'color' => 'warning', 'label' => 'Wali Kelas XII'],
    'wali_kpa' => ['icon' => 'bi-award', 'color' => 'danger', 'label' => 'Wali Kelas KPA']
];
```

#### Logika Conditional:
- Jika kategori adalah 'bk' dan data `$catatan_bk_grouped` tersedia, tampilkan sub-accordion BK
- Jika kategori adalah 'pamong' dan data `$catatan_pamong_grouped` tersedia, tampilkan sub-accordion Pamong
- Jika kategori adalah 'wali_kelas' dan data `$catatan_wali_kelas_grouped` tersedia, tampilkan sub-accordion Wali Kelas
- Jika bukan kategori yang memiliki sub-accordion, tampilkan accordion normal seperti sebelumnya

#### Styling CSS Tambahan:
```css
/* Sub-accordion styling for BK, Pamong, and Wali Kelas */
#bkSubAccordion .accordion-item,
#pamongSubAccordion .accordion-item,
#waliKelasSubAccordion .accordion-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

#bkSubAccordion .accordion-button,
#pamongSubAccordion .accordion-button,
#waliKelasSubAccordion .accordion-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    background-color: #f8f9fa;
}

#bkSubAccordion .accordion-button:not(.collapsed),
#pamongSubAccordion .accordion-button:not(.collapsed),
#waliKelasSubAccordion .accordion-button:not(.collapsed) {
    background-color: #e7f3ff;
    border-color: #b6d7ff;
}

#bkSubAccordion .accordion-body,
#pamongSubAccordion .accordion-body,
#waliKelasSubAccordion .accordion-body {
    padding: 1rem;
    background-color: #fdfdfd;
}
```

## Fitur yang Ditambahkan

### 1. Organisasi yang Lebih Baik
- Catatan BK, Pamong, dan Wali Kelas sekarang dikelompokkan berdasarkan jenis
- Setiap sub-kategori memiliki icon dan warna yang berbeda
- Badge menunjukkan jumlah catatan di setiap sub-kategori

### 2. Visual Hierarchy
- Accordion utama untuk kategori besar (Pamong, Wali Kelas, BK)
- Sub-accordion untuk setiap kategori yang lebih spesifik:
  - BK: Konseling, Prestasi, Pelanggaran, Lainnya
  - Pamong: MP, MT, MM, MU
  - Wali Kelas: KPP, X, XI, XII, KPA
- Styling yang konsisten dengan desain yang ada

### 3. Backward Compatibility
- Kategori yang tidak memiliki sub-accordion tetap menggunakan tampilan accordion normal
- Tidak ada perubahan pada fungsionalitas yang sudah ada
- Data tetap kompatibel dengan struktur database yang ada

## Cara Penggunaan

1. Buka halaman detail siswa
2. Klik tab "Catatan Siswa"
3. Klik accordion yang diinginkan:
   - **Catatan Pamong** - akan menampilkan sub-accordion MP, MT, MM, MU
   - **Catatan Wali Kelas** - akan menampilkan sub-accordion KPP, X, XI, XII, KPA
   - **Catatan BK** - akan menampilkan sub-accordion Konseling, Prestasi, Pelanggaran, Lainnya
4. Klik sub-accordion yang diinginkan untuk melihat catatan spesifik

## Manfaat

1. **Organisasi yang Lebih Baik**: Semua catatan tidak lagi tercampur dalam satu tempat
2. **Navigasi yang Mudah**: User dapat langsung ke jenis catatan yang dicari
3. **Visual yang Jelas**: Setiap kategori memiliki icon dan warna yang berbeda
4. **Skalabilitas**: Mudah menambah sub-kategori baru jika diperlukan
5. **Konsistensi**: Mengikuti pola desain yang sudah ada di aplikasi
6. **Efisiensi**: Mengurangi scrolling dan mempercepat pencarian catatan

## Testing

Untuk menguji implementasi ini:
1. Jalankan script test: `test_bk_sub_accordion.php` dan `test_pamong_wali_sub_accordion.php`
2. Pastikan ada data catatan dengan jenis yang berbeda-beda
3. Buka halaman detail siswa
4. Verifikasi bahwa sub-accordion muncul dengan benar untuk semua kategori
5. Pastikan catatan ditampilkan di sub-kategori yang tepat
6. Test fungsionalitas edit dan delete masih berfungsi normal
7. Verifikasi bahwa badge menampilkan jumlah catatan yang benar
