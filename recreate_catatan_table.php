<?php
/**
 * <PERSON>ript untuk membuat ulang tabel catatan_siswa dengan AUTO_INCREMENT yang benar
 */

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>Membuat Ulang Tabel Catatan Siswa</h2>";
    
    $db = new Database();
    
    // Backup data yang ada
    echo "<p>1. Backup data catatan yang ada...</p>";
    $existingData = $db->fetchAll("SELECT * FROM catatan_siswa");
    echo "<p>Ditemukan " . count($existingData) . " catatan untuk di-backup</p>";
    
    // Drop tabel lama
    echo "<p>2. Menghapus tabel lama...</p>";
    $db->query("DROP TABLE IF EXISTS catatan_siswa");
    echo "<p style='color: green;'>✅ Tabel lama dihapus</p>";
    
    // Buat tabel baru dengan struktur yang benar
    echo "<p>3. Membuat tabel baru...</p>";
    
    $createTableSQL = "
    CREATE TABLE catatan_siswa (
        id INT(11) NOT NULL AUTO_INCREMENT,
        siswa_id INT(11) NOT NULL,
        jenis_catatan ENUM(
            'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
            'wali_kpp', 'wali_x', 'wali_xi', 'wali_xii', 'wali_kpa',
            'bk_konseling', 'bk_pelanggaran', 'bk_prestasi', 'bk_lainnya'
        ) NOT NULL,
        judul_catatan VARCHAR(255) NOT NULL,
        isi_catatan TEXT NOT NULL,
        tanggal_catatan DATE NOT NULL,
        tingkat_prioritas ENUM('rendah', 'sedang', 'tinggi', 'urgent') DEFAULT 'sedang',
        status_catatan ENUM('draft', 'aktif', 'selesai', 'ditunda') DEFAULT 'aktif',
        tindak_lanjut TEXT,
        tanggal_tindak_lanjut DATE,
        created_by INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by INT(11),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        PRIMARY KEY (id),
        INDEX idx_siswa_jenis (siswa_id, jenis_catatan),
        INDEX idx_tanggal (tanggal_catatan),
        INDEX idx_status (status_catatan),
        INDEX idx_prioritas (tingkat_prioritas)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($createTableSQL);
    echo "<p style='color: green;'>✅ Tabel baru berhasil dibuat</p>";
    
    // Restore data yang di-backup (kecuali yang bermasalah)
    if (!empty($existingData)) {
        echo "<p>4. Restore data backup...</p>";
        $restored = 0;
        $skipped = 0;
        
        foreach ($existingData as $data) {
            try {
                // Skip data dengan ID 0 atau yang bermasalah
                if (empty($data['siswa_id']) || empty($data['jenis_catatan'])) {
                    $skipped++;
                    continue;
                }
                
                $sql = "INSERT INTO catatan_siswa (
                    siswa_id, jenis_catatan, judul_catatan, isi_catatan,
                    tanggal_catatan, tingkat_prioritas, status_catatan,
                    tindak_lanjut, tanggal_tindak_lanjut, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $params = [
                    $data['siswa_id'],
                    $data['jenis_catatan'],
                    $data['judul_catatan'],
                    $data['isi_catatan'],
                    $data['tanggal_catatan'],
                    $data['tingkat_prioritas'],
                    $data['status_catatan'],
                    $data['tindak_lanjut'],
                    $data['tanggal_tindak_lanjut'],
                    $data['created_by'],
                    $data['created_at']
                ];
                
                $db->query($sql, $params);
                $restored++;
                
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Skip data: {$e->getMessage()}</p>";
                $skipped++;
            }
        }
        
        echo "<p>Data restored: <strong>{$restored}</strong>, Skipped: <strong>{$skipped}</strong></p>";
    }
    
    // Test insert
    echo "<p>5. Testing insert...</p>";
    
    $siswa = $db->fetch("SELECT id_siswa, nama_lengkap FROM siswa LIMIT 1");
    
    if ($siswa) {
        try {
            $sql = "INSERT INTO catatan_siswa (
                siswa_id, jenis_catatan, judul_catatan, isi_catatan,
                tanggal_catatan, tingkat_prioritas, status_catatan, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $siswa['id_siswa'],
                'bk_prestasi',
                'Test Prestasi Final',
                'Test untuk memastikan tabel baru berfungsi dengan baik',
                date('Y-m-d'),
                'sedang',
                'aktif',
                1
            ];
            
            $result = $db->query($sql, $params);
            $insertId = $db->lastInsertId();
            
            if ($result && $insertId > 0) {
                echo "<p style='color: green;'>✅ Test insert berhasil dengan ID: {$insertId}</p>";
                
                // Hapus data test
                $db->query("DELETE FROM catatan_siswa WHERE id = ?", [$insertId]);
                echo "<p>Test data dihapus</p>";
                
                echo "<h3 style='color: green;'>🎉 Tabel berhasil dibuat ulang!</h3>";
                echo "<p>Sekarang Anda dapat menjalankan script insert_sample_catatan.php</p>";
            } else {
                echo "<p style='color: red;'>❌ Test insert gagal</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error test insert: {$e->getMessage()}</p>";
        }
    }
    
    // Cek struktur final
    echo "<p>6. Struktur tabel final:</p>";
    $structure = $db->fetchAll("DESCRIBE catatan_siswa");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Key</th><th>Extra</th></tr>";
    foreach ($structure as $field) {
        $extraStyle = (strpos($field['Extra'], 'auto_increment') !== false) ? 'color: green; font-weight: bold;' : '';
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td style='{$extraStyle}'>{$field['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
