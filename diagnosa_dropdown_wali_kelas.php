<?php
/**
 * Script Diagnosa Dropdown Wali Kelas Kosong
 * 
 * Menganalisis mengapa dropdown wali kelas kosong di form tambah kelas
 */

// Include database connection
require_once __DIR__ . '/app/config/Database.php';
require_once __DIR__ . '/app/models/User.php';

echo "<h1>🔍 Diagnosa Dropdown Wali Kelas</h1>";

try {
    $db = new Database();
    $pdo = $db->getConnection();
    $userModel = new User();
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Masalah yang Dilaporkan</h3>";
    echo "<ul>";
    echo "<li>Form tambah kelas: dropdown wali kelas tidak ada pilihannya</li>";
    echo "<li>Manajemen user: belum ada informasi kelas yang dikelola</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 1: Cek user dengan role wali kelas
    echo "<h2>👥 Test 1: User dengan Role Wali Kelas</h2>";
    
    $stmt = $pdo->query("
        SELECT id, username, nama_lengkap, role, email, is_active
        FROM users 
        WHERE role LIKE 'wali_kelas%'
        ORDER BY role, nama_lengkap
    ");
    $waliKelasUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total user wali kelas:</strong> " . count($waliKelasUsers) . "</p>";
    
    if (!empty($waliKelasUsers)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasUsers as $user) {
            $statusColor = $user['is_active'] ? 'green' : 'red';
            $statusText = $user['is_active'] ? 'Aktif' : 'Tidak Aktif';
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><code>{$user['role']}</code></td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Tidak ada user dengan role wali kelas ditemukan!</p>";
        echo "<p>Ini adalah penyebab dropdown kosong.</p>";
    }
    
    // Test 2: Test method getUsersByRole()
    echo "<h2>🔧 Test 2: Method getUsersByRole()</h2>";
    
    $waliKelasList = $userModel->getUsersByRole('wali_kelas');
    
    echo "<p><strong>Hasil getUsersByRole('wali_kelas'):</strong> " . count($waliKelasList) . " user</p>";
    
    if (!empty($waliKelasList)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($waliKelasList as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['nama_lengkap']}</td>";
            echo "<td><code>{$user['role']}</code></td>";
            echo "<td style='color: green;'>✅ Ditemukan</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: red;'>❌ Method getUsersByRole() tidak mengembalikan data!</p>";
    }
    
    // Test 3: Cek controller KelasController
    echo "<h2>🎮 Test 3: Controller KelasController</h2>";
    
    echo "<p>Mengecek apakah controller memanggil getUsersByRole() dengan benar...</p>";
    
    // Simulasi controller logic
    require_once __DIR__ . '/app/controllers/KelasController.php';
    
    // Cek apakah ada method create di controller
    if (class_exists('KelasController')) {
        $reflection = new ReflectionClass('KelasController');
        $methods = $reflection->getMethods();
        
        echo "<p><strong>Methods di KelasController:</strong></p>";
        echo "<ul>";
        foreach ($methods as $method) {
            if ($method->isPublic()) {
                echo "<li><code>{$method->getName()}</code></li>";
            }
        }
        echo "</ul>";
        
        if ($reflection->hasMethod('create')) {
            echo "<p style='color: green;'>✅ Method create() ada di KelasController</p>";
        } else {
            echo "<p style='color: red;'>❌ Method create() tidak ada di KelasController</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ KelasController tidak ditemukan</p>";
    }
    
    // Test 4: Simulasi data untuk dropdown
    echo "<h2>📝 Test 4: Simulasi Dropdown</h2>";
    
    if (!empty($waliKelasList)) {
        echo "<h4>Simulasi dropdown yang seharusnya muncul:</h4>";
        echo "<select class='form-select' style='width: 100%; padding: 8px; margin: 10px 0;'>";
        echo "<option value=''>Pilih Wali Kelas</option>";
        
        foreach ($waliKelasList as $wali) {
            $roleInfo = '';
            if (strpos($wali['role'], 'wali_kelas_') === 0) {
                $roleInfo = ' - ' . strtoupper(str_replace(['wali_kelas_', '_'], ['', '-'], $wali['role']));
            }
            
            echo "<option value='{$wali['id']}'>";
            echo htmlspecialchars($wali['nama_lengkap']) . " (" . htmlspecialchars($wali['username']) . ")" . $roleInfo;
            echo "</option>";
        }
        echo "</select>";
        
        echo "<p style='color: green;'>✅ Dropdown seharusnya menampilkan " . count($waliKelasList) . " pilihan</p>";
    } else {
        echo "<p style='color: red;'>❌ Dropdown akan kosong karena tidak ada data wali kelas</p>";
    }
    
    // Test 5: Solusi yang diperlukan
    echo "<h2>💡 Test 5: Solusi yang Diperlukan</h2>";
    
    $solutions = [];
    
    if (empty($waliKelasUsers)) {
        $solutions[] = [
            'masalah' => 'Tidak ada user dengan role wali kelas',
            'solusi' => 'Jalankan script fix_role_kelas_sync.php untuk membuat user wali kelas',
            'prioritas' => 'Tinggi'
        ];
    }
    
    if (empty($waliKelasList) && !empty($waliKelasUsers)) {
        $solutions[] = [
            'masalah' => 'Method getUsersByRole() tidak berfungsi',
            'solusi' => 'Periksa dan perbaiki method getUsersByRole() di User model',
            'prioritas' => 'Tinggi'
        ];
    }
    
    // Cek apakah controller memanggil getUsersByRole
    $controllerFile = __DIR__ . '/app/controllers/KelasController.php';
    if (file_exists($controllerFile)) {
        $controllerContent = file_get_contents($controllerFile);
        if (strpos($controllerContent, 'getUsersByRole') === false) {
            $solutions[] = [
                'masalah' => 'Controller tidak memanggil getUsersByRole()',
                'solusi' => 'Update KelasController untuk memanggil getUsersByRole("wali_kelas")',
                'prioritas' => 'Tinggi'
            ];
        }
    }
    
    if (!empty($solutions)) {
        echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr><th>Masalah</th><th>Solusi</th><th>Prioritas</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($solutions as $solution) {
            $prioritasColor = $solution['prioritas'] === 'Tinggi' ? 'red' : 'orange';
            
            echo "<tr>";
            echo "<td>{$solution['masalah']}</td>";
            echo "<td>{$solution['solusi']}</td>";
            echo "<td style='color: {$prioritasColor}; font-weight: bold;'>{$solution['prioritas']}</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p style='color: green;'>✅ Tidak ada masalah yang ditemukan</p>";
    }
    
    // Test 6: Quick fix
    echo "<h2>🚀 Test 6: Quick Fix</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 Langkah Perbaikan Cepat:</h4>";
    echo "<ol>";
    echo "<li><strong>Jalankan script perbaikan:</strong> <a href='fix_role_kelas_sync.php' target='_blank'>fix_role_kelas_sync.php</a></li>";
    echo "<li><strong>Verifikasi user wali kelas:</strong> Pastikan ada user dengan role wali_kelas_*</li>";
    echo "<li><strong>Test dropdown:</strong> Refresh halaman tambah kelas</li>";
    echo "<li><strong>Update manajemen user:</strong> Tambah kolom kelas yang dikelola</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔗 Link untuk Test:</h4>";
    echo "<ul>";
    echo "<li><a href='/siswa-app/public/kelas/create' target='_blank'>Form Tambah Kelas</a></li>";
    echo "<li><a href='/siswa-app/public/admin/users' target='_blank'>Manajemen User</a></li>";
    echo "<li><a href='fix_role_kelas_sync.php' target='_blank'>Script Perbaikan</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Pesan Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Diagnosa selesai pada " . date('Y-m-d H:i:s') . "</em></p>";
?>
