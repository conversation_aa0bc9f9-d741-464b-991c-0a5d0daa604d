<?php
/**
 * Add Rapor KPP and KPA to ENUM
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=siswa_app', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>📊 Add Rapor KPP and KPA</h2>";
    
    echo "<h3>Current ENUM Values:</h3>";
    
    // Check current ENUM values
    $stmt = $pdo->prepare("SHOW COLUMNS FROM berkas LIKE 'jenis_berkas'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Current Type:</strong> " . htmlspecialchars($result['Type']);
    echo "</div>";
    
    echo "<h3>Adding Rapor KPP and KPA...</h3>";
    
    $alterSql = "ALTER TABLE berkas MODIFY COLUMN jenis_berkas ENUM(
        'kartu_keluarga',
        'akta_lahir',
        'rapor_kelas_x',
        'rapor_kelas_xi', 
        'rapor_kelas_xii',
        'rapor_kpp',
        'rapor_kpa',
        'ijazah_sd',
        'ijazah_smp',
        'ijazah_sma',
        'foto_siswa',
        'surat_keterangan_sehat',
        'surat_kelakuan_baik',
        'surat_peringatan_1',
        'surat_peringatan_2',
        'surat_peringatan_3',
        'surat_panggilan_ortu',
        'piagam_prestasi',
        'sertifikat_lomba',
        'penghargaan_akademik',
        'lainnya'
    ) NOT NULL";
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<code>" . htmlspecialchars($alterSql) . "</code>";
    echo "</div>";
    
    // Execute the ALTER TABLE
    $stmt = $pdo->prepare($alterSql);
    $result = $stmt->execute();
    
    echo "<p style='color: green;'>✅ ALTER TABLE executed successfully!</p>";
    
    echo "<hr>";
    echo "<h3>Verification:</h3>";
    
    // Check updated ENUM values
    $stmt = $pdo->prepare("SHOW COLUMNS FROM berkas LIKE 'jenis_berkas'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Updated Type:</strong> " . htmlspecialchars($result['Type']);
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>Test Creating Sample Rapor KPP and KPA:</h3>";
    
    // Create sample data for testing
    require_once 'app/models/Berkas.php';
    $berkasModel = new Berkas();
    
    $sampleData = [
        [
            'siswa_id' => 57,
            'jenis_berkas' => 'rapor_kpp',
            'nama_berkas' => 'Rapor KPP',
            'nama_file_asli' => 'rapor_kpp_test.pdf',
            'nama_file_sistem' => 'Rapor_KPP_Siswa_57_' . date('YmdHis') . '_test.pdf',
            'ukuran_file' => 1024000,
            'mime_type' => 'application/pdf',
            'file_path' => 'uploads/berkas/rapor/rapor_kpp_test.pdf',
            'file_hash' => hash('sha256', 'test_kpp_content'),
            'keterangan' => 'Test Rapor KPP',
            'uploaded_by' => 1
        ],
        [
            'siswa_id' => 57,
            'jenis_berkas' => 'rapor_kpa',
            'nama_berkas' => 'Rapor KPA',
            'nama_file_asli' => 'rapor_kpa_test.pdf',
            'nama_file_sistem' => 'Rapor_KPA_Siswa_57_' . date('YmdHis') . '_test.pdf',
            'ukuran_file' => 1024000,
            'mime_type' => 'application/pdf',
            'file_path' => 'uploads/berkas/rapor/rapor_kpa_test.pdf',
            'file_hash' => hash('sha256', 'test_kpa_content'),
            'keterangan' => 'Test Rapor KPA',
            'uploaded_by' => 1
        ]
    ];
    
    foreach ($sampleData as $data) {
        try {
            $berkasId = $berkasModel->create($data);
            echo "<p>✅ Created sample " . $data['nama_berkas'] . " with ID: $berkasId</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error creating " . $data['nama_berkas'] . ": " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Rapor KPP dan KPA Berhasil Ditambahkan!</h4>";
    echo "<p><strong>Kategori Rapor sekarang mencakup:</strong></p>";
    echo "<ul>";
    echo "<li>📊 Rapor Kelas X</li>";
    echo "<li>📊 Rapor Kelas XI</li>";
    echo "<li>📊 Rapor Kelas XII</li>";
    echo "<li>📊 <strong>Rapor KPP</strong> (Baru)</li>";
    echo "<li>📊 <strong>Rapor KPA</strong> (Baru)</li>";
    echo "</ul>";
    echo "<p>Sekarang pengguna dapat mengupload dan mengelompokkan rapor KPP dan KPA.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>📋 Penjelasan KPP dan KPA:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b3d9ff;'>";
echo "<p><strong>KPP (Kompetensi Pengetahuan dan Praktik):</strong></p>";
echo "<ul>";
echo "<li>Rapor yang menilai kompetensi pengetahuan siswa</li>";
echo "<li>Biasanya berisi nilai-nilai mata pelajaran akademik</li>";
echo "<li>Fokus pada aspek kognitif dan praktik</li>";
echo "</ul>";

echo "<p><strong>KPA (Kompetensi Pengetahuan dan Aplikasi):</strong></p>";
echo "<ul>";
echo "<li>Rapor yang menilai kemampuan aplikasi pengetahuan</li>";
echo "<li>Berisi penilaian penerapan ilmu dalam praktik</li>";
echo "<li>Fokus pada aspek aplikatif dan implementasi</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center;'>";
echo "<a href='test_berkas_grouped.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Test Grouped Display</a> ";
echo "<a href='public/siswa/detail/57' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👁️ Detail Siswa</a> ";
echo "<a href='public/upload/berkas/57' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📤 Test Upload</a>";
echo "</p>";
?>
