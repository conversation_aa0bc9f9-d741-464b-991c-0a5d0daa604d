<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Fitur Tambah Siswa di Halaman Data Kelas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
        }
        .before-after {
            display: flex;
            gap: 20px;
        }
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background-color: #f8f9fa;
            border: 2px solid #dc3545;
        }
        .after {
            background-color: #f8f9fa;
            border: 2px solid #28a745;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ffeaa7;
        }
        .new-feature {
            background-color: #d1ecf1;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🎯 Demo Fitur Tambah Siswa di Halaman Data Kelas</h1>
        
        <!-- Header Improvement -->
        <div class="demo-section">
            <h2><i class="bi bi-1-circle-fill text-primary"></i> Peningkatan Header Halaman</h2>
            <div class="before-after">
                <div class="before">
                    <h5 class="text-danger">❌ Sebelum</h5>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="card-title mb-0">
                                    <i class="bi bi-building"></i> Daftar Kelas
                                </h4>
                                <small class="text-muted"><strong>2025/2026 (Tahun Berjalan)</strong></small>
                            </div>
                            <a href="#" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Tambah Kelas
                            </a>
                        </div>
                    </div>
                    <p class="mt-2"><small>Hanya ada tombol "Tambah Kelas"</small></p>
                </div>
                
                <div class="after">
                    <h5 class="text-success">✅ Sesudah</h5>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="card-title mb-0">
                                    <i class="bi bi-building"></i> Daftar Kelas
                                </h4>
                                <small class="text-muted"><strong>2025/2026 (Tahun Berjalan)</strong></small>
                            </div>
                            <div class="btn-group">
                                <a href="#" class="btn btn-success new-feature">
                                    <i class="bi bi-person-plus"></i> Tambah Siswa
                                </a>
                                <a href="#" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Tambah Kelas
                                </a>
                            </div>
                        </div>
                    </div>
                    <p class="mt-2"><small>Sekarang ada tombol "Tambah Siswa" (hijau) dan "Tambah Kelas"</small></p>
                </div>
            </div>
        </div>

        <!-- Table Row Improvement -->
        <div class="demo-section">
            <h2><i class="bi bi-2-circle-fill text-primary"></i> Peningkatan Baris Tabel Kelas</h2>
            <div class="before-after">
                <div class="before">
                    <h5 class="text-danger">❌ Sebelum</h5>
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Nama Kelas</th>
                                <th>Tingkat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>X-IPA-1</strong></td>
                                <td><span class="badge bg-success">Kelas X</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-warning" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-info" title="Detail">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-danger" title="Hapus">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p><small>Hanya ada tombol Edit, Detail, dan Hapus</small></p>
                </div>
                
                <div class="after">
                    <h5 class="text-success">✅ Sesudah</h5>
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Nama Kelas</th>
                                <th>Tingkat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>X-IPA-1</strong></td>
                                <td><span class="badge bg-success">Kelas X</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-success new-feature" title="Tambah Siswa">
                                            <i class="bi bi-person-plus"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-warning" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-info" title="Detail">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-danger" title="Hapus">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p><small>Sekarang ada tombol "Tambah Siswa" (hijau) di awal grup tombol</small></p>
                </div>
            </div>
        </div>

        <!-- Form Enhancement -->
        <div class="demo-section">
            <h2><i class="bi bi-3-circle-fill text-primary"></i> Peningkatan Form Tambah Siswa</h2>
            <div class="before-after">
                <div class="before">
                    <h5 class="text-danger">❌ Sebelum</h5>
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="bi bi-person-plus"></i> Tambah Siswa Baru</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select class="form-select">
                                    <option value="">Pilih Kelas</option>
                                    <option value="1">X-IPA-1 - 2025/2026</option>
                                    <option value="2">X-IPA-2 - 2025/2026</option>
                                    <option value="3">XI-IPA-1 - 2025/2026</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <p><small>User harus memilih kelas secara manual</small></p>
                </div>
                
                <div class="after">
                    <h5 class="text-success">✅ Sesudah</h5>
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="bi bi-person-plus"></i> Tambah Siswa Baru</h4>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info alert-dismissible fade show new-feature" role="alert">
                                <i class="bi bi-info-circle"></i> 
                                Anda akan menambahkan siswa ke kelas: <strong>X-IPA-1 - 2025/2026</strong>
                                <button type="button" class="btn-close"></button>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select class="form-select">
                                    <option value="">Pilih Kelas</option>
                                    <option value="1" selected class="highlight">X-IPA-1 - 2025/2026</option>
                                    <option value="2">X-IPA-2 - 2025/2026</option>
                                    <option value="3">XI-IPA-1 - 2025/2026</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <p><small>Kelas otomatis terpilih + notifikasi informatif</small></p>
                </div>
            </div>
        </div>

        <!-- Workflow -->
        <div class="demo-section">
            <h2><i class="bi bi-4-circle-fill text-primary"></i> Alur Kerja (Workflow)</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-info">🔄 Metode 1: Tambah Siswa Umum</h5>
                    <ol class="list-group list-group-numbered">
                        <li class="list-group-item">Buka halaman Data Kelas</li>
                        <li class="list-group-item">Klik tombol <span class="badge bg-success">Tambah Siswa</span> di header</li>
                        <li class="list-group-item">Pilih kelas secara manual di form</li>
                        <li class="list-group-item">Isi data siswa dan submit</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h5 class="text-success">⚡ Metode 2: Tambah Siswa Spesifik</h5>
                    <ol class="list-group list-group-numbered">
                        <li class="list-group-item">Buka halaman Data Kelas</li>
                        <li class="list-group-item">Klik tombol <span class="badge bg-success"><i class="bi bi-person-plus"></i></span> di baris kelas yang diinginkan</li>
                        <li class="list-group-item new-feature">Form terbuka dengan kelas sudah terpilih</li>
                        <li class="list-group-item">Isi data siswa dan submit</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Benefits -->
        <div class="demo-section">
            <h2><i class="bi bi-star-fill text-warning"></i> Keuntungan Fitur Ini</h2>
            <div class="row">
                <div class="col-md-3">
                    <div class="card h-100 border-success">
                        <div class="card-body text-center">
                            <i class="bi bi-speedometer2 text-success" style="font-size: 2rem;"></i>
                            <h6 class="card-title mt-2">Efisiensi</h6>
                            <p class="card-text small">Mengurangi langkah manual dalam pemilihan kelas</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 border-info">
                        <div class="card-body text-center">
                            <i class="bi bi-person-check text-info" style="font-size: 2rem;"></i>
                            <h6 class="card-title mt-2">User Experience</h6>
                            <p class="card-text small">Workflow yang lebih intuitif dan user-friendly</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 border-warning">
                        <div class="card-body text-center">
                            <i class="bi bi-shield-check text-warning" style="font-size: 2rem;"></i>
                            <h6 class="card-title mt-2">Konsistensi</h6>
                            <p class="card-text small">Mengurangi kesalahan pemilihan kelas</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 border-primary">
                        <div class="card-body text-center">
                            <i class="bi bi-arrows-move text-primary" style="font-size: 2rem;"></i>
                            <h6 class="card-title mt-2">Fleksibilitas</h6>
                            <p class="card-text small">Mendukung kedua metode: umum dan spesifik</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4 mb-4">
            <p class="text-muted">
                <i class="bi bi-check-circle-fill text-success"></i>
                Fitur "Tambah Siswa di Halaman Data Kelas" telah berhasil diimplementasikan!
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
