<?php
/**
 * Ensure Session Authentication
 * Script ini akan memastikan session authentication berfungsi dengan benar
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔐 Ensure Session Authentication</h1>";

try {
    echo "<h2>📋 Current Session Status</h2>";
    
    // Display current session
    echo "<h3>Session Data:</h3>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    
    // Check if user is authenticated
    if (!isset($_SESSION['user']) || !isset($_SESSION['user_id'])) {
        echo "<p style='color: orange;'>⚠️ User not authenticated, setting up authentication...</p>";
        
        // Set up authentication
        $_SESSION['user'] = [
            'id' => 1,
            'username' => 'admin',
            'role' => 'admin',
            'nama_lengkap' => 'Administrator'
        ];
        $_SESSION['user_id'] = 1;
        $_SESSION['user_role'] = 'admin';
        $_SESSION['authenticated'] = true;
        $_SESSION['login_time'] = time();
        
        echo "<p style='color: green;'>✅ Authentication setup complete</p>";
    } else {
        echo "<p style='color: green;'>✅ User already authenticated</p>";
    }
    
    echo "<h2>🧪 Test Authentication</h2>";
    
    // Test Security::requireAuth()
    require_once __DIR__ . '/app/helpers/Security.php';
    
    try {
        // This should not throw an exception if authentication is working
        Security::requireAuth();
        echo "<p style='color: green;'>✅ Security::requireAuth() passed</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Security::requireAuth() failed: " . $e->getMessage() . "</p>";
    }
    
    // Test Security::isAuthenticated()
    try {
        $isAuth = Security::isAuthenticated();
        if ($isAuth) {
            echo "<p style='color: green;'>✅ Security::isAuthenticated() returns true</p>";
        } else {
            echo "<p style='color: red;'>❌ Security::isAuthenticated() returns false</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Security::isAuthenticated() error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🔗 Test Kelas URLs with Authentication</h2>";
    
    // Get a test class
    require_once __DIR__ . '/app/models/Database.php';
    require_once __DIR__ . '/app/models/Kelas.php';
    
    $kelasModel = new Kelas();
    $testClasses = $kelasModel->getAll('2024/2025');
    
    if (!empty($testClasses)) {
        $testClass = $testClasses[0];
        $classId = $testClass['id_kelas'];
        
        echo "<h3>Test Class: {$testClass['nama_kelas']} (ID: {$classId})</h3>";
        
        $urls = [
            'List Classes' => '/siswa-app/public/kelas',
            'Create Class' => '/siswa-app/public/kelas/create',
            'View Detail' => "/siswa-app/public/kelas/detail/{$classId}",
            'Edit Class' => "/siswa-app/public/kelas/edit/{$classId}",
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Function</th><th>URL</th><th>Test</th>";
        echo "</tr>";
        
        foreach ($urls as $function => $url) {
            echo "<tr>";
            echo "<td><strong>{$function}</strong></td>";
            echo "<td><code>{$url}</code></td>";
            echo "<td><a href='{$url}' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test Now</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>🎯 Quick Test Buttons</h3>";
        echo "<div style='margin: 15px 0;'>";
        echo "<a href='/siswa-app/public/kelas' target='_blank' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>📋 View Class List</a>";
        echo "<a href='/siswa-app/public/kelas/detail/{$classId}' target='_blank' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>👁️ View Detail</a>";
        echo "<a href='/siswa-app/public/kelas/edit/{$classId}' target='_blank' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>✏️ Edit Class</a>";
        echo "<a href='/siswa-app/public/kelas/create' target='_blank' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>➕ Create New</a>";
        echo "</div>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ No classes found for testing</p>";
    }
    
    echo "<h2>🔧 Session Persistence</h2>";
    
    // Save session to ensure it persists
    session_write_close();
    session_start();
    
    echo "<p style='color: green;'>✅ Session saved and restarted</p>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>Session Status:</strong> " . session_status() . " (1=disabled, 2=active)</p>";
    
    echo "<h2>📝 Instructions</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 How to Test:</h4>";
    echo "<ol>";
    echo "<li>Click the test buttons above to verify each function works</li>";
    echo "<li>If any button shows a login page, the authentication is not working</li>";
    echo "<li>If buttons work but show errors, there may be other issues</li>";
    echo "<li>Check browser console (F12) for any JavaScript errors</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🐛 Troubleshooting</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ If authentication issues persist:</h4>";
    echo "<ol>";
    echo "<li><strong>Clear Browser Cache:</strong> Ctrl+Shift+Delete</li>";
    echo "<li><strong>Clear Cookies:</strong> Delete all cookies for localhost</li>";
    echo "<li><strong>Try Incognito Mode:</strong> Test in private browsing</li>";
    echo "<li><strong>Check Session Files:</strong> Ensure session directory is writable</li>";
    echo "<li><strong>Check PHP Settings:</strong> Verify session configuration</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ Summary</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎯 Authentication Status:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Session Started:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Yes' : 'No') . "</li>";
    echo "<li>✅ <strong>User Authenticated:</strong> " . (isset($_SESSION['user']) ? 'Yes' : 'No') . "</li>";
    echo "<li>✅ <strong>User ID Set:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'No') . "</li>";
    echo "<li>✅ <strong>User Role:</strong> " . (isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'Not set') . "</li>";
    echo "</ul>";
    echo "<p><strong>Authentication should now be working for all kelas functions!</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Authentication Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
